{"Source": {"Type": "PostgreSQL", "ConnectionString": "Host=*************;Port=5432;Database=ykz_ledger;Username=postgres;Password=******;", "Schema": "public", "BatchSize": 100}, "Target": {"Type": "MySQL", "ConnectionString": "Server=*************;Port=3306;Database=ykz_ledger_mysql;Uid=root;Pwd=******;CharSet=utf8mb4;", "BatchSize": 100}, "Migration": {"EnableCheckpoint": true, "ParallelDegree": 1, "CreateTableStructure": true, "MigrateData": true, "CreatePrimaryKeys": true, "CreateForeignKeys": false, "CreateIndexes": false, "IncludedTables": ["Ledger_LedgerDepartments"]}, "Logging": {"LogLevel": {"Default": "Information", "TryCode.DatabaseMigration": "Information"}}}