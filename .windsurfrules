1.Please communicate with me in Chinese.
2.Code comments should be written in Chinese.
3.If it is C# code, unit tests should preferably use Xunit, and multiple test cases should be generated as much as possible using Theory and InlineData.
4.The generated code must be accurate, so if a certain class is required in the code logic, you need to first read and analyze this class along with its related referenced classes to ensure that the properties or methods used from this class in the logic are accurate.
5.If the code file being analyzed is too long, you need to read it in multiple passes until the entire file is read and analyzed, rather than just reading the first 200 lines.
6.git commit messages should be written in Chinese. output in the format `[EMOJI] [TYPE](file/topic): [description in {locale}]`. Use GitMoji emojis (e.g., ✨ → feat), present tense, active voice, max 120 characters per line, no code blocks.
