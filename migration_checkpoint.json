{"MigrationId": "e1862326-b8ee-410c-b572-2705bbc7eebe", "SourceType": "PostgreSQL", "TargetType": "MySQL", "StartTime": "2025-02-25T11:45:45.687861+08:00", "LastUpdateTime": "2025-02-25T14:19:47.965098+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 4, "TotalRows": 30, "MigratedRows": 30, "TableCheckpoints": {"orders": {"TableName": "orders", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-02-25T11:45:45.913785+08:00", "LastUpdateTime": "2025-02-25T14:21:09.526993+08:00", "Status": "Completed", "ErrorMessage": "Cannot add or update a child row: a foreign key constraint fails (`test_db2`.`#sql-1_b8`, CONSTRAINT `orders_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`))"}, "products": {"TableName": "products", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-02-25T11:45:45.914925+08:00", "LastUpdateTime": "2025-02-25T14:21:07.393698+08:00", "Status": "Completed", "ErrorMessage": "Cannot add or update a child row: a foreign key constraint fails (`test_db2`.`#sql-1_b8`, CONSTRAINT `orders_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`))"}, "users": {"TableName": "users", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-02-25T11:45:45.915115+08:00", "LastUpdateTime": "2025-02-25T14:21:08.431209+08:00", "Status": "Completed", "ErrorMessage": "Cannot add or update a child row: a foreign key constraint fails (`test_db2`.`#sql-1_b8`, CONSTRAINT `orders_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`))"}}, "ErrorMessage": "表 users 迁移失败: Cannot add or update a child row: a foreign key constraint fails (`test_db2`.`#sql-1_b8`, CONSTRAINT `orders_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`))"}