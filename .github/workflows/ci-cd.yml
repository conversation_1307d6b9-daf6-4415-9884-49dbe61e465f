name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '6.0.x'
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal
      env:
        MYSQL_CONNECTION_STRING: "Server=localhost;Database=test_db;User=root;Password=test_password;"
        POSTGRES_CONNECTION_STRING: "Host=localhost;Database=test_db;Username=postgres;Password=test_password;"
      
    - name: Publish
      if: github.event_name != 'pull_request'
      run: dotnet publish src/TryCode.DatabaseMigration.CLI/TryCode.DatabaseMigration.CLI.csproj -c Release -o publish
      
    - name: Upload build artifacts
      if: github.event_name != 'pull_request'
      uses: actions/upload-artifact@v3
      with:
        name: migration-tool
        path: publish/

  publish-nuget:
    name: Publish NuGet Packages
    needs: build-and-test
    if: github.event_name != 'pull_request' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '6.0.x'
    
    - name: Build and Pack Core
      run: dotnet pack src/TryCode.DatabaseMigration.Core/TryCode.DatabaseMigration.Core.csproj -c Release -o packages
    
    - name: Build and Pack MySQL
      run: dotnet pack src/TryCode.DatabaseMigration.MySQL/TryCode.DatabaseMigration.MySQL.csproj -c Release -o packages
    
    - name: Build and Pack PostgreSQL
      run: dotnet pack src/TryCode.DatabaseMigration.PostgreSQL/TryCode.DatabaseMigration.PostgreSQL.csproj -c Release -o packages
    
    - name: Push packages to NuGet
      run: dotnet nuget push packages/*.nupkg --api-key ${{ secrets.NUGET_API_KEY }} --source https://api.nuget.org/v3/index.json
      # Note: You need to add NUGET_API_KEY secret in GitHub repository settings
