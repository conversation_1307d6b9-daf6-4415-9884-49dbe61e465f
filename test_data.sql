-- 创建用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    age INT CHECK (age >= 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入用户测试数据
INSERT INTO users (username, email, age) VALUES
('user1', '<EMAIL>', 25),
('user2', '<EMAIL>', 30),
('user3', '<EMAIL>', 35),
('user4', '<EMAIL>', 40),
('user5', '<EMAIL>', 45),
('user6', '<EMAIL>', 50),
('user7', '<EMAIL>', 55),
('user8', '<EMAIL>', 60),
('user9', '<EMAIL>', 65),
('user10', '<EMAIL>', 70);

-- 创建产品表
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price NUMERIC(10, 2) CHECK (price > 0),
    in_stock BOOLEAN DEFAULT TRUE,
    attributes JSONB
);

-- 插入产品测试数据
INSERT INTO products (name, price, in_stock, attributes) VALUES
('Product A', 19.99, TRUE, '{"color": "red", "size": "M"}'),
('Product B', 29.99, FALSE, '{"color": "blue", "size": "L"}'),
('Product C', 39.99, TRUE, '{"color": "green", "size": "S"}'),
('Product D', 49.99, FALSE, '{"color": "yellow", "size": "XL"}'),
('Product E', 59.99, TRUE, '{"color": "black", "size": "M"}'),
('Product F', 69.99, FALSE, '{"color": "white", "size": "L"}'),
('Product G', 79.99, TRUE, '{"color": "purple", "size": "S"}'),
('Product H', 89.99, FALSE, '{"color": "orange", "size": "XL"}'),
('Product I', 99.99, TRUE, '{"color": "pink", "size": "M"}'),
('Product J', 109.99, FALSE, '{"color": "brown", "size": "L"}');

-- 创建订单表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES users(id),
    order_date DATE DEFAULT CURRENT_DATE,
    total_amount MONEY,
    items TEXT[]
);

-- 插入订单测试数据
INSERT INTO orders (user_id, total_amount, items) VALUES
(1, 19.99, ARRAY['Item1', 'Item2']),
(2, 29.99, ARRAY['Item3', 'Item4']),
(3, 39.99, ARRAY['Item5', 'Item6']),
(4, 49.99, ARRAY['Item7', 'Item8']),
(5, 59.99, ARRAY['Item9', 'Item10']),
(6, 69.99, ARRAY['Item11', 'Item12']),
(7, 79.99, ARRAY['Item13', 'Item14']),
(8, 89.99, ARRAY['Item15', 'Item16']),
(9, 99.99, ARRAY['Item17', 'Item18']),
(10, 109.99, ARRAY['Item19', 'Item20']);

-- 创建评论表
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INT REFERENCES users(id),
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    duration INTERVAL
);

-- 插入评论测试数据
INSERT INTO comments (user_id, content, duration) VALUES
(1, 'Great product!', '1 hour'),
(2, 'Fast delivery', '2 days'),
(3, 'Good quality', '3 weeks'),
(4, 'Excellent service', '1 month'),
(5, 'Highly recommended', '6 months'),
(6, 'Will buy again', '1 year'),
(7, 'Very satisfied', '2 hours'),
(8, 'Impressive', '4 days'),
(9, 'Top notch', '5 weeks'),
(10, 'Best purchase ever', '2 months');

-- 创建日志表
CREATE TABLE logs (
    id SERIAL PRIMARY KEY,
    event BYTEA,
    ip_address CIDR,
    search_vector TSVECTOR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入日志测试数据
INSERT INTO logs (event, ip_address, search_vector) VALUES
(E'\\xDEADBEEF', '***********/32', to_tsvector('Log entry 1')),
(E'\\xCAFEBABE', '********/24', to_tsvector('Log entry 2')),
(E'\\xFEEDFACE', '**********/16', to_tsvector('Log entry 3')),
(E'\\xDEADC0DE', '***********/24', to_tsvector('Log entry 4')),
(E'\\xBADF00D', '********/32', to_tsvector('Log entry 5')),
(E'\\xCAFED00D', '**********/16', to_tsvector('Log entry 6')),
(E'\\xDEADBABE', '***********/24', to_tsvector('Log entry 7')),
(E'\\xFACEB00C', '********/32', to_tsvector('Log entry 8')),
(E'\\xBAADF00D', '**********/16', to_tsvector('Log entry 9')),
(E'\\xDEADFEED', '***********/24', to_tsvector('Log entry 10'));
