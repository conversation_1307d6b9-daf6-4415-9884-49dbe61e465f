using System;
using System.Collections.Generic;

namespace TryCode.DatabaseMigration.Checkpoint.Models
{
    /// <summary>
    /// 迁移断点信息
    /// </summary>
    public class MigrationCheckpoint
    {
        /// <summary>
        /// 迁移ID
        /// </summary>
        public string MigrationId { get; set; }

        /// <summary>
        /// 源数据库类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 目标数据库类型
        /// </summary>
        public string TargetType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 状态（NotStarted、InProgress、Completed、Failed）
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 总表数
        /// </summary>
        public int TotalTables { get; set; }

        /// <summary>
        /// 已完成表数
        /// </summary>
        public int CompletedTables { get; set; }

        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; set; }

        /// <summary>
        /// 已迁移行数
        /// </summary>
        public long MigratedRows { get; set; }

        /// <summary>
        /// 表迁移断点信息
        /// </summary>
        public Dictionary<string, TableCheckpoint> TableCheckpoints { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        public MigrationCheckpoint()
        {
            MigrationId = Guid.NewGuid().ToString();
            TableCheckpoints = new Dictionary<string, TableCheckpoint>();
            Status = "NotStarted";
            StartTime = DateTime.Now;
            LastUpdateTime = DateTime.Now;
        }
    }
}
