using System;

namespace TryCode.DatabaseMigration.Checkpoint.Models
{
    /// <summary>
    /// 表迁移断点信息
    /// </summary>
    public class TableCheckpoint
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string TableName { get; set; }

        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; set; }

        /// <summary>
        /// 已迁移行数
        /// </summary>
        public long MigratedRows { get; set; }

        /// <summary>
        /// 是否已创建表结构
        /// </summary>
        public bool SchemaCreated { get; set; }

        /// <summary>
        /// 是否已创建外键
        /// </summary>
        public bool ForeignKeysCreated { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 状态（NotStarted、InProgress、Completed、Failed）
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否仅创建表结构而不迁移数据
        /// </summary>
        public bool SchemaOnly { get; set; }

        /// <summary>
        /// 是否为分区表
        /// </summary>
        public bool IsPartitioned { get; set; }

        /// <summary>
        /// 分区迁移进度信息
        /// </summary>
        public Dictionary<string, PartitionCheckpoint> PartitionCheckpoints { get; set; } = new Dictionary<string, PartitionCheckpoint>();
    }

    /// <summary>
    /// 分区迁移断点信息
    /// </summary>
    public class PartitionCheckpoint
    {
        /// <summary>
        /// 分区名称
        /// </summary>
        public string PartitionName { get; set; }

        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; set; }

        /// <summary>
        /// 已迁移行数
        /// </summary>
        public long MigratedRows { get; set; }

        /// <summary>
        /// 状态（NotStarted、InProgress、Completed、Failed）
        /// </summary>
        public string Status { get; set; } = "NotStarted";

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
