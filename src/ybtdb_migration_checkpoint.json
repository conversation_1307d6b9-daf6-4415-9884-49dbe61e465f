{"MigrationId": "34ac2273-9ceb-4aa6-bbe3-252f0d2bfc2c", "SourceType": "PostgreSQL", "TargetType": "MySQL", "StartTime": "2025-02-26T10:06:34.256742+08:00", "LastUpdateTime": "2025-02-26T10:07:08.609303+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 2, "TotalRows": 21, "MigratedRows": 21, "TableCheckpoints": {"AbpAuditLogs": {"TableName": "AbpAuditLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": false, "ForeignKeysCreated": false, "StartTime": "2025-02-26T10:06:35.355397+08:00", "LastUpdateTime": "2025-02-26T10:07:09.635357+08:00", "Status": "Failed", "ErrorMessage": "One or more errors occurred. (One or more errors occurred. (<PERSON><PERSON> found when trying to get lock; try restarting transaction))"}, "AbpEntityChanges": {"TableName": "AbpEntityChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": false, "ForeignKeysCreated": false, "StartTime": "2025-02-26T10:06:35.358134+08:00", "LastUpdateTime": "2025-02-26T10:07:09.635982+08:00", "Status": "Failed", "ErrorMessage": "One or more errors occurred. (One or more errors occurred. (<PERSON><PERSON> found when trying to get lock; try restarting transaction))"}, "AbpClaimTypes": {"TableName": "AbpClaimTypes", "TotalRows": 21, "MigratedRows": 21, "SchemaCreated": false, "ForeignKeysCreated": false, "StartTime": "2025-02-26T10:06:35.358396+08:00", "LastUpdateTime": "2025-02-26T10:07:09.635747+08:00", "Status": "Failed", "ErrorMessage": "One or more errors occurred. (One or more errors occurred. (<PERSON><PERSON> found when trying to get lock; try restarting transaction))"}}, "ErrorMessage": "表 AbpEntityChanges 迁移失败: One or more errors occurred. (One or more errors occurred. (Deadlock found when trying to get lock; try restarting transaction))"}