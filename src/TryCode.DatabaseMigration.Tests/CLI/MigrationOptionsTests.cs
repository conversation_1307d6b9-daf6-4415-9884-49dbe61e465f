using System.Collections.Generic;
using TryCode.DatabaseMigration.CLI.Commands;
using TryCode.DatabaseMigration.CLI.Options;
using TryCode.DatabaseMigration.Configuration.Models;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.CLI
{
    /// <summary>
    /// 迁移选项测试
    /// </summary>
    public class MigrationOptionsTests
    {
        /// <summary>
        /// 测试 SchemaOnly 选项是否正确应用到配置
        /// </summary>
        [Theory]
        [InlineData(true, true)]  // SchemaOnly = true 应该设置 SkipDataMigration = true
        [InlineData(false, false)] // SchemaOnly = false 应该设置 SkipDataMigration = false
        public void Should_Apply_SchemaOnly_Option_To_Config(bool schemaOnly, bool expectedSkipDataMigration)
        {
            // Arrange
            var options = new MigrationOptions
            {
                ConfigFile = "test.json",
                SchemaOnly = schemaOnly
            };

            var config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = "postgresql",
                    ConnectionString = "Host=localhost;Database=test;Username=test;Password=test"
                },
                Target = new DatabaseConfig
                {
                    Type = "postgresql",
                    ConnectionString = "Host=localhost;Database=test_target;Username=test;Password=test"
                }
            };

            // Act
            var command = new MigrationCommand(options);
            // 使用反射调用私有方法
            var method = typeof(MigrationCommand).GetMethod("ApplyCommandLineOptions", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method.Invoke(command, new object[] { config });

            // Assert
            Assert.Equal(expectedSkipDataMigration, config.SkipDataMigration);
        }

        /// <summary>
        /// 测试 SchemaOnly 和 SkipSchemaCreation 选项的互斥性
        /// </summary>
        [Theory]
        [InlineData(true, true)]   // 两个选项都设置为 true
        [InlineData(true, false)]  // 只设置 SchemaOnly
        [InlineData(false, true)]  // 只设置 SkipSchemaCreation
        [InlineData(false, false)] // 两个选项都设置为 false
        public void Should_Handle_SchemaOnly_And_SkipSchemaCreation_Options(bool schemaOnly, bool skipSchemaCreation)
        {
            // Arrange
            var options = new MigrationOptions
            {
                ConfigFile = "test.json",
                SchemaOnly = schemaOnly,
                SkipSchemaCreation = skipSchemaCreation
            };

            var config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = "postgresql",
                    ConnectionString = "Host=localhost;Database=test;Username=test;Password=test"
                },
                Target = new DatabaseConfig
                {
                    Type = "postgresql",
                    ConnectionString = "Host=localhost;Database=test_target;Username=test;Password=test"
                }
            };

            // Act
            var command = new MigrationCommand(options);
            var method = typeof(MigrationCommand).GetMethod("ApplyCommandLineOptions", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method.Invoke(command, new object[] { config });

            // Assert
            Assert.Equal(skipSchemaCreation, config.SkipSchemaCreation);
            Assert.Equal(schemaOnly, config.SkipDataMigration);
        }
    }
}
