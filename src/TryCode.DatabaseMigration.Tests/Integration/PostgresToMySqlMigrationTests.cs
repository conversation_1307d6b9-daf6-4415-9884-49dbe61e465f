using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using MySql.Data.MySqlClient;
using Nhgdb;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.MySQL;
using TryCode.DatabaseMigration.PostgreSQL;
using Xunit;
using Xunit.Abstractions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Debug;

namespace TryCode.DatabaseMigration.Tests.Integration
{
    /// <summary>
    /// PostgreSQL到MySQL的数据迁移集成测试
    /// </summary>
    public class PostgresToMySqlMigrationTests : IDisposable
    {
        private readonly string _pgConnectionString;
        private readonly string _mysqlConnectionString;
        private readonly PostgresDataReader _pgReader;
        private readonly MySqlDataWriter _mysqlWriter;
        private readonly ITestOutputHelper _output;
        private readonly string _createTablesSqlPath;
        private readonly string _insertDataSqlPath;
        private readonly ILogger<PostgresDataReader> _pgLogger;
        private readonly ILogger<MySqlDataWriter> _mysqlLogger;

        public PostgresToMySqlMigrationTests(ITestOutputHelper output)
        {
            _output = output;
            _pgConnectionString = DatabaseTestConfig.PostgresConnectionString;
            _mysqlConnectionString = DatabaseTestConfig.MySqlConnectionString;
            
            // 创建日志工厂和日志记录器
            var loggerFactory = new LoggerFactory(new[] { new DebugLoggerProvider() });
            _pgLogger = loggerFactory.CreateLogger<PostgresDataReader>();
            _mysqlLogger = loggerFactory.CreateLogger<MySqlDataWriter>();
            
            _pgReader = new PostgresDataReader(_pgConnectionString, _pgLogger);
            _mysqlWriter = new MySqlDataWriter(_mysqlConnectionString, _mysqlLogger);
            
            // SQL文件路径
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            var projectDir = Directory.GetParent(baseDir).Parent.Parent.Parent.FullName;
            _createTablesSqlPath = Path.Combine(projectDir, "TestData", "create_tables.sql");
            _insertDataSqlPath = Path.Combine(projectDir, "TestData", "insert_test_data.sql");

            // 初始化测试数据库
            SetupTestDatabaseAsync().Wait();
        }

        [Fact]
        public async Task MigrateAllTables_ShouldSucceed()
        {
            // 1. 获取所有表的结构
            var schemas = await _pgReader.GetTableSchemasAsync();
            Assert.NotEmpty(schemas);
            _output.WriteLine($"找到 {schemas.Count()} 个表需要迁移");

            foreach (var schema in schemas)
            {
                _output.WriteLine($"\n开始迁移表: {schema.Name}");
                
                // 2. 在MySQL中创建表
                await _mysqlWriter.CreateTableAsync(schema);
                _output.WriteLine($"表 {schema.Name} 结构创建成功");

                // 3. 获取表的总行数
                var totalRows = await _pgReader.GetTableRowCountAsync(schema.Name);
                _output.WriteLine($"表 {schema.Name} 共有 {totalRows} 行数据");

                // 4. 分批读取和写入数据
                const int batchSize = 1000;
                long offset = 0;

                while (offset < totalRows)
                {
                    var data = await _pgReader.ReadTableDataAsync(schema.Name, batchSize, offset);
                    var rows = data.ToList();
                    if (!rows.Any()) break;

                    await _mysqlWriter.BulkWriteAsync(schema.Name, rows);
                    _output.WriteLine($"已迁移 {schema.Name} 表的第 {offset + 1} 到 {offset + rows.Count} 行数据");
                    
                    offset += rows.Count;
                }

                // 5. 验证数据量
                var mysqlRowCount = await GetMySqlTableRowCount(schema.Name);
                Assert.Equal(totalRows, mysqlRowCount);
                _output.WriteLine($"表 {schema.Name} 验证成功: PostgreSQL {totalRows} 行 = MySQL {mysqlRowCount} 行");
            }
        }

        [Theory]
        [InlineData("departments")]
        [InlineData("employees")]
        [InlineData("projects")]
        public async Task MigrateSpecificTable_ShouldSucceed(string tableName)
        {
            // 1. 获取表结构
            var schemas = await _pgReader.GetTableSchemasAsync();
            var schema = schemas.FirstOrDefault(s => s.Name == tableName);
            Assert.NotNull(schema);

            // 2. 在MySQL中创建表
            await _mysqlWriter.CreateTableAsync(schema);

            // 3. 获取源表数据量
            var totalRows = await _pgReader.GetTableRowCountAsync(tableName);

            // 4. 分批迁移数据
            const int batchSize = 1000;
            long offset = 0;

            while (offset < totalRows)
            {
                var data = await _pgReader.ReadTableDataAsync(tableName, batchSize, offset);
                var rows = data.ToList();
                if (!rows.Any()) break;

                await _mysqlWriter.BulkWriteAsync(tableName, rows);
                offset += rows.Count;
            }

            // 5. 验证数据
            var mysqlRowCount = await GetMySqlTableRowCount(tableName);
            Assert.Equal(totalRows, mysqlRowCount);

            // 6. 验证部分数据内容
            if (tableName == "departments")
            {
                await VerifyDepartmentsData();
            }
            else if (tableName == "employees")
            {
                await VerifyEmployeesData();
            }
            else if (tableName == "projects")
            {
                await VerifyProjectsData();
            }
        }

        private async Task VerifyDepartmentsData()
        {
            using var pgConn = new NhgdbConnection(_pgConnectionString);
            using var mysqlConn = new MySqlConnection(_mysqlConnectionString);

            var pgDept = await pgConn.QueryFirstAsync<dynamic>(
                "SELECT * FROM departments ORDER BY dept_id LIMIT 1");
            var mysqlDept = await mysqlConn.QueryFirstAsync<dynamic>(
                "SELECT * FROM departments ORDER BY dept_id LIMIT 1");

            Assert.Equal(pgDept.dept_name.ToString(), mysqlDept.dept_name.ToString());
            Assert.Equal(
                decimal.Parse(pgDept.budget.ToString()), 
                decimal.Parse(mysqlDept.budget.ToString()));
        }

        private async Task VerifyEmployeesData()
        {
            using var pgConn = new NhgdbConnection(_pgConnectionString);
            using var mysqlConn = new MySqlConnection(_mysqlConnectionString);

            var pgEmp = await pgConn.QueryFirstAsync<dynamic>(
                "SELECT * FROM employees ORDER BY emp_id LIMIT 1");
            var mysqlEmp = await mysqlConn.QueryFirstAsync<dynamic>(
                "SELECT * FROM employees ORDER BY emp_id LIMIT 1");

            Assert.Equal(pgEmp.emp_name.ToString(), mysqlEmp.emp_name.ToString());
            Assert.Equal(pgEmp.email.ToString(), mysqlEmp.email.ToString());
        }

        private async Task VerifyProjectsData()
        {
            using var pgConn = new NhgdbConnection(_pgConnectionString);
            using var mysqlConn = new MySqlConnection(_mysqlConnectionString);

            var pgProj = await pgConn.QueryFirstAsync<dynamic>(
                "SELECT * FROM projects ORDER BY project_id LIMIT 1");
            var mysqlProj = await mysqlConn.QueryFirstAsync<dynamic>(
                "SELECT * FROM projects ORDER BY project_id LIMIT 1");

            Assert.Equal(pgProj.project_name.ToString(), mysqlProj.project_name.ToString());
        }

        private async Task<long> GetMySqlTableRowCount(string tableName)
        {
            using var conn = new MySqlConnection(_mysqlConnectionString);
            return await conn.ExecuteScalarAsync<long>($"SELECT COUNT(*) FROM {tableName}");
        }

        private async Task SetupTestDatabaseAsync()
        {
            try
            {
                // 1. 清理现有表
                await CleanupTestDatabaseAsync();

                // 2. 读取SQL文件内容
                var createTablesSql = await File.ReadAllTextAsync(_createTablesSqlPath);
                var insertDataSql = await File.ReadAllTextAsync(_insertDataSqlPath);

                // 3. 在PostgreSQL中执行SQL，使用事务确保原子性
                await using var conn = new NhgdbConnection(_pgConnectionString);
                await conn.OpenAsync();

                // 3.1 创建扩展（如果需要）
                await conn.ExecuteAsync("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";");

                // 3.2 分别执行每个语句
                var createStatements = createTablesSql
                    .Split(';')
                    .Where(s => !string.IsNullOrWhiteSpace(s))
                    .Select(s => s.Trim());

                foreach (var statement in createStatements)
                {
                    if (!string.IsNullOrWhiteSpace(statement))
                    {
                        try
                        {
                            await conn.ExecuteAsync(statement);
                            _output.WriteLine($"执行成功: {statement.Substring(0, Math.Min(50, statement.Length))}...");
                        }
                        catch (Exception ex)
                        {
                            _output.WriteLine($"执行失败: {statement}");
                            _output.WriteLine($"错误: {ex.Message}");
                            throw;
                        }
                    }
                }

                // 3.3 执行数据插入
                await conn.ExecuteAsync(insertDataSql);

                _output.WriteLine("PostgreSQL测试数据库初始化成功");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"初始化测试数据库失败: {ex.Message}");
                _output.WriteLine(ex.StackTrace);
                throw;
            }
        }

        private async Task CleanupTestDatabaseAsync()
        {
            // 清理PostgreSQL数据库
            using (var conn = new NhgdbConnection(_pgConnectionString))
            {
                await conn.OpenAsync();
                var tables = new[]
                {
                    "binary_data", "json_data", "time_zone_test", "config_settings",
                    "audit_logs", "employee_projects", "salaries", "projects", 
                    "employees", "departments"
                };

                // 禁用外键检查
                await conn.ExecuteAsync("SET session_replication_role = 'replica';");

                foreach (var table in tables)
                {
                    try
                    {
                        await conn.ExecuteAsync($"DROP TABLE IF EXISTS {table} CASCADE");
                        _output.WriteLine($"已清理PostgreSQL表: {table}");
                    }
                    catch (Exception ex)
                    {
                        _output.WriteLine($"清理PostgreSQL表 {table} 失败: {ex.Message}");
                    }
                }

                // 恢复外键检查
                await conn.ExecuteAsync("SET session_replication_role = 'origin';");
            }

            // 清理MySQL数据库
            using (var conn = new MySqlConnection(_mysqlConnectionString))
            {
                await conn.OpenAsync();
                var tables = new[]
                {
                    "binary_data", "json_data", "time_zone_test", "config_settings",
                    "audit_logs", "employee_projects", "salaries", "projects", 
                    "employees", "departments"
                };

                // 禁用外键检查
                await conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 0;");

                foreach (var table in tables)
                {
                    try
                    {
                        await conn.ExecuteAsync($"DROP TABLE IF EXISTS {table}");
                        _output.WriteLine($"已清理MySQL表: {table}");
                    }
                    catch (Exception ex)
                    {
                        _output.WriteLine($"清理MySQL表 {table} 失败: {ex.Message}");
                    }
                }

                // 恢复外键检查
                await conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 1;");
            }
        }

        public void Dispose()
        {
            // 清理测试数据
            CleanupTestDatabaseAsync().Wait();
        }
    }
}
