using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using MySql.Data.MySqlClient;
using Nhgdb;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.MySQL;
using TryCode.DatabaseMigration.PostgreSQL;
using Xunit;
using Xunit.Abstractions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Debug;

namespace TryCode.DatabaseMigration.Tests.Integration
{
    /// <summary>
    /// PostgreSQL到MySQL数据类型迁移测试
    /// </summary>
    public class PostgresToMySqlDataTypeMigrationTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly string _pgConnectionString;
        private readonly string _mysqlConnectionString;
        private readonly PostgresDataReader _pgReader;
        private readonly MySqlDataWriter _mysqlWriter;
        private readonly ILogger<PostgresDataReader> _pgLogger;
        private readonly ILogger<MySqlDataWriter> _mysqlLogger;

        public PostgresToMySqlDataTypeMigrationTests(ITestOutputHelper output)
        {
            _output = output;
            _pgConnectionString = DatabaseTestConfig.PostgresConnectionString;
            _mysqlConnectionString = DatabaseTestConfig.MySqlConnectionString;
            
            // 创建日志工厂和日志记录器
            var loggerFactory = new LoggerFactory(new[] { new DebugLoggerProvider() });
            _pgLogger = loggerFactory.CreateLogger<PostgresDataReader>();
            _mysqlLogger = loggerFactory.CreateLogger<MySqlDataWriter>();
            
            _pgReader = new PostgresDataReader(_pgConnectionString, _pgLogger);
            _mysqlWriter = new MySqlDataWriter(_mysqlConnectionString, _mysqlLogger);
        }

        [Theory]
        [InlineData("numeric_types")]
        [InlineData("text_types")]
        [InlineData("datetime_types")]
        [InlineData("array_types")]
        [InlineData("json_types")]
        [InlineData("special_types")]
        public async Task MigrateTable_WithDifferentDataTypes_ShouldSucceed(string tableName)
        {
            // 1. 准备测试数据
            await SetupTestTableAsync(tableName);

            // 2. 获取源表结构和数据
            var schemas = await _pgReader.GetTableSchemasAsync();
            var schema = schemas.First(s => s.Name == tableName);
            var sourceData = await _pgReader.ReadTableDataAsync(tableName, 1000, 0);
            var rowCount = await _pgReader.GetTableRowCountAsync(tableName);

            // 3. 在MySQL中创建表并迁移数据
            await _mysqlWriter.CreateTableAsync(schema);
            await _mysqlWriter.BulkWriteAsync(tableName, sourceData);

            // 4. 验证数据
            await VerifyMigratedDataAsync(tableName, rowCount);
        }

        private async Task SetupTestTableAsync(string tableName)
        {
            await using var conn = new NhgdbConnection(_pgConnectionString);
            await conn.OpenAsync();

            switch (tableName)
            {
                case "numeric_types":
                    await CreateNumericTypesTableAsync(conn);
                    break;
                case "text_types":
                    await CreateTextTypesTableAsync(conn);
                    break;
                case "datetime_types":
                    await CreateDateTimeTypesTableAsync(conn);
                    break;
                case "array_types":
                    await CreateArrayTypesTableAsync(conn);
                    break;
                case "json_types":
                    await CreateJsonTypesTableAsync(conn);
                    break;
                case "special_types":
                    await CreateSpecialTypesTableAsync(conn);
                    break;
            }
        }

        private async Task CreateNumericTypesTableAsync(NhgdbConnection conn)
        {
            // 创建包含各种数字类型的表
            await conn.ExecuteAsync(@"
                DROP TABLE IF EXISTS numeric_types;
                CREATE TABLE numeric_types (
                    id SERIAL PRIMARY KEY,
                    tiny_int SMALLINT,
                    small_int SMALLINT,
                    int_val INTEGER,
                    big_int BIGINT,
                    decimal_val DECIMAL(10,2),
                    numeric_val NUMERIC(15,4),
                    real_val REAL,
                    double_val DOUBLE PRECISION,
                    money_val MONEY
                )");

            // 插入测试数据
            await conn.ExecuteAsync(@"
                INSERT INTO numeric_types (
                    tiny_int, small_int, int_val, big_int, 
                    decimal_val, numeric_val, real_val, double_val, money_val
                ) VALUES 
                (1, 100, 10000, 1000000, 
                 123.45, 12345.6789, 3.14159, 2.718281828, 1234.56),
                (-1, -100, -10000, -1000000, 
                 -123.45, -12345.6789, -3.14159, -2.718281828, -1234.56),
                (0, 32767, 2147483647, 9223372036854775807,
                 9999.99, 99999.9999, 1e38, 1e308, 999999.99)");
        }

        private async Task CreateTextTypesTableAsync(NhgdbConnection conn)
        {
            // 创建文本类型表
            await conn.ExecuteAsync(@"
                DROP TABLE IF EXISTS text_types;
                CREATE TABLE text_types (
                    id SERIAL PRIMARY KEY,
                    char_val CHAR(1) DEFAULT NULL,
                    char_fixed CHAR(10) DEFAULT NULL,
                    varchar_val VARCHAR(50) DEFAULT NULL,
                    text_val TEXT DEFAULT NULL,
                    name_val NAME DEFAULT NULL
                )");

            // 插入测试数据
            await conn.ExecuteAsync(@"
                INSERT INTO text_types (char_val, char_fixed, varchar_val, text_val, name_val)
                VALUES 
                ('A', 'FIXED', 'Variable length text', 'Long text content', 'Test name'),
                ('B', 'TEST', 'Another text', 'More long content', 'Another name'),
                (NULL, NULL, NULL, NULL, NULL)");
        }

        private async Task CreateDateTimeTypesTableAsync(NhgdbConnection conn)
        {
            // 创建日期时间类型表
            await conn.ExecuteAsync(@"
                DROP TABLE IF EXISTS datetime_types;
                CREATE TABLE datetime_types (
                    id SERIAL PRIMARY KEY,
                    date_val DATE DEFAULT NULL,
                    time_val TIME DEFAULT NULL,
                    timestamp_val TIMESTAMP DEFAULT NULL,
                    timestamptz_val TIMESTAMP WITH TIME ZONE DEFAULT NULL,
                    interval_val VARCHAR(100) DEFAULT NULL
                )");

            // 插入测试数据
            await conn.ExecuteAsync(@"
                INSERT INTO datetime_types (date_val, time_val, timestamp_val, timestamptz_val, interval_val)
                VALUES 
                ('2024-01-01', '12:00:00', '2024-01-01 12:00:00', '2024-01-01 12:00:00+08', '1 day'),
                ('2024-02-01', '13:30:00', '2024-02-01 13:30:00', '2024-02-01 13:30:00+08', '2 hours'),
                (NULL, NULL, NULL, NULL, NULL)");
        }

        private async Task CreateArrayTypesTableAsync(NhgdbConnection conn)
        {
            // 创建包含各种数组类型的表
            await conn.ExecuteAsync(@"
                DROP TABLE IF EXISTS array_types;
                CREATE TABLE array_types (
                    id SERIAL PRIMARY KEY,
                    int_array INTEGER[] DEFAULT NULL,
                    text_array TEXT[] DEFAULT NULL,
                    date_array DATE[] DEFAULT NULL,
                    numeric_array NUMERIC[] DEFAULT NULL
                )");

            // 插入测试数据
            await conn.ExecuteAsync(@"
                INSERT INTO array_types (int_array, text_array, date_array, numeric_array)
                VALUES 
                (ARRAY[1,2,3], ARRAY['a','b','c'], ARRAY['2024-01-01'::date, '2024-01-02'::date], ARRAY[1.1,2.2,3.3]),
                (ARRAY[4,5,6], ARRAY['d','e','f'], ARRAY['2024-02-01'::date, '2024-02-02'::date], ARRAY[4.4,5.5,6.6]),
                (NULL, NULL, NULL, NULL)");
        }

        private async Task CreateJsonTypesTableAsync(NhgdbConnection conn)
        {
            // 创建包含各种JSON类型的表
            await conn.ExecuteAsync(@"
                DROP TABLE IF EXISTS json_types;
                CREATE TABLE json_types (
                    id SERIAL PRIMARY KEY,
                    json_val JSON,
                    jsonb_val JSONB,
                    json_array JSON,
                    jsonb_array JSONB,
                    json_null JSON,
                    complex_json JSONB,
                    nested_json JSONB,
                    json_with_array JSONB
                )");

            // 插入测试数据
            await conn.ExecuteAsync(@"
                INSERT INTO json_types (
                    json_val, jsonb_val, json_array, jsonb_array,
                    json_null, complex_json, nested_json, json_with_array
                ) VALUES 
                ('{""key"": ""value""}', '{""name"": ""test"", ""age"": 30}', '[1,2,3]', '[""a"",""b"",""c""]',
                 NULL, '{""id"": 1, ""data"": {""name"": ""test"", ""values"": [1,2,3]}}',
                 '{""level1"": {""level2"": {""level3"": ""value""}}}',
                 '{""array"": [{""id"": 1, ""name"": ""item1""}, {""id"": 2, ""name"": ""item2""}]}'),
                ('{""number"": 123}', '{""active"": true, ""count"": 100}', '[4,5,6]', '[""d"",""e"",""f""]',
                 NULL, '{""id"": 2, ""data"": {""name"": ""test2"", ""values"": [4,5,6]}}',
                 '{""a"": {""b"": {""c"": {""d"": ""value""}}}}',
                 '{""items"": [{""key"": ""a"", ""value"": 1}, {""key"": ""b"", ""value"": 2}]}'),
                (NULL, NULL, NULL, NULL,
                 NULL, NULL, NULL, NULL)");
        }

        private async Task CreateSpecialTypesTableAsync(NhgdbConnection conn)
        {
            // 创建特殊类型表
            await conn.ExecuteAsync(@"
                DROP TABLE IF EXISTS special_types;
                CREATE TABLE special_types (
                    id SERIAL PRIMARY KEY,
                    uuid_val UUID DEFAULT NULL,
                    inet_val INET DEFAULT NULL,
                    mac_val VARCHAR(17) DEFAULT NULL,
                    bit_val BIT(1) DEFAULT NULL,
                    bit_varying_val VARCHAR(10) DEFAULT NULL
                )");

            // 插入测试数据
            await conn.ExecuteAsync(@"
                INSERT INTO special_types (uuid_val, inet_val, mac_val, bit_val, bit_varying_val)
                VALUES 
                ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '192.168.1.1', '08:00:2b:01:02:03', B'1', B'101'),
                ('123e4567-e89b-12d3-a456-************', '10.0.0.0/8', 'aa:bb:cc:dd:ee:ff', B'0', B'1010'),
                (NULL, NULL, NULL, NULL, NULL)");
        }

        private async Task VerifyMigratedDataAsync(string tableName, long expectedRowCount)
        {
            using var conn = new MySqlConnection(_mysqlConnectionString);
            await conn.OpenAsync();

            // 验证行数
            var actualRowCount = await conn.ExecuteScalarAsync<long>($"SELECT COUNT(*) FROM `{tableName}`");
            Assert.Equal(expectedRowCount, actualRowCount);

            // 获取并输出迁移后的数据
            var data = await conn.QueryAsync($"SELECT * FROM `{tableName}`");
            _output.WriteLine($"\n{tableName} 迁移后的数据:");
            foreach (var row in data)
            {
                _output.WriteLine(System.Text.Json.JsonSerializer.Serialize(row));
            }
        }

        public void Dispose()
        {
            // 清理测试数据
            CleanupTestDatabaseAsync().Wait();
        }

        private async Task CleanupTestDatabaseAsync()
        {
            var tables = new[]
            {
                "numeric_types", "text_types", "datetime_types",
                "array_types", "json_types", "special_types"
            };

            // 清理PostgreSQL数据库
            using (var conn = new NhgdbConnection(_pgConnectionString))
            {
                await conn.OpenAsync();
                foreach (var table in tables)
                {
                    try
                    {
                        await conn.ExecuteAsync($"DROP TABLE IF EXISTS {table} CASCADE");
                        _output.WriteLine($"已清理PostgreSQL表: {table}");
                    }
                    catch (Exception ex)
                    {
                        _output.WriteLine($"清理PostgreSQL表 {table} 失败: {ex.Message}");
                    }
                }
            }

            // 清理MySQL数据库
            using (var conn = new MySqlConnection(_mysqlConnectionString))
            {
                await conn.OpenAsync();
                await conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 0");
                foreach (var table in tables)
                {
                    try
                    {
                        await conn.ExecuteAsync($"DROP TABLE IF EXISTS `{table}`");
                        _output.WriteLine($"已清理MySQL表: {table}");
                    }
                    catch (Exception ex)
                    {
                        _output.WriteLine($"清理MySQL表 {table} 失败: {ex.Message}");
                    }
                }
                await conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 1");
            }
        }
    }
}
