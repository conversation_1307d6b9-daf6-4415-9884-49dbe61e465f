-- 创建测试数据库表结构
-- 作者：Cascade AI
-- 创建时间：2025-02-25
-- 说明：用于测试数据库迁移工具的表结构，包含多种字段类型和主外键关系

-- 1. 部门表（基础表）
CREATE TABLE departments (
    dept_id SERIAL PRIMARY KEY,
    dept_name VARCHAR(50) NOT NULL UNIQUE,
    budget NUMERIC(15,2) CHECK (budget > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 员工表（核心测试表，将包含2万条数据）
CREATE TABLE employees (
    emp_id BIGSERIAL PRIMARY KEY,
    dept_id INT NOT NULL REFERENCES departments(dept_id),
    emp_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    hire_date DATE NOT NULL,
    salary NUMERIC(10,2),
    is_active BOOLEAN DEFAULT true,
    skills JSONB,
    ip_address INET,
    profile TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 薪资表（UUID主键示例）
CREATE TABLE salaries (
    salary_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    emp_id BIGINT NOT NULL REFERENCES employees(emp_id),
    base_salary NUMERIC(10,2) CHECK (base_salary >= 0),
    bonus NUMERIC(10,2),
    tax_rate DECIMAL(5,4),
    payment_date DATE
);

-- 4. 项目表（数组类型示例）
CREATE TABLE projects (
    project_id SERIAL PRIMARY KEY,
    project_name VARCHAR(100) NOT NULL,
    tags VARCHAR(50)[],
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. 员工项目关联表（复合主键示例）
CREATE TABLE employee_projects (
    emp_id BIGINT REFERENCES employees(emp_id),
    project_id INT REFERENCES projects(project_id),
    role VARCHAR(50) NOT NULL,
    join_date DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (emp_id, project_id)
);

-- 6. 审计日志表（JSONB和时间戳示例）
CREATE TABLE audit_logs (
    log_id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_data JSONB,
    new_data JSONB,
    log_time TIMESTAMPTZ DEFAULT NOW()
);

-- 7. 配置表（键值对示例）
CREATE TABLE config_settings (
    setting_key VARCHAR(50) PRIMARY KEY,
    setting_value VARCHAR(255),
    data_type VARCHAR(20) NOT NULL,
    is_encrypted BOOLEAN DEFAULT false,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 时区测试表（时间间隔示例）
CREATE TABLE time_zone_test (
    zone_id SERIAL PRIMARY KEY,
    zone_name VARCHAR(50) NOT NULL,
    utc_offset INTERVAL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. JSON数据表（全文搜索示例）
CREATE TABLE json_data (
    data_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metadata JSONB NOT NULL,
    full_text TSVECTOR,
    tags VARCHAR(50)[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. 二进制数据表（大对象示例）
CREATE TABLE binary_data (
    file_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_name VARCHAR(255) NOT NULL,
    file_data BYTEA,
    file_size BIGINT,
    mime_type VARCHAR(100),
    checksum VARCHAR(64),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_employees_dept_id ON employees(dept_id);
CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_salaries_emp_id ON salaries(emp_id);
CREATE INDEX idx_employee_projects_project_id ON employee_projects(project_id);
CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_json_data_full_text ON json_data USING gin(full_text);

-- 添加外键约束的ON DELETE规则
ALTER TABLE employees 
    ADD CONSTRAINT fk_employees_dept 
    FOREIGN KEY (dept_id) 
    REFERENCES departments(dept_id) 
    ON DELETE RESTRICT;

ALTER TABLE salaries 
    ADD CONSTRAINT fk_salaries_emp 
    FOREIGN KEY (emp_id) 
    REFERENCES employees(emp_id) 
    ON DELETE CASCADE;

ALTER TABLE employee_projects 
    ADD CONSTRAINT fk_emp_projects_emp 
    FOREIGN KEY (emp_id) 
    REFERENCES employees(emp_id) 
    ON DELETE CASCADE;

ALTER TABLE employee_projects 
    ADD CONSTRAINT fk_emp_projects_project 
    FOREIGN KEY (project_id) 
    REFERENCES projects(project_id) 
    ON DELETE RESTRICT;
