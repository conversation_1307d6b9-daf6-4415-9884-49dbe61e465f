-- 插入测试数据
-- 作者：Cascade AI
-- 创建时间：2025-02-25
-- 说明：用于测试数据库迁移工具的测试数据，包含一个大数据量表（employees）和其他基础数据

BEGIN;

-- 1. 插入部门数据
INSERT INTO departments (dept_name, budget) VALUES 
('研发部', 1000000.00),
('市场部', 800000.50),
('财务部', 1200000.75),
('人力资源部', 500000.25),
('运维部', 750000.00);

-- 2. 插入项目数据
INSERT INTO projects (project_name, tags, start_date, end_date) VALUES
('数据库迁移工具', ARRAY['C#'::varchar, 'PostgreSQL'::varchar, 'MySQL'::varchar], '2025-01-01', '2025-12-31'),
('自动化测试平台', ARRAY['Python'::varchar, 'Selenium'::varchar], '2025-02-01', '2025-06-30'),
('微服务框架', ARRAY['Java'::varchar, 'Spring Cloud'::varchar], '2025-03-01', '2025-09-30'),
('数据分析平台', ARRAY['Python'::varchar, 'Pandas'::varchar, 'AI'::varchar], '2025-04-01', '2025-12-31'),
('安全审计系统', ARRAY['Go'::varchar, 'Security'::varchar], '2025-05-01', '2025-11-30');

-- 3. 插入配置数据
INSERT INTO config_settings (setting_key, setting_value, data_type, is_encrypted) VALUES
('DB_TIMEOUT', '30', 'INTEGER', false),
('MAX_CONNECTIONS', '100', 'INTEGER', false),
('API_KEY', 'encrypted_value_here', 'STRING', true),
('DEBUG_MODE', 'true', 'BOOLEAN', false),
('BATCH_SIZE', '1000', 'INTEGER', false);

-- 4. 插入时区测试数据
INSERT INTO time_zone_test (zone_name, utc_offset, is_active) VALUES
('UTC', '00:00:00', true),
('Asia/Shanghai', '08:00:00', true),
('America/New_York', '-05:00:00', true),
('Europe/London', '00:00:00', true),
('Australia/Sydney', '11:00:00', true);

-- 5. 插入JSON测试数据
INSERT INTO json_data (metadata, tags) VALUES
(
    '{"type": "user_config", "settings": {"theme": "dark", "language": "zh-CN"}}'::jsonb,
    ARRAY['config'::varchar, 'user'::varchar]
),
(
    '{"type": "system_config", "settings": {"max_memory": "8G", "max_cpu": "4"}}'::jsonb,
    ARRAY['config'::varchar, 'system'::varchar]
),
(
    '{"type": "app_config", "settings": {"cache_size": "1G", "log_level": "INFO"}}'::jsonb,
    ARRAY['config'::varchar, 'app'::varchar]
);

-- 6. 生成大量员工数据（2万条）
INSERT INTO employees (dept_id, emp_name, email, hire_date, salary, skills, ip_address, profile)
SELECT 
    -- 部门ID在1-5之间随机分配
    (random() * 4 + 1)::int,
    -- 员工姓名
    '员工_' || generate_series,
    -- 邮箱
    'employee' || generate_series || '@example.com',
    -- 入职日期（过去5年内随机）
    CURRENT_DATE - (random() * 365 * 5)::int,
    -- 薪资（5000-15000之间）
    (random() * 10000 + 5000)::numeric(10,2),
    -- 技能（JSON格式）
    jsonb_build_object(
        'programming', (ARRAY['C#'::text, 'SQL'::text, 'Python'::text])[(random() * 2 + 1)::int],
        'level', (random() * 5 + 1)::int,
        'certificates', (ARRAY['PMP'::text, 'CISSP'::text, 'AWS'::text])[(random() * 2 + 1)::int]
    ),
    -- IP地址
    ('192.168.' || (random() * 255)::int || '.' || (random() * 255)::int)::inet,
    -- 简介
    '这是员工_' || generate_series || '的个人简介'
FROM generate_series(1, 20000);

-- 7. 为部分员工生成薪资记录
INSERT INTO salaries (emp_id, base_salary, bonus, tax_rate, payment_date)
SELECT 
    emp_id,
    salary,
    (random() * 5000)::numeric(10,2),
    (random() * 0.1 + 0.1)::decimal(5,4),
    CURRENT_DATE - (random() * 30)::int
FROM employees 
WHERE emp_id <= 100;  -- 只为前100名员工生成薪资记录

-- 8. 生成项目分配
INSERT INTO employee_projects (emp_id, project_id, role, join_date)
SELECT 
    e.emp_id,
    p.project_id,
    (ARRAY['开发者'::varchar, '测试员'::varchar, '架构师'::varchar, '项目经理'::varchar])[(random() * 3 + 1)::int],
    CURRENT_DATE - (random() * 90)::int
FROM 
    (SELECT emp_id FROM employees WHERE emp_id <= 50) e  -- 只为前50名员工分配项目
    CROSS JOIN 
    (SELECT project_id FROM projects) p
WHERE random() < 0.3;  -- 30%的概率分配到项目

-- 9. 生成一些审计日志
INSERT INTO audit_logs (table_name, operation, old_data, new_data)
SELECT 
    'employees',
    'UPDATE',
    jsonb_build_object('salary', 5000),
    jsonb_build_object('salary', 6000)
FROM generate_series(1, 5);

-- 10. 插入一些二进制数据测试记录
INSERT INTO binary_data (file_name, file_size, mime_type, checksum)
VALUES
('test1.pdf', 1024576, 'application/pdf', 'e10adc3949ba59abbe56e057f20f883e'),
('test2.jpg', 512000, 'image/jpeg', '098f6bcd4621d373cade4e832627b4f6'),
('test3.doc', 2048000, 'application/msword', '202cb962ac59075b964b07152d234b70');

COMMIT;
