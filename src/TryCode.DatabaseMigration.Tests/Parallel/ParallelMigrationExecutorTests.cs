using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using TryCode.DatabaseMigration.Checkpoint;
using TryCode.DatabaseMigration.Checkpoint.Models;
using TryCode.DatabaseMigration.Configuration.Models;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Parallel;
using TryCode.DatabaseMigration.Parallel.Models;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.Parallel
{
    /// <summary>
    /// 并行迁移执行器测试
    /// </summary>
    public class ParallelMigrationExecutorTests
    {
        private readonly Mock<IDataReader> _mockSourceReader;
        private readonly Mock<IDataWriter> _mockTargetWriter;
        private readonly Mock<ICheckpointManager> _mockCheckpointManager;
        private readonly Mock<ILogger<ParallelMigrationExecutor>> _mockLogger;
        private readonly MigrationConfig _config;
        private readonly Mock<IProgress<Core.Models.MigrationProgress>> _mockProgress;

        public ParallelMigrationExecutorTests()
        {
            _mockSourceReader = new Mock<IDataReader>();
            _mockTargetWriter = new Mock<IDataWriter>();
            _mockCheckpointManager = new Mock<ICheckpointManager>();
            _mockLogger = new Mock<ILogger<ParallelMigrationExecutor>>();
            _mockProgress = new Mock<IProgress<Core.Models.MigrationProgress>>();

            _config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = "PostgreSQL",
                    ConnectionString = "Host=localhost;Database=test;Username=test;Password=test",
                    BatchSize = 1000
                },
                Target = new DatabaseConfig
                {
                    Type = "MySQL",
                    ConnectionString = "Server=localhost;Database=test;Uid=test;Pwd=test;"
                },
                MaxDegreeOfParallelism = 4
            };
        }

        /// <summary>
        /// 测试当 SkipDataMigration 为 true 时，数据迁移是否被跳过
        /// </summary>
        [Fact]
        public async Task Should_Skip_Data_Migration_When_SkipDataMigration_Is_True()
        {
            // Arrange
            _config.SkipDataMigration = true;

            // 设置 GetTableSchemasAsync 的模拟返回值
            var tables = new List<TableSchema>
            {
                new TableSchema { Name = "users", Columns = new List<ColumnSchema>() },
                new TableSchema { Name = "products", Columns = new List<ColumnSchema>() }
            };
            _mockSourceReader.Setup(r => r.GetTableSchemasAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(tables);

            // 设置 GetTableRowCountAsync 的模拟返回值
            _mockSourceReader.Setup(r => r.GetTableRowCountAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(100);

            // 设置 InitializeMigrationCheckpointAsync 的模拟实现
            _mockCheckpointManager.Setup(c => c.InitializeMigrationCheckpointAsync(
                    It.IsAny<int>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // 设置 MarkTableInProgressAsync 的模拟实现
            _mockCheckpointManager.Setup(c => c.MarkTableInProgressAsync(
                    It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // 设置 UpdateTableRowsAsync 的模拟实现
            _mockCheckpointManager.Setup(c => c.UpdateTableRowsAsync(
                    It.IsAny<string>(), It.IsAny<long>(), It.IsAny<long>(),
                    It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // 设置 MarkTableCompletedAsync 的模拟实现
            _mockCheckpointManager.Setup(c => c.MarkTableCompletedAsync(
                    It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var executor = new ParallelMigrationExecutor(
                _mockSourceReader.Object,
                _mockTargetWriter.Object,
                _mockCheckpointManager.Object,
                _config,
                _mockProgress.Object,
                _mockLogger.Object);

            // Act
            await executor.ExecuteAsync(CancellationToken.None);

            // Assert
            // 验证表结构创建被调用
            _mockTargetWriter.Verify(w => w.CreateTableAsync(
                    It.IsAny<TableSchema>(), It.IsAny<bool>(),It.IsAny<CancellationToken>()),
                Times.Exactly(2)); // 应该为两个表创建表结构

            // 验证数据迁移没有被调用
            _mockSourceReader.Verify(r => r.ReadTableDataAsync(
                    It.IsAny<string>(), It.IsAny<int>(), It.IsAny<long>(),
                    It.IsAny<CancellationToken>()),
                Times.Never); // 不应该读取任何表数据

            _mockTargetWriter.Verify(w => w.BulkWriteAsync(
                    It.IsAny<string>(), It.IsAny<IEnumerable<IDictionary<string, object>>>(),
                    It.IsAny<CancellationToken>()),
                Times.Never); // 不应该写入任何表数据
        }
    }
}
//
//         [Theory]
//         [InlineData(1)]
//         [InlineData(2)]
//         [InlineData(4)]
//         public async Task ExecuteAsync_MigratesTablesInParallel(int parallelism)
//         {
//             // 准备
//             _config.MaxDegreeOfParallelism = parallelism;
//
//             var tables = new List<TableSchema>
//             {
//                 new TableSchema { Name = "table1" },
//                 new TableSchema { Name = "table2" }
//             };
//
//             var checkpoint = new MigrationCheckpoint
//             {
//                 SourceType = "PostgreSQL",
//                 TargetType = "MySQL"
//             };
//
//             _mockReader.Setup(r => r.GetTableSchemasAsync(It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(tables);
//
//             _mockReader.Setup(r => r.GetTableRowCountAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(1000);
//
//             _mockReader.Setup(r => r.ReadTableDataAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<int>(),
//                     It.IsAny<long>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new List<Dictionary<string, object>> { new Dictionary<string, object>() });
//
//             _mockCheckpointManager.Setup(m => m.InitializeAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<string>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(checkpoint);
//
//             _mockCheckpointManager.Setup(m => m.GetTableCheckpointAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync((string tableName, CancellationToken _) => new TableCheckpoint 
//                 { 
//                     TableName = tableName,
//                     TotalRows = 1000 
//                 });
//
//             // 记录所有调用的表名和调用次数
//             var tableCallCounts = new Dictionary<string, int>();
//                 
//             _mockWriter.Setup(w => w.BulkWriteAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<IEnumerable<object>>(), // 修改为正确的参数类型
//                     It.IsAny<CancellationToken>()))
//                 .Callback<string, IEnumerable<object>, CancellationToken>((tableName, data, token) => 
//                 {
//                     if (!tableCallCounts.ContainsKey(tableName))
//                         tableCallCounts[tableName] = 0;
//                     tableCallCounts[tableName]++;
//                 })
//                 .Returns(Task.CompletedTask);
//
//             // 执行
//             await _executor.ExecuteAsync();
//
//             // 验证
//             Assert.True(tableCallCounts.ContainsKey("table1") && tableCallCounts["table1"] > 0, 
//                 $"表 'table1' 的 BulkWriteAsync 应该被调用至少一次，实际调用 {(tableCallCounts.ContainsKey("table1") ? tableCallCounts["table1"] : 0)} 次");
//             Assert.True(tableCallCounts.ContainsKey("table2") && tableCallCounts["table2"] > 0, 
//                 $"表 'table2' 的 BulkWriteAsync 应该被调用至少一次，实际调用 {(tableCallCounts.ContainsKey("table2") ? tableCallCounts["table2"] : 0)} 次");
//
//             _mockWriter.Verify(w => w.CreateForeignKeysAsync(
//                 It.IsAny<string>(),
//                 It.IsAny<List<ForeignKeySchema>>(),
//                 It.IsAny<CancellationToken>()),
//                 Times.Exactly(2));
//
//             _mockProgress.Verify(p => p.Report(It.IsAny<TableMigrationProgress>()), Times.AtLeast(6));
//         }
//
//         [Fact]
//         public async Task ExecuteAsync_SkipsSchemaCreation_WhenConfigured()
//         {
//             // 准备
//             _config.SkipSchemaCreation = true;
//
//             var tables = new List<TableSchema>
//             {
//                 new TableSchema { Name = "table1" }
//             };
//
//             _mockReader.Setup(r => r.GetTableSchemasAsync(It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(tables);
//
//             _mockReader.Setup(r => r.GetTableRowCountAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(1000);
//
//             _mockReader.Setup(r => r.ReadTableDataAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<int>(),
//                     It.IsAny<long>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new List<Dictionary<string, object>> { new Dictionary<string, object>() });
//
//             var checkpoint = new MigrationCheckpoint
//             {
//                 SourceType = "PostgreSQL",
//                 TargetType = "MySQL"
//             };
//
//             _mockCheckpointManager.Setup(m => m.InitializeAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<string>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(checkpoint);
//
//             _mockCheckpointManager.Setup(m => m.GetTableCheckpointAsync(
//                     It.Is<string>(s => s == "table1"),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new TableCheckpoint 
//                 { 
//                     TableName = "table1",
//                     TotalRows = 1000,
//                     SchemaCreated = true
//                 });
//
//             // 执行
//             await _executor.ExecuteAsync();
//
//             // 验证
//             _mockWriter.Verify(w => w.CreateTableAsync(
//                 It.IsAny<TableSchema>(),
//                 It.IsAny<CancellationToken>()),
//                 Times.Never);
//         }
//
//         [Fact]
//         public async Task ExecuteAsync_SkipsForeignKeys_WhenConfigured()
//         {
//             // 准备
//             _config.SkipForeignKeys = true;
//
//             var tables = new List<TableSchema>
//             {
//                 new TableSchema { Name = "table1" }
//             };
//
//             _mockReader.Setup(r => r.GetTableSchemasAsync(It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(tables);
//
//             _mockReader.Setup(r => r.GetTableRowCountAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(1000);
//
//             _mockReader.Setup(r => r.ReadTableDataAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<int>(),
//                     It.IsAny<long>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new List<Dictionary<string, object>> { new Dictionary<string, object>() });
//
//             var checkpoint = new MigrationCheckpoint
//             {
//                 SourceType = "PostgreSQL",
//                 TargetType = "MySQL"
//             };
//
//             _mockCheckpointManager.Setup(m => m.InitializeAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<string>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(checkpoint);
//
//             _mockCheckpointManager.Setup(m => m.GetTableCheckpointAsync(
//                     It.Is<string>(s => s == "table1"),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new TableCheckpoint 
//                 { 
//                     TableName = "table1",
//                     TotalRows = 1000,
//                     SchemaCreated = true
//                 });
//
//             // 执行
//             await _executor.ExecuteAsync();
//
//             // 验证
//             _mockWriter.Verify(w => w.CreateForeignKeysAsync(
//                 It.IsAny<string>(),
//                 It.IsAny<List<ForeignKeySchema>>(),
//                 It.IsAny<CancellationToken>()),
//                 Times.Never);
//         }
//
//         [Fact]
//         public async Task ExecuteAsync_FiltersTablesByConfig()
//         {
//             // 准备
//             _config.Tables = new List<string> { "table1" };
//
//             var tables = new List<TableSchema>
//             {
//                 new TableSchema { Name = "table1" },
//                 new TableSchema { Name = "table2" }
//             };
//
//             _mockReader.Setup(r => r.GetTableSchemasAsync(It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(tables);
//
//             _mockReader.Setup(r => r.GetTableRowCountAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(1000);
//
//             _mockReader.Setup(r => r.ReadTableDataAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<int>(),
//                     It.IsAny<long>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new List<Dictionary<string, object>> { new Dictionary<string, object>() });
//
//             var checkpoint = new MigrationCheckpoint
//             {
//                 SourceType = "PostgreSQL",
//                 TargetType = "MySQL"
//             };
//
//             _mockCheckpointManager.Setup(m => m.InitializeAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<string>(),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(checkpoint);
//
//             _mockCheckpointManager.Setup(m => m.GetTableCheckpointAsync(
//                     It.Is<string>(s => s == "table1"),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(new TableCheckpoint 
//                 { 
//                     TableName = "table1",
//                     TotalRows = 1000,
//                     SchemaCreated = true
//                 });
//
//             // 记录所有调用的表名和调用次数
//             var tableCallCounts = new Dictionary<string, int>();
//                 
//             _mockWriter.Setup(w => w.BulkWriteAsync(
//                     It.IsAny<string>(),
//                     It.IsAny<IEnumerable<object>>(), // 修改为正确的参数类型
//                     It.IsAny<CancellationToken>()))
//                 .Callback<string, IEnumerable<object>, CancellationToken>((tableName, data, token) => 
//                 {
//                     if (!tableCallCounts.ContainsKey(tableName))
//                         tableCallCounts[tableName] = 0;
//                     tableCallCounts[tableName]++;
//                 })
//                 .Returns(Task.CompletedTask);
//
//             // 执行
//             await _executor.ExecuteAsync();
//
//             // 验证
//             Assert.True(tableCallCounts.ContainsKey("table1") && tableCallCounts["table1"] > 0, 
//                 $"表 'table1' 的 BulkWriteAsync 应该被调用至少一次，实际调用 {(tableCallCounts.ContainsKey("table1") ? tableCallCounts["table1"] : 0)} 次");
//             Assert.True(!tableCallCounts.ContainsKey("table2") || tableCallCounts["table2"] == 0, 
//                 $"表 'table2' 不应该被迁移，实际调用 {(tableCallCounts.ContainsKey("table2") ? tableCallCounts["table2"] : 0)} 次");
//         }
//
//         [Fact]
//         public async Task ExecuteAsync_HandlesErrors_AndUpdatesCheckpoint()
//         {
//             // 准备
//             var tables = new List<TableSchema>
//             {
//                 new TableSchema { Name = "table1" }
//             };
//
//             _mockReader.Setup(r => r.GetTableSchemasAsync(It.IsAny<CancellationToken>()))
//                 .ReturnsAsync(tables);
//
//             _mockCheckpointManager.Setup(m => m.GetTableCheckpointAsync(
//                     It.Is<string>(s => s == "table1" || s == "table2"),
//                     It.IsAny<CancellationToken>()))
//                 .ReturnsAsync((string tableName, CancellationToken _) => new TableCheckpoint 
//                 { 
//                     TableName = tableName,
//                     TotalRows = 1000 
//                 });
//
//             _mockWriter.Setup(w => w.CreateTableAsync(
//                     It.IsAny<TableSchema>(),
//                     It.IsAny<CancellationToken>()))
//                 .ThrowsAsync(new Exception("测试错误"));
//
//             // 执行和验证
//             var ex = await Assert.ThrowsAsync<AggregateException>(() => _executor.ExecuteAsync());
//             Assert.Contains(ex.InnerExceptions, e => e.InnerException?.Message == "测试错误");
//
//             _mockCheckpointManager.Verify(m => m.MarkTableFailedAsync(
//                 It.IsAny<string>(),
//                 It.Is<string>(s => s.Contains("测试错误")),
//                 It.IsAny<CancellationToken>()), Times.Once);
//         }
//     }
// }
