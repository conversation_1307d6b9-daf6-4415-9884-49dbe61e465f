<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="6.0.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="3.2.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TryCode.DatabaseMigration.CLI\TryCode.DatabaseMigration.CLI.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Core\TryCode.DatabaseMigration.Core.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Configuration\TryCode.DatabaseMigration.Configuration.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.MySQL\TryCode.DatabaseMigration.MySQL.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.PostgreSQL\TryCode.DatabaseMigration.PostgreSQL.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Parallel\TryCode.DatabaseMigration.Parallel.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Checkpoint\TryCode.DatabaseMigration.Checkpoint.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="TestData\create_tables.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\insert_test_data.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
