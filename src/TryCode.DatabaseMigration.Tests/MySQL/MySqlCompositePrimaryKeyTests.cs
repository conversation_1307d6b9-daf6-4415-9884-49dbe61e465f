using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.MySQL;
using Xunit;
using Xunit.Abstractions;

namespace TryCode.DatabaseMigration.Tests.MySQL
{
    /// <summary>
    /// MySQL复合主键处理测试
    /// </summary>
    public class MySqlCompositePrimaryKeyTests : IDisposable
    {
        private readonly string _connectionString;
        private readonly MySqlDataWriter _writer;
        private readonly ITestOutputHelper _output;

        public MySqlCompositePrimaryKeyTests(ITestOutputHelper output)
        {
            _output = output;
            // 使用统一的测试数据库连接字符串
            _connectionString = DatabaseTestConfig.MySqlConnectionString;
            _writer = new MySqlDataWriter(_connectionString, NullLogger<MySqlDataWriter>.Instance);
        }

        [Fact]
        public async Task CreateTable_WithCompositePrimaryKey_CreatesCorrectDataTypes()
        {
            // 准备
            var tableName = $"test_composite_pk_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            
            // 创建一个表架构，包含复合主键
            var schema = new TableSchema
            {
                Name = tableName,
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema
                    {
                        Name = "ProcessId",
                        DataType = "varchar",
                        MaxLength = 50,
                        IsNullable = false
                    },
                    new ColumnSchema
                    {
                        Name = "ServiceId",
                        DataType = "varchar",
                        MaxLength = 50,
                        IsNullable = false
                    },
                    new ColumnSchema
                    {
                        Name = "Description",
                        DataType = "varchar",
                        MaxLength = 255,
                        IsNullable = true
                    }
                }
            };
            
            // 添加复合主键
            schema.AddPrimaryKey("ProcessId");
            schema.AddPrimaryKey("ServiceId");
            
            _output.WriteLine($"表架构: {tableName}");
            _output.WriteLine($"主键列: {string.Join(", ", schema.PrimaryKeys)}");
            foreach (var column in schema.Columns)
            {
                _output.WriteLine($"列 {column.Name}: 数据类型={column.DataType}, 长度={column.MaxLength}, 是主键={column.IsPrimaryKey}");
            }

            try
            {
                // 执行
                await _writer.CreateTableAsync(schema);
                _output.WriteLine($"表 {tableName} 创建成功");

                // 验证
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 检查表是否创建
                var tableExists = await connection.ExecuteScalarAsync<int>(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = @TableName AND table_schema = DATABASE()",
                    new { TableName = tableName });
                
                _output.WriteLine($"表存在检查: {tableExists > 0}");
                Assert.Equal(1, tableExists);

                // 检查列的数据类型
                var columns = await connection.QueryAsync(
                    @"SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = DATABASE()",
                    new { TableName = tableName });

                var columnsList = columns.ToList();
                _output.WriteLine($"找到 {columnsList.Count} 列");

                foreach (var column in columnsList)
                {
                    _output.WriteLine($"列: {column.COLUMN_NAME}");
                    _output.WriteLine($"  数据类型: {column.DATA_TYPE}");
                    _output.WriteLine($"  列类型: {column.COLUMN_TYPE}");
                    _output.WriteLine($"  是否可空: {column.IS_NULLABLE}");
                    _output.WriteLine($"  键类型: {column.COLUMN_KEY}");
                }

                // 验证 ProcessId 列
                var processIdColumn = columnsList.FirstOrDefault(c => c.COLUMN_NAME == "ProcessId");
                Assert.NotNull(processIdColumn);
                Assert.Equal("varchar", processIdColumn.DATA_TYPE);
                Assert.Equal("PRI", processIdColumn.COLUMN_KEY); // PRI 表示主键

                // 验证 ServiceId 列
                var serviceIdColumn = columnsList.FirstOrDefault(c => c.COLUMN_NAME == "ServiceId");
                Assert.NotNull(serviceIdColumn);
                Assert.Equal("varchar", serviceIdColumn.DATA_TYPE);
                Assert.Equal("PRI", serviceIdColumn.COLUMN_KEY); // PRI 表示主键

                // 验证两列都被正确地设置为主键
                Assert.Contains("varchar", processIdColumn.COLUMN_TYPE.ToString());
                Assert.Contains("varchar", serviceIdColumn.COLUMN_TYPE.ToString());

                // 确保非 TEXT 类型
                Assert.DoesNotContain("text", processIdColumn.COLUMN_TYPE.ToString().ToLower());
                Assert.DoesNotContain("text", serviceIdColumn.COLUMN_TYPE.ToString().ToLower());
            }
            finally
            {
                // 清理 - 删除测试表
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();
                try
                {
                    await connection.ExecuteAsync($"DROP TABLE IF EXISTS `{tableName}`");
                    _output.WriteLine($"表 {tableName} 已删除");
                }
                catch (Exception ex)
                {
                    _output.WriteLine($"清理表时出错: {ex.Message}");
                }
            }
        }

        [Fact]
        public async Task AddRecords_ToTableWithCompositePrimaryKey_Succeeds()
        {
            // 准备
            var tableName = $"test_composite_pk_data_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            
            // 创建一个表架构，包含复合主键
            var schema = new TableSchema
            {
                Name = tableName,
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema
                    {
                        Name = "ProcessId",
                        DataType = "varchar",
                        MaxLength = 50,
                        IsNullable = false
                    },
                    new ColumnSchema
                    {
                        Name = "ServiceId",
                        DataType = "varchar",
                        MaxLength = 50,
                        IsNullable = false
                    },
                    new ColumnSchema
                    {
                        Name = "Status",
                        DataType = "varchar",
                        MaxLength = 20,
                        IsNullable = true
                    }
                }
            };
            
            // 添加复合主键
            schema.AddPrimaryKey("ProcessId");
            schema.AddPrimaryKey("ServiceId");

            try
            {
                // 创建表
                await _writer.CreateTableAsync(schema);
                _output.WriteLine($"表 {tableName} 创建成功");

                // 准备测试数据
                var testData = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        ["ProcessId"] = "PROC-001",
                        ["ServiceId"] = "SVC-001",
                        ["Status"] = "Active"
                    },
                    new Dictionary<string, object>
                    {
                        ["ProcessId"] = "PROC-001",
                        ["ServiceId"] = "SVC-002",
                        ["Status"] = "Pending"
                    },
                    new Dictionary<string, object>
                    {
                        ["ProcessId"] = "PROC-002",
                        ["ServiceId"] = "SVC-001",
                        ["Status"] = "Completed"
                    }
                };

                // 写入数据
                await _writer.BulkWriteAsync(tableName, testData);
                _output.WriteLine($"向表 {tableName} 写入了 {testData.Count} 行数据");

                // 验证数据
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                var records = await connection.QueryAsync($"SELECT * FROM `{tableName}`");
                var recordsList = records.ToList();

                _output.WriteLine($"从表中检索到 {recordsList.Count} 行数据");
                foreach (var record in recordsList)
                {
                    _output.WriteLine($"记录: ProcessId={record.ProcessId}, ServiceId={record.ServiceId}, Status={record.Status}");
                }

                Assert.Equal(testData.Count, recordsList.Count);
            }
            finally
            {
                // 清理 - 删除测试表
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();
                try
                {
                    await connection.ExecuteAsync($"DROP TABLE IF EXISTS `{tableName}`");
                    _output.WriteLine($"表 {tableName} 已删除");
                }
                catch (Exception ex)
                {
                    _output.WriteLine($"清理表时出错: {ex.Message}");
                }
            }
        }

        public void Dispose()
        {
            // 清理资源
        }
    }
}
