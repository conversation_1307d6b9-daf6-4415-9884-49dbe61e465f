using System;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.MySQL;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.MySQL
{
    /// <summary>
    /// MySQL数据读取器测试
    /// 注意：这些测试需要一个可用的MySQL测试数据库
    /// </summary>
    public class MySqlDataReaderTests : IDisposable
    {
        private readonly string _connectionString;
        private readonly string _masterConnectionString;
        private readonly TryCode.DatabaseMigration.MySQL.MySqlDataReader _reader;

        public MySqlDataReaderTests()
        {
            // 使用统一的测试数据库连接字符串
            _masterConnectionString = DatabaseTestConfig.MySqlMasterConnectionString;
            _connectionString = DatabaseTestConfig.MySqlConnectionString;
            _reader = new TryCode.DatabaseMigration.MySQL.MySqlDataReader(_connectionString);

            // 创建测试数据库
            using (var conn = new MySqlConnection(_masterConnectionString))
            {
                conn.Open();
                conn.Execute($"DROP DATABASE IF EXISTS test_db1");
                conn.Execute($"CREATE DATABASE test_db1");
            }

            // 清理并设置测试数据
            CleanupTestDatabase().Wait();
            SetupTestDatabase().Wait();
        }

        [Fact]
        public async Task GetTableSchemasAsync_ReturnsCorrectSchemas()
        {
            // 执行
            var schemas = await _reader.GetTableSchemasAsync();

            // 验证
            Assert.NotNull(schemas);
            Assert.NotEmpty(schemas);

            var testTable = schemas.FirstOrDefault(s => s.Name == "test_table");
            Assert.NotNull(testTable);
            Assert.Equal(4, testTable.Columns.Count);
            Assert.Contains(testTable.Columns, c => c.Name == "id" && c.DataType == "int");
            Assert.Contains(testTable.Columns, c => c.Name == "name" && c.DataType == "varchar");
            Assert.Contains(testTable.Columns, c => c.Name == "age" && c.DataType == "int");
            Assert.Contains(testTable.Columns, c => c.Name == "data" && c.DataType == "json");
            Assert.Contains(testTable.PrimaryKeys, pk => pk == "id");
        }

        [Theory]
        [InlineData(5, 0)]  // 第一页
        [InlineData(5, 5)]  // 第二页
        [InlineData(2, 8)]  // 部分页
        public async Task ReadTableDataAsync_WithPagination_ReturnsCorrectData(int pageSize, long offset)
        {
            // 执行
            var data = await _reader.ReadTableDataAsync("test_table", pageSize, offset);

            // 验证
            Assert.NotNull(data);
            var rows = data.ToList();
            Assert.True((int)rows.Count() <= pageSize);

            if (rows.Any())
            {
                var firstRow = (IDictionary<string, object>)rows.First();
                Assert.True(firstRow.ContainsKey("id"));
                Assert.True(firstRow.ContainsKey("name"));
                Assert.True(firstRow.ContainsKey("age"));
                Assert.True(firstRow.ContainsKey("data"));
            }
        }

        [Fact]
        public async Task GetTableRowCountAsync_ReturnsCorrectCount()
        {
            // 执行
            var count = await _reader.GetTableRowCountAsync("test_table");

            // 验证
            Assert.Equal(10, count); // 假设测试数据有10行
        }

        public void Dispose()
        {
            // 清理测试数据
            CleanupTestDatabase().Wait();
        }

        private async Task SetupTestDatabase()
        {
            await using var connection = new MySqlConnection(_connectionString);
            await connection.OpenAsync();

            // 创建测试数据库
            await connection.ExecuteAsync(@"
                DROP TABLE IF EXISTS test_table;
                CREATE TABLE test_table (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50),
                    age INT,
                    data JSON
                );");

            // 插入测试数据
            var testData = new List<object>();
            for (int i = 1; i <= 10; i++)
            {
                testData.Add(new
                {
                    name = $"Test{i}",
                    age = 20 + i,
                    data = $"{{\"key{i}\": \"value{i}\"}}"
                });
            }

            await connection.ExecuteAsync(@"
                INSERT INTO test_table (name, age, data)
                VALUES (@name, @age, @data)",
                testData);
        }

        private async Task CleanupTestDatabase()
        {
            await using var connection = new MySqlConnection(_connectionString);
            await connection.OpenAsync();

            // 删除测试表（级联删除会自动处理外键表）
            await connection.ExecuteAsync("DROP TABLE IF EXISTS test_table");
        }
    }
}
