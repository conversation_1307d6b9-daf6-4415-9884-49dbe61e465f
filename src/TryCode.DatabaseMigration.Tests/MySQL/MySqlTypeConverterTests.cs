using System;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.MySQL.TypeConverters;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.MySQL
{
    /// <summary>
    /// MySQL类型转换器测试
    /// </summary>
    public class MySqlTypeConverterTests
    {
        private readonly MySqlTypeConverter _converter;

        public MySqlTypeConverterTests()
        {
            _converter = new MySqlTypeConverter();
        }

        [Theory]
        [InlineData("tinyint", "smallint")]
        [InlineData("smallint", "int")]
        [InlineData("int", "bigint")]
        [InlineData("char", "varchar")]
        [InlineData("varchar", "text")]
        [InlineData("text", "mediumtext")]
        [InlineData("json", "longtext")]
        public void CanConvert_SupportedTypes_ReturnsTrue(string sourceType, string targetType)
        {
            // 执行
            var result = _converter.CanConvert(sourceType, targetType);

            // 验证
            Assert.True(result);
        }

        [Theory]
        [InlineData("int", "varchar")]
        [InlineData("text", "int")]
        [InlineData("json", "binary")]
        public void CanConvert_UnsupportedTypes_ReturnsFalse(string sourceType, string targetType)
        {
            // 执行
            var result = _converter.CanConvert(sourceType, targetType);

            // 验证
            Assert.False(result);
        }

        [Theory]
        [InlineData("varchar", 100, null, null, "varchar(100)")]
        [InlineData("text", null, null, null, "text")]
        [InlineData("decimal", null, 10, 2, "decimal(10, 2)")]
        [InlineData("tinyint", null, null, null, "tinyint")]
        public void ConvertDataType_ReturnsCorrectMySqlType(
            string sourceType,
            int? maxLength,
            int? precision,
            int? scale,
            string expectedType)
        {
            // 准备
            var column = new ColumnSchema
            {
                Name = "test",
                DataType = sourceType,
                MaxLength = maxLength,
                NumericPrecision = precision,
                Scale = scale
            };

            // 执行
            var result = _converter.ConvertDataType(sourceType, column);

            // 验证
            Assert.Equal(expectedType, result);
        }

        [Theory]
        [InlineData(1, "tinyint", "int", 1)]
        [InlineData(100, "smallint", "int", 100)]
        [InlineData("test", "varchar", "text", "test")]
        public async Task ConvertValueAsync_BasicTypes_ConvertsCorrectly(
            object value,
            string sourceType,
            string targetType,
            object expected)
        {
            // 执行
            var result = await _converter.ConvertValueAsync(value, sourceType, targetType);

            // 验证
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("2024-02-23", "date", "datetime")]
        [InlineData("12:34:56", "time", "datetime")]
        public async Task ConvertValueAsync_DateTimeTypes_ConvertsCorrectly(
            string value,
            string sourceType,
            string targetType)
        {
            // 执行
            var result = await _converter.ConvertValueAsync(value, sourceType, targetType);

            // 验证
            Assert.NotNull(result);
            Assert.IsType<DateTime>(result);
        }

        [Fact]
        public async Task ConvertValueAsync_NullValue_ReturnsNull()
        {
            // 执行
            var result = await _converter.ConvertValueAsync(null, "varchar", "text");

            // 验证
            Assert.Null(result);
        }

        [Fact]
        public async Task ConvertValueAsync_DBNullValue_ReturnsNull()
        {
            // 执行
            var result = await _converter.ConvertValueAsync(DBNull.Value, "varchar", "text");

            // 验证
            Assert.Null(result);
        }

        [Theory]
        [InlineData(true, "tinyint", "tinyint(1)")]
        [InlineData(false, "tinyint", "tinyint")]
        public void ConvertDataType_BooleanTinyInt_ReturnsCorrectType(bool isBoolean, string sourceType, string expectedType)
        {
            // 准备
            var column = new ColumnSchema
            {
                Name = "test",
                DataType = sourceType,
                IsBoolean = isBoolean
            };

            // 执行
            var result = _converter.ConvertDataType(sourceType, column);

            // 验证
            Assert.Equal(expectedType, result);
        }
    }
}
