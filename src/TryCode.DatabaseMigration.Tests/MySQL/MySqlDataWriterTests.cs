using Dapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.MySQL;
using Xunit;
using Xunit.Abstractions;

namespace TryCode.DatabaseMigration.Tests.MySQL
{
    /// <summary>
    /// MySQL数据写入器测试
    /// 注意：这些测试需要一个可用的MySQL测试数据库
    /// </summary>
    public class MySqlDataWriterTests : IDisposable
    {
        private readonly string _connectionString;
        private readonly MySqlDataWriter _writer;
        private readonly ITestOutputHelper _output;

        public MySqlDataWriterTests(ITestOutputHelper output)
        {
            _output = output;
            // 使用统一的测试数据库连接字符串
            _connectionString = DatabaseTestConfig.MySqlConnectionString;
            _writer = new MySqlDataWriter(_connectionString, NullLogger<MySqlDataWriter>.Instance);
        }

        [Fact]
        public async Task CreateTableAsync_CreatesTableWithCorrectSchema()
        {
            // 测试数据库连接
            using (var testConn = new MySqlConnection(_connectionString))
            {
                try
                {
                    await testConn.OpenAsync();
                    _output.WriteLine("Successfully connected to database");

                    // 获取当前数据库名称
                    var dbName = await testConn.ExecuteScalarAsync<string>("SELECT DATABASE()");
                    _output.WriteLine($"Current database: {dbName}");

                    // 获取当前用户权限
                    var privileges = await testConn.QueryAsync<dynamic>("SHOW GRANTS");
                    _output.WriteLine("Current user privileges:");
                    foreach (var privilege in privileges)
                    {
                        _output.WriteLine($"  {privilege}");
                    }
                }
                catch (Exception ex)
                {
                    _output.WriteLine($"Database connection test failed: {ex.Message}");
                    _output.WriteLine(ex.StackTrace);
                    throw;
                }
            }

            // 清理：删除已存在的表
            using (var cleanupConn = new MySqlConnection(_connectionString))
            {
                try
                {
                    await cleanupConn.ExecuteAsync($"DROP TABLE IF EXISTS test_create_table");
                    _output.WriteLine("Successfully cleaned up existing table");
                }
                catch (Exception ex)
                {
                    _output.WriteLine($"Failed to clean up existing table: {ex.Message}");
                    _output.WriteLine(ex.StackTrace);
                    throw;
                }
            }

            // 准备
            var schema = new TableSchema
            {
                Name = "test_create_table",
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema
                    {
                        Name = "id",
                        DataType = "int",
                        IsNullable = false,
                        IsAutoIncrement = true
                    },
                    new ColumnSchema
                    {
                        Name = "name",
                        DataType = "varchar",
                        MaxLength = 100,
                        IsNullable = true
                    },
                    new ColumnSchema
                    {
                        Name = "created_at",
                        DataType = "timestamp",
                        IsNullable = false,
                        DefaultValue = "CURRENT_TIMESTAMP"
                    }
                },
                PrimaryKeys = new List<string> { "id" }
            };

            // 执行
            await _writer.CreateTableAsync(schema);

            // 验证
            using var conn = new MySqlConnection(_connectionString);
            await conn.OpenAsync();

            // 先检查表是否存在
            var tableExists = await conn.ExecuteScalarAsync<bool>(@"
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                    AND table_name = @tableName
                )",
                new { tableName = schema.Name });

            _output.WriteLine($"Table exists: {tableExists}");
            Assert.True(tableExists, "表应该已经创建");

            // 获取所有列信息
            var columnsQuery = @"
                SELECT 
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    extra,
                    column_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = @tableName
                ORDER BY ordinal_position";

            var columns = await conn.QueryAsync<dynamic>(columnsQuery, new { tableName = schema.Name });
            var columnsList = columns.ToList();
            _output.WriteLine($"\n找到 {columnsList.Count} 列在表 {schema.Name} 中");
            foreach (var column in columnsList)
            {
                _output.WriteLine($"\n列: {column.COLUMN_NAME}");
                _output.WriteLine($"  数据类型: {column.DATA_TYPE}");
                _output.WriteLine($"  列类型: {column.COLUMN_TYPE}");
                _output.WriteLine($"  是否可空: {column.IS_NULLABLE}");
                _output.WriteLine($"  默认值: {column.COLUMN_DEFAULT}");
                _output.WriteLine($"  额外信息: {column.EXTRA}");
                _output.WriteLine($"  最大长度: {column.CHARACTER_MAXIMUM_LENGTH}");
                _output.WriteLine($"  数值精度: {column.NUMERIC_PRECISION}");
                _output.WriteLine($"  小数位数: {column.NUMERIC_SCALE}");
            }
            // 验证列数
            Assert.Equal(3, columnsList.Count);

            // 获取并验证每一列
            var idColumn = columnsList.FirstOrDefault(c => c.COLUMN_NAME == "id");
            _output.WriteLine("\n验证id列:");
            _output.WriteLine($"  找到id列: {idColumn != null}");
            if (idColumn != null)
            {
                _output.WriteLine($"  数据类型: {idColumn.DATA_TYPE}");
                _output.WriteLine($"  是否可空: {idColumn.IS_NULLABLE}");
                _output.WriteLine($"  额外信息: {idColumn.EXTRA}");
            }
            Assert.NotNull(idColumn);
            Assert.Equal("int", idColumn.DATA_TYPE);
            Assert.Equal("NO", idColumn.IS_NULLABLE);
            Assert.Contains("auto_increment", idColumn.EXTRA.ToString());

            var nameColumn = columnsList.FirstOrDefault(c => c.COLUMN_NAME == "name");
            _output.WriteLine("\n验证name列:");
            _output.WriteLine($"  找到name列: {nameColumn != null}");
            if (nameColumn != null)
            {
                _output.WriteLine($"  数据类型: {nameColumn.DATA_TYPE}");
                _output.WriteLine($"  是否可空: {nameColumn.IS_NULLABLE}");
                _output.WriteLine($"  列类型: {nameColumn.COLUMN_TYPE}");
            }
            Assert.NotNull(nameColumn);
            Assert.Equal("varchar", nameColumn.DATA_TYPE);
            Assert.Equal("YES", nameColumn.IS_NULLABLE);
            Assert.Equal("varchar(100)", nameColumn.COLUMN_TYPE);

            var createdAtColumn = columnsList.FirstOrDefault(c => c.COLUMN_NAME == "created_at");
            _output.WriteLine("\n验证created_at列:");
            _output.WriteLine($"  找到created_at列: {createdAtColumn != null}");
            if (createdAtColumn != null)
            {
                _output.WriteLine($"  数据类型: {createdAtColumn.DATA_TYPE}");
                _output.WriteLine($"  是否可空: {createdAtColumn.IS_NULLABLE}");
                _output.WriteLine($"  默认值: {createdAtColumn.COLUMN_DEFAULT}");
            }
            Assert.NotNull(createdAtColumn);
            Assert.Equal("timestamp", createdAtColumn.DATA_TYPE.ToString().ToLower());
            Assert.Equal("NO", createdAtColumn.IS_NULLABLE);
            Assert.NotNull(createdAtColumn.COLUMN_DEFAULT);
            Assert.Contains("CURRENT_TIMESTAMP", createdAtColumn.COLUMN_DEFAULT.ToString().ToUpper());
        }

        [Theory]
        [InlineData(1)]    // 单行数据
        [InlineData(10)]   // 多行数据
        [InlineData(100)]  // 批量数据
        public async Task BulkWriteAsync_InsertsDataCorrectly(int rowCount)
        {
            // 准备
            var tableName = $"test_bulk_write_{rowCount}";
            await CreateTestTable(tableName);

            var testData = Enumerable.Range(1, rowCount)
                .Select(i => new Dictionary<string, object>
                {
                    ["id"] = i,
                    ["name"] = $"Test {i}",
                    ["value"] = i * 10
                })
                .ToList();

            // 执行
            await _writer.BulkWriteAsync(tableName, testData);

            // 验证
            using var conn = new MySqlConnection(_connectionString);
            var count = await conn.ExecuteScalarAsync<int>(
                $"SELECT COUNT(*) FROM `{tableName}`");
            Assert.Equal(rowCount, count);

            var firstRow = await conn.QueryFirstAsync(
                $"SELECT * FROM `{tableName}` WHERE id = 1");
            Assert.Equal("Test 1", firstRow.name);
            Assert.Equal(10, firstRow.value);
        }

        [Fact]
        public async Task CreateForeignKeysAsync_CreatesForeignKeysCorrectly()
        {
            // 准备
            var mainTableName = "test_fk_main";
            var detailTableName = "test_fk_detail";

            // 创建主表和从表
            await CreateTestTables(mainTableName, detailTableName);

            var foreignKeys = new List<ForeignKeySchema>
            {
                new ForeignKeySchema
                {
                    Name = "fk_test_detail_main",
                    ColumnName = "main_id",
                    ReferencedTable = mainTableName,
                    ReferencedColumn = "id"
                }
            };

            // 执行
            await _writer.CreateForeignKeysAsync(detailTableName, foreignKeys);

            // 验证
            using var conn = new MySqlConnection(_connectionString);
            var fkExists = await conn.ExecuteScalarAsync<bool>(@"
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.key_column_usage
                    WHERE table_schema = DATABASE()
                    AND table_name = @tableName
                    AND referenced_table_name = @referencedTable
                    AND constraint_name = @constraintName
                )",
                new
                {
                    tableName = detailTableName,
                    referencedTable = mainTableName,
                    constraintName = foreignKeys[0].Name
                });

            Assert.True(fkExists);
        }

        [Fact]
        public async Task TestDatabaseConnection()
        {
            using var conn = new MySqlConnection(_connectionString);
            try
            {
                await conn.OpenAsync();
                _output.WriteLine("数据库连接成功");

                // 测试数据库版本
                var version = await conn.ExecuteScalarAsync<string>("SELECT VERSION()");
                _output.WriteLine($"MySQL版本: {version}");

                // 测试当前数据库
                var database = await conn.ExecuteScalarAsync<string>("SELECT DATABASE()");
                _output.WriteLine($"当前数据库: {database}");

                // 测试用户权限
                var privileges = await conn.QueryAsync<dynamic>("SHOW GRANTS");
                _output.WriteLine("用户权限:");
                foreach (var privilege in privileges)
                {
                    _output.WriteLine($"  {privilege}");
                }

                // 测试创建表权限
                await conn.ExecuteAsync("DROP TABLE IF EXISTS test_connection");
                await conn.ExecuteAsync(@"
                    CREATE TABLE test_connection (
                        id int NOT NULL AUTO_INCREMENT,
                        name varchar(100),
                        PRIMARY KEY (id)
                    )");
                _output.WriteLine("测试表创建成功");

                // 验证表是否创建成功
                var tableExists = await conn.ExecuteScalarAsync<bool>(@"
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = DATABASE()
                        AND table_name = 'test_connection'
                    )");
                _output.WriteLine($"测试表存在: {tableExists}");

                // 获取表的列信息
                var columns = await conn.QueryAsync<dynamic>(@"
                    SELECT 
                        column_name,
                        data_type,
                        is_nullable,
                        column_default,
                        extra
                    FROM information_schema.columns
                    WHERE table_schema = DATABASE()
                    AND table_name = 'test_connection'
                    ORDER BY ordinal_position");

                _output.WriteLine("测试表的列信息:");
                foreach (var column in columns)
                {
                    _output.WriteLine($"  列名: {column.column_name}");
                    _output.WriteLine($"    数据类型: {column.data_type}");
                    _output.WriteLine($"    是否可空: {column.is_nullable}");
                    _output.WriteLine($"    默认值: {column.column_default}");
                    _output.WriteLine($"    额外信息: {column.extra}");
                }

                // 清理
                await conn.ExecuteAsync("DROP TABLE IF EXISTS test_connection");
                _output.WriteLine("测试表删除成功");
            }
            catch (Exception ex)
            {
                _output.WriteLine($"数据库测试失败: {ex.Message}");
                _output.WriteLine(ex.StackTrace);
                throw;
            }
        }

        private async Task CreateTestTable(string tableName)
        {
            using var conn = new MySqlConnection(_connectionString);
            await conn.ExecuteAsync($@"
                CREATE TABLE IF NOT EXISTS `{tableName}` (
                    id INT PRIMARY KEY,
                    name VARCHAR(100),
                    value INT
                )");
        }

        private async Task CreateTestTables(string mainTableName, string detailTableName)
        {
            using var conn = new MySqlConnection(_connectionString);
            await conn.ExecuteAsync($@"
                CREATE TABLE IF NOT EXISTS `{mainTableName}` (
                    id INT PRIMARY KEY,
                    name VARCHAR(100)
                )");

            await conn.ExecuteAsync($@"
                CREATE TABLE IF NOT EXISTS `{detailTableName}` (
                    id INT PRIMARY KEY,
                    main_id INT,
                    detail VARCHAR(100)
                )");
        }

        public void Dispose()
        {
            // 清理测试表
            using var conn = new MySqlConnection(_connectionString);
            conn.Open();

            // 获取所有以test_开头的表
            var tables = conn.Query<string>(@"
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                AND table_name LIKE 'test_%'");

            foreach (var table in tables)
            {
                conn.Execute($"DROP TABLE IF EXISTS `{table}`");
            }
        }
    }
}
