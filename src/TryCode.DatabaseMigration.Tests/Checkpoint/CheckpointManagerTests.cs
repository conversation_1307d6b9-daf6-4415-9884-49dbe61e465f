using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Checkpoint;
using TryCode.DatabaseMigration.Checkpoint.Models;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.Checkpoint
{
    /// <summary>
    /// 断点管理器测试
    /// </summary>
    public class CheckpointManagerTests : IDisposable
    {
        private readonly string _checkpointFilePath = "test_checkpoint.json";
        private readonly CheckpointManager _manager;

        public CheckpointManagerTests()
        {
            _manager = new CheckpointManager(_checkpointFilePath);
        }

        public void Dispose()
        {
            if (File.Exists(_checkpointFilePath))
            {
                File.Delete(_checkpointFilePath);
            }
        }

        [Theory]
        [InlineData("PostgreSQL", "MySQL")]
        [InlineData("MySQL", "PostgreSQL")]
        [InlineData("SQLServer", "MySQL")]
        public async Task InitializeAsync_CreatesNewCheckpoint_WhenFileDoesNotExist(
            string sourceType,
            string targetType)
        {
            // 执行
            var checkpoint = await _manager.InitializeAsync(sourceType, targetType);

            // 验证
            Assert.NotNull(checkpoint);
            Assert.Equal(sourceType, checkpoint.SourceType);
            Assert.Equal(targetType, checkpoint.TargetType);
            Assert.Equal("NotStarted", checkpoint.Status);
            Assert.NotNull(checkpoint.TableCheckpoints);
            Assert.Empty(checkpoint.TableCheckpoints);
        }

        [Fact]
        public async Task InitializeAsync_LoadsExistingCheckpoint_WhenFileExists()
        {
            // 准备
            var existingCheckpoint = new MigrationCheckpoint
            {
                SourceType = "PostgreSQL",
                TargetType = "MySQL",
                Status = "InProgress",
                TotalTables = 2,
                CompletedTables = 1
            };

            var json = JsonSerializer.Serialize(existingCheckpoint);
            await File.WriteAllTextAsync(_checkpointFilePath, json);

            // 执行
            var checkpoint = await _manager.InitializeAsync("PostgreSQL", "MySQL");

            // 验证
            Assert.NotNull(checkpoint);
            Assert.Equal("PostgreSQL", checkpoint.SourceType);
            Assert.Equal("MySQL", checkpoint.TargetType);
            Assert.Equal("InProgress", checkpoint.Status);
            Assert.Equal(2, checkpoint.TotalTables);
            Assert.Equal(1, checkpoint.CompletedTables);
        }

        [Theory]
        [InlineData("test_table1")]
        [InlineData("test_table2")]
        public async Task GetTableCheckpointAsync_CreatesNewTableCheckpoint_WhenNotExists(string tableName)
        {
            // 准备
            await _manager.InitializeAsync("PostgreSQL", "MySQL");

            // 执行
            var tableCheckpoint = await _manager.GetTableCheckpointAsync(tableName);

            // 验证
            Assert.NotNull(tableCheckpoint);
            Assert.Equal(tableName, tableCheckpoint.TableName);
            Assert.Equal("NotStarted", tableCheckpoint.Status);
            Assert.Equal(0, tableCheckpoint.TotalRows);
            Assert.Equal(0, tableCheckpoint.MigratedRows);
        }

        [Fact]
        public async Task UpdateTableCheckpointAsync_UpdatesProgress()
        {
            // 准备
            await _manager.InitializeAsync("PostgreSQL", "MySQL");
            var tableCheckpoint = await _manager.GetTableCheckpointAsync("test_table");

            // 更新进度
            tableCheckpoint.TotalRows = 100;
            tableCheckpoint.MigratedRows = 50;
            tableCheckpoint.Status = "InProgress";

            // 执行
            await _manager.UpdateTableCheckpointAsync(tableCheckpoint);

            // 验证
            var updatedCheckpoint = await _manager.GetTableCheckpointAsync("test_table");
            Assert.Equal(100, updatedCheckpoint.TotalRows);
            Assert.Equal(50, updatedCheckpoint.MigratedRows);
            Assert.Equal("InProgress", updatedCheckpoint.Status);
        }

        [Fact]
        public async Task MarkTableCompletedAsync_UpdatesStatus()
        {
            // 准备
            await _manager.InitializeAsync("PostgreSQL", "MySQL");
            var tableCheckpoint = await _manager.GetTableCheckpointAsync("test_table");

            // 执行
            await _manager.MarkTableCompletedAsync("test_table");

            // 验证
            var updatedCheckpoint = await _manager.GetTableCheckpointAsync("test_table");
            Assert.Equal("Completed", updatedCheckpoint.Status);
        }

        [Theory]
        [InlineData("表结构创建失败")]
        [InlineData("数据迁移失败")]
        public async Task MarkTableFailedAsync_UpdatesStatusAndError(string errorMessage)
        {
            // 准备
            await _manager.InitializeAsync("PostgreSQL", "MySQL");
            var tableCheckpoint = await _manager.GetTableCheckpointAsync("test_table");

            // 执行
            await _manager.MarkTableFailedAsync("test_table", errorMessage);

            // 验证
            var updatedCheckpoint = await _manager.GetTableCheckpointAsync("test_table");
            Assert.Equal("Failed", updatedCheckpoint.Status);
            Assert.Equal(errorMessage, updatedCheckpoint.ErrorMessage);
        }

        [Fact]
        public async Task InitializeAsync_ThrowsException_WhenTypeMismatch()
        {
            // 准备
            var existingCheckpoint = new MigrationCheckpoint
            {
                SourceType = "PostgreSQL",
                TargetType = "MySQL"
            };

            var json = JsonSerializer.Serialize(existingCheckpoint);
            await File.WriteAllTextAsync(_checkpointFilePath, json);

            // 执行和验证
            await Assert.ThrowsAsync<InvalidOperationException>(
                () => _manager.InitializeAsync("MySQL", "PostgreSQL"));
        }
    }
}
