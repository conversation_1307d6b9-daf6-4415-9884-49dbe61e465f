using System;
using System.Text.Json;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.PostgreSQL.TypeConverters;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.PostgreSQL
{
    /// <summary>
    /// PostgreSQL类型转换器测试
    /// </summary>
    public class PostgresTypeConverterTests
    {
        private readonly PostgresTypeConverter _converter;

        public PostgresTypeConverterTests()
        {
            _converter = new PostgresTypeConverter();
        }

        [Theory]
        [InlineData("json", "nvarchar")]
        [InlineData("jsonb", "nvarchar")]
        [InlineData("uuid", "uniqueidentifier")]
        [InlineData("timestamp", "datetime2")]
        [InlineData("bytea", "varbinary")]
        public void CanConvert_SupportedTypes_ReturnsTrue(string sourceType, string targetType)
        {
            // 执行
            var result = _converter.CanConvert(sourceType, targetType);

            // 验证
            Assert.True(result);
        }

        [Theory]
        [InlineData("json", "int")]
        [InlineData("text", "json")]
        [InlineData("uuid", "varchar")]
        public void CanConvert_UnsupportedTypes_ReturnsFalse(string sourceType, string targetType)
        {
            // 执行
            var result = _converter.CanConvert(sourceType, targetType);

            // 验证
            Assert.False(result);
        }

        [Theory]
        [InlineData("json", "nvarchar(max)")]
        [InlineData("uuid", "uniqueidentifier")]
        [InlineData("timestamp without time zone", "datetime2")]
        [InlineData("timestamp with time zone", "datetimeoffset")]
        public void ConvertDataType_ReturnsCorrectSqlServerType(string sourceType, string expectedType)
        {
            // 准备
            var column = new ColumnSchema { Name = "test", DataType = sourceType };

            // 执行
            var result = _converter.ConvertDataType(sourceType, column);

            // 验证
            Assert.Equal(expectedType, result);
        }

        [Fact]
        public async Task ConvertValueAsync_JsonToString_ConvertsCorrectly()
        {
            // 准备
            var jsonDoc = JsonDocument.Parse("{\"name\":\"test\",\"value\":123}");

            // 执行
            var result = await _converter.ConvertValueAsync(jsonDoc, "json", "nvarchar");

            // 验证
            Assert.Equal("{\"name\":\"test\",\"value\":123}", result);
        }

        [Theory]
        [InlineData("***********", "inet", "nvarchar")]
        [InlineData("2001:db8::", "inet", "nvarchar")]
        public async Task ConvertValueAsync_NetworkAddressToString_ConvertsCorrectly(
            string value, string sourceType, string targetType)
        {
            // 执行
            var result = await _converter.ConvertValueAsync(value, sourceType, targetType);

            // 验证
            Assert.Equal(value, result);
        }

        [Theory]
        [InlineData("1 year 2 months", "interval", "nvarchar")]
        [InlineData("2 days 3 hours", "interval", "nvarchar")]
        public async Task ConvertValueAsync_IntervalToString_ConvertsCorrectly(
            string value, string sourceType, string targetType)
        {
            // 执行
            var result = await _converter.ConvertValueAsync(value, sourceType, targetType);

            // 验证
            Assert.Equal(value, result);
        }

        [Fact]
        public async Task ConvertValueAsync_NullValue_ReturnsNull()
        {
            // 执行
            var result = await _converter.ConvertValueAsync(null, "json", "nvarchar");

            // 验证
            Assert.Null(result);
        }

        [Fact]
        public async Task ConvertValueAsync_DBNullValue_ReturnsNull()
        {
            // 执行
            var result = await _converter.ConvertValueAsync(DBNull.Value, "json", "nvarchar");

            // 验证
            Assert.Null(result);
        }
    }
}
