using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Nhgdb;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.PostgreSQL;
using Xunit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Debug;

namespace TryCode.DatabaseMigration.Tests.PostgreSQL
{
    /// <summary>
    /// PostgreSQL数据写入器测试
    /// 注意：这些测试需要一个可用的PostgreSQL测试数据库
    /// </summary>
    public class PostgresDataWriterTests : IDisposable
    {
        private readonly string _connectionString;
        private readonly PostgresDataWriter _writer;
        private readonly ILogger<PostgresDataWriter> _logger;

        public PostgresDataWriterTests()
        {
            // 使用测试数据库连接字符串
            _connectionString = DatabaseTestConfig.PostgresConnectionString;
            
            // 创建日志记录器
            var loggerFactory = new LoggerFactory(new[] { new DebugLoggerProvider() });
            _logger = loggerFactory.CreateLogger<PostgresDataWriter>();
            
            _writer = new PostgresDataWriter(_connectionString, _logger);
        }

        [Fact]
        public async Task CreateTableAsync_CreatesTableWithCorrectSchema()
        {
            // 准备
            var schema = new TableSchema
            {
                Name = "test_create_table",
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema
                    {
                        Name = "id",
                        DataType = "integer",
                        IsNullable = false
                    },
                    new ColumnSchema
                    {
                        Name = "name",
                        DataType = "varchar",
                        MaxLength = 100,
                        IsNullable = true
                    },
                    new ColumnSchema
                    {
                        Name = "created_at",
                        DataType = "timestamp",
                        IsNullable = false,
                        DefaultValue = "CURRENT_TIMESTAMP"
                    }
                },
                PrimaryKeys = new List<string> { "id" }
            };

            // 执行
            await _writer.CreateTableAsync(schema);

            // 验证
            using var conn = new NhgdbConnection(_connectionString);
            var tableExists = await conn.ExecuteScalarAsync<bool>(@"
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = @tableName
                )",
                new { tableName = schema.Name });

            Assert.True(tableExists);

            var columns = await conn.QueryAsync(@"
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = @tableName",
                new { tableName = schema.Name });

            Assert.Equal(3, columns.Count());
            Assert.Contains(columns, c => 
                c.column_name == "id" && 
                c.data_type == "integer" && 
                c.is_nullable == "NO");
            Assert.Contains(columns, c => 
                c.column_name == "name" && 
                c.data_type == "character varying" && 
                c.is_nullable == "YES");
        }

        [Theory]
        [InlineData(1)]    // 单行数据
        [InlineData(10)]   // 多行数据
        [InlineData(100)]  // 批量数据
        public async Task BulkWriteAsync_InsertsDataCorrectly(int rowCount)
        {
            // 准备
            var tableName = $"test_bulk_write_{rowCount}";
            await CreateTestTable(tableName);

            var testData = Enumerable.Range(1, rowCount)
                .Select(i => new Dictionary<string, object>
                {
                    ["id"] = i,
                    ["name"] = $"Test {i}",
                    ["value"] = i * 10
                })
                .ToList();

            // 执行
            await _writer.BulkWriteAsync(tableName, testData);

            // 验证
            using var conn = new NhgdbConnection(_connectionString);
            var count = await conn.ExecuteScalarAsync<int>(
                $"SELECT COUNT(*) FROM \"{tableName}\"");
            Assert.Equal(rowCount, count);

            var firstRow = await conn.QueryFirstAsync(
                $"SELECT * FROM \"{tableName}\" WHERE id = 1");
            Assert.Equal("Test 1", firstRow.name);
            Assert.Equal(10, firstRow.value);
        }

        [Fact]
        public async Task CreateForeignKeysAsync_CreatesForeignKeysCorrectly()
        {
            // 准备
            var mainTableName = "test_fk_main";
            var detailTableName = "test_fk_detail";

            // 创建主表和从表
            await CreateTestTables(mainTableName, detailTableName);

            var foreignKeys = new List<ForeignKeySchema>
            {
                new ForeignKeySchema
                {
                    Name = "fk_test_detail_main",
                    ColumnName = "main_id",
                    ReferencedTable = mainTableName,
                    ReferencedColumn = "id"
                }
            };

            // 执行
            await _writer.CreateForeignKeysAsync(detailTableName, foreignKeys);

            // 验证
            using var conn = new NhgdbConnection(_connectionString);
            var fkExists = await conn.ExecuteScalarAsync<bool>(@"
                SELECT EXISTS (
                    SELECT FROM information_schema.table_constraints
                    WHERE constraint_type = 'FOREIGN KEY'
                    AND table_name = @tableName
                    AND constraint_name = @constraintName
                )",
                new 
                { 
                    tableName = detailTableName,
                    constraintName = foreignKeys[0].Name
                });

            Assert.True(fkExists);
        }

        private async Task CreateTestTable(string tableName)
        {
            using var conn = new NhgdbConnection(_connectionString);
            await conn.ExecuteAsync($@"
                CREATE TABLE IF NOT EXISTS ""{tableName}"" (
                    id INTEGER PRIMARY KEY,
                    name VARCHAR(100),
                    value INTEGER
                )");
        }

        private async Task CreateTestTables(string mainTableName, string detailTableName)
        {
            using var conn = new NhgdbConnection(_connectionString);
            await conn.ExecuteAsync($@"
                CREATE TABLE IF NOT EXISTS ""{mainTableName}"" (
                    id INTEGER PRIMARY KEY,
                    name VARCHAR(100)
                )");

            await conn.ExecuteAsync($@"
                CREATE TABLE IF NOT EXISTS ""{detailTableName}"" (
                    id INTEGER PRIMARY KEY,
                    main_id INTEGER,
                    detail VARCHAR(100)
                )");
        }

        public void Dispose()
        {
            // 清理测试表
            using var conn = new NhgdbConnection(_connectionString);
            conn.Open();

            // 获取所有以test_开头的表
            var tables = conn.Query<string>(@"
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name LIKE 'test_%'");

            foreach (var table in tables)
            {
                conn.Execute($"DROP TABLE IF EXISTS \"{table}\" CASCADE");
            }
        }
    }
}
