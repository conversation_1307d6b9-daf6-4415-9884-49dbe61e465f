using System;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Nhgdb;
using TryCode.DatabaseMigration.PostgreSQL;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.PostgreSQL
{
    /// <summary>
    /// PostgreSQL数据读取器测试
    /// 注意：这些测试需要一个可用的PostgreSQL测试数据库
    /// </summary>
    public class PostgresDataReaderTests : IDisposable
    {
        private readonly string _connectionString;
        private readonly PostgresDataReader _reader;

        public PostgresDataReaderTests()
        {
            // 使用测试数据库连接字符串
            _connectionString = DatabaseTestConfig.PostgresConnectionString;
            _reader = new PostgresDataReader(_connectionString);
            
            // 设置测试数据
            SetupTestDatabase().Wait();
        }

        [Fact]
        public async Task GetTableSchemasAsync_ReturnsCorrectSchemas()
        {
            // 执行
            var schemas = await _reader.GetTableSchemasAsync();

            // 验证
            Assert.NotNull(schemas);
            Assert.NotEmpty(schemas);

            var testTable = schemas.FirstOrDefault(s => s.Name == "test_table");
            Assert.NotNull(testTable);
            Assert.Equal(4, testTable.Columns.Count);
            Assert.Contains(testTable.Columns, c => c.Name == "id" && c.DataType == "integer");
            Assert.Contains(testTable.Columns, c => c.Name == "name" && c.DataType == "character varying");
            Assert.Contains(testTable.Columns, c => c.Name == "age" && c.DataType == "integer");
            Assert.Contains(testTable.Columns, c => c.Name == "data" && c.DataType == "jsonb");
            Assert.Contains(testTable.PrimaryKeys, pk => pk == "id");
        }

        [Theory]
        [InlineData(5, 0)]  // 第一页
        [InlineData(5, 5)]  // 第二页
        [InlineData(2, 8)]  // 部分页
        public async Task ReadTableDataAsync_WithPagination_ReturnsCorrectData(int pageSize, long offset)
        {
            // 执行
            var data = await _reader.ReadTableDataAsync("test_table", pageSize, offset);

            // 验证
            Assert.NotNull(data);
            var rows = data.ToList();
            Assert.True(rows.Count <= pageSize);
            
            if (rows.Any())
            {
                var firstRow = (IDictionary<string, object>)rows.First();
                Assert.True(firstRow.ContainsKey("id"));
                Assert.True(firstRow.ContainsKey("name"));
                Assert.True(firstRow.ContainsKey("age"));
                Assert.True(firstRow.ContainsKey("data"));
            }
        }

        [Fact]
        public async Task GetTableRowCountAsync_ReturnsCorrectCount()
        {
            // 执行
            var count = await _reader.GetTableRowCountAsync("test_table");

            // 验证
            Assert.Equal(10, count); // 假设测试数据有10行
        }

        public void Dispose()
        {
            // 清理测试数据
            CleanupTestDatabase().Wait();
        }

        private async Task SetupTestDatabase()
        {
            await using var connection = new NhgdbConnection(_connectionString);
            await connection.OpenAsync();

            // 创建测试数据库
            await connection.ExecuteAsync(@"
                DROP TABLE IF EXISTS test_table;
                CREATE TABLE test_table (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50),
                    age INT,
                    data JSONB
                );");

            // 插入测试数据
            var testData = new List<object>();
            for (int i = 1; i <= 10; i++)
            {
                testData.Add(new
                {
                    name = $"Test{i}",
                    age = 20 + i,
                    data = $"{{\"key{i}\": \"value{i}\"}}"
                });
            }

            await connection.ExecuteAsync(@"
                INSERT INTO test_table (name, age, data)
                VALUES (@name, @age, @data::jsonb)",
                testData);
        }

        private async Task CleanupTestDatabase()
        {
            await using var connection = new NhgdbConnection(_connectionString);
            await connection.OpenAsync();

            // 删除测试表（级联删除会自动处理外键表）
            await connection.ExecuteAsync("DROP TABLE IF EXISTS test_table");
        }
    }
}
