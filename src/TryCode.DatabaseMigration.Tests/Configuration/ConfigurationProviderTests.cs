using System;
using System.IO;
using System.Text.Json;
using TryCode.DatabaseMigration.Configuration;
using TryCode.DatabaseMigration.Configuration.Models;
using TryCode.DatabaseMigration.Core.Exceptions;
using Xunit;

namespace TryCode.DatabaseMigration.Tests.Configuration
{
    /// <summary>
    /// 配置提供程序测试
    /// </summary>
    public class ConfigurationProviderTests : IDisposable
    {
        private readonly string _testConfigPath = "test_config.json";

        public void Dispose()
        {
            if (File.Exists(_testConfigPath))
            {
                File.Delete(_testConfigPath);
            }
        }

        [Fact]
        public void Constructor_WithInvalidPath_ThrowsFileNotFoundException()
        {
            // 验证
            Assert.Throws<FileNotFoundException>(() => new ConfigurationProvider("invalid_path.json"));
        }

        [Fact]
        public void Constructor_WithNullPath_ThrowsArgumentNullException()
        {
            // 验证
            Assert.Throws<ArgumentNullException>(() => new ConfigurationProvider(null));
        }

        [Theory]
        [InlineData("PostgreSQL", "MySQL")]
        [InlineData("MySQL", "PostgreSQL")]
        [InlineData("SQLServer", "MySQL")]
        public void GetMigrationConfig_WithValidConfig_ReturnsCorrectConfig(string sourceType, string targetType)
        {
            // 准备
            var config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = sourceType,
                    ConnectionString = "connection_string_1",
                    BatchSize = 1000,
                    CommandTimeout = 30
                },
                Target = new DatabaseConfig
                {
                    Type = targetType,
                    ConnectionString = "connection_string_2",
                    BatchSize = 1000,
                    CommandTimeout = 30
                },
                EnableCheckpoint = true,
                CheckpointFilePath = "checkpoint.json",
                MaxDegreeOfParallelism = 4
            };

            var json = JsonSerializer.Serialize(config);
            File.WriteAllText(_testConfigPath, json);

            // 执行
            var provider = new ConfigurationProvider(_testConfigPath);
            var result = provider.GetMigrationConfig();

            // 验证
            Assert.NotNull(result);
            Assert.Equal(sourceType, result.Source.Type);
            Assert.Equal(targetType, result.Target.Type);
            Assert.Equal("connection_string_1", result.Source.ConnectionString);
            Assert.Equal("connection_string_2", result.Target.ConnectionString);
            Assert.Equal(1000, result.Source.BatchSize);
            Assert.Equal(30, result.Source.CommandTimeout);
            Assert.True(result.EnableCheckpoint);
            Assert.Equal("checkpoint.json", result.CheckpointFilePath);
            Assert.Equal(4, result.MaxDegreeOfParallelism);
        }

        [Theory]
        [InlineData(null, "MySQL", "源数据库类型不能为空")]
        [InlineData("PostgreSQL", null, "目标数据库类型不能为空")]
        [InlineData("PostgreSQL", "MySQL", "源数据库连接字符串不能为空", true)]
        public void GetMigrationConfig_WithInvalidConfig_ThrowsInvalidOperationException(
            string sourceType,
            string targetType,
            string expectedError,
            bool nullConnectionString = false)
        {
            // 准备
            var config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = sourceType,
                    ConnectionString = nullConnectionString ? null : "connection_string_1"
                },
                Target = new DatabaseConfig
                {
                    Type = targetType,
                    ConnectionString = "connection_string_2"
                }
            };

            var json = JsonSerializer.Serialize(config);
            File.WriteAllText(_testConfigPath, json);

            // 执行和验证
            var provider = new ConfigurationProvider(_testConfigPath);
            var exception = Assert.Throws<TryCode.DatabaseMigration.Core.Exceptions.ConfigurationValidationException>(() => provider.GetMigrationConfig());
            Assert.Equal(expectedError, exception.Message);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        public void GetMigrationConfig_WithInvalidParallelism_ThrowsInvalidOperationException(int parallelism)
        {
            // 准备
            var config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = "PostgreSQL",
                    ConnectionString = "connection_string_1"
                },
                Target = new DatabaseConfig
                {
                    Type = "MySQL",
                    ConnectionString = "connection_string_2"
                },
                MaxDegreeOfParallelism = parallelism
            };

            var json = JsonSerializer.Serialize(config);
            File.WriteAllText(_testConfigPath, json);

            // 执行和验证
            var provider = new ConfigurationProvider(_testConfigPath);
            var exception = Assert.Throws<TryCode.DatabaseMigration.Core.Exceptions.ConfigurationValidationException>(() => provider.GetMigrationConfig());
            Assert.Equal("最大并行度必须大于0", exception.Message);
        }

        [Theory]
        [InlineData(true, null)]
        [InlineData(true, "")]
        public void GetMigrationConfig_WithInvalidCheckpoint_ThrowsInvalidOperationException(
            bool enableCheckpoint,
            string checkpointPath)
        {
            // 准备
            var config = new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = "PostgreSQL",
                    ConnectionString = "connection_string_1"
                },
                Target = new DatabaseConfig
                {
                    Type = "MySQL",
                    ConnectionString = "connection_string_2"
                },
                EnableCheckpoint = enableCheckpoint,
                CheckpointFilePath = checkpointPath
            };

            var json = JsonSerializer.Serialize(config);
            File.WriteAllText(_testConfigPath, json);

            // 执行和验证
            var provider = new ConfigurationProvider(_testConfigPath);
            var exception = Assert.Throws<ConfigurationValidationException>(() => provider.GetMigrationConfig());
            Assert.Equal("启用断点续传时必须指定断点文件路径", exception.Message);
        }
    }
}
