using System;
using System.Configuration;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using TryCode.DatabaseMigration.Configuration.Models;
using TryCode.DatabaseMigration.Core.Exceptions;

namespace TryCode.DatabaseMigration.Configuration
{
    /// <summary>
    /// 配置提供程序
    /// </summary>
    public class ConfigurationProvider
    {
        private readonly IConfiguration _configuration;

        /// <summary>
        /// 从JSON文件加载配置
        /// </summary>
        public ConfigurationProvider(string configFilePath)
        {
            if (string.IsNullOrEmpty(configFilePath))
            {
                throw new ArgumentNullException(nameof(configFilePath));
            }

            if (!File.Exists(configFilePath))
            {
                throw new FileNotFoundException("配置文件不存在", configFilePath);
            }

            _configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile(configFilePath, optional: false, reloadOnChange: true)
                .Build();
        }

        /// <summary>
        /// 获取迁移配置
        /// </summary>
        public MigrationConfig GetMigrationConfig()
        {
            var config = CreateDefaultConfig();
            _configuration.Bind(config);
            ValidateConfig(config);
            return config;
        }

        /// <summary>
        /// 获取迁移配置（异步）
        /// </summary>
        public async Task<MigrationConfig> GetMigrationConfigAsync()
        {
            var config = CreateDefaultConfig();
            _configuration.Bind(config);
            await ValidateConfigAsync(config);
            return config;
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private MigrationConfig CreateDefaultConfig()
        {
            return new MigrationConfig
            {
                Source = new DatabaseConfig
                {
                    Type = "postgresql",
                    ConnectionString = ""
                },
                Target = new DatabaseConfig
                {
                    Type = "postgresql",
                    ConnectionString = ""
                },
                Logging = new LoggingConfig
                {
                    Level = "Information",
                    FilePath = "migration.log"
                }
            };
        }

        /// <summary>
        /// 验证配置的有效性
        /// </summary>
        private void ValidateConfig(MigrationConfig config)
        {
            if (config == null)
            {
                throw new ConfigurationValidationException("配置对象不能为空");
            }

            if (config.Source == null)
            {
                throw new ConfigurationValidationException("源数据库配置不能为空");
            }

            if (string.IsNullOrEmpty(config.Source.Type))
            {
                throw new ConfigurationValidationException("源数据库类型不能为空");
            }

            if (string.IsNullOrEmpty(config.Source.ConnectionString))
            {
                throw new ConfigurationValidationException("源数据库连接字符串不能为空");
            }

            if (config.Target == null)
            {
                throw new ConfigurationValidationException("目标数据库配置不能为空");
            }

            if (string.IsNullOrEmpty(config.Target.Type))
            {
                throw new ConfigurationValidationException("目标数据库类型不能为空");
            }

            if (string.IsNullOrEmpty(config.Target.ConnectionString))
            {
                throw new ConfigurationValidationException("目标数据库连接字符串不能为空");
            }

            if (config.EnableCheckpoint && string.IsNullOrEmpty(config.CheckpointFilePath))
            {
                throw new ConfigurationValidationException("启用断点续传时必须指定断点文件路径");
            }

            if (config.MaxDegreeOfParallelism < 1)
            {
                throw new ConfigurationValidationException("最大并行度必须大于0");
            }

            if (config.Source.BatchSize < 1)
            {
                throw new ConfigurationValidationException("批处理大小必须大于0");
            }

            if (config.Source.CommandTimeout < 1)
            {
                throw new ConfigurationValidationException("命令超时时间必须大于0");
            }

            // 验证数据库配置
            ValidateDatabaseConfig(config.Source);
            ValidateDatabaseConfig(config.Target);

            // 验证表配置
            // 注释掉这段代码，允许同时指定包含表和排除表
            // if (config.Tables.Count > 0 && config.ExcludedTables.Count > 0)
            // {
            //     throw new ConfigurationException("不能同时指定包含表和排除表");
            // }

            // 验证并行度配置
        }

        /// <summary>
        /// 验证配置的有效性（异步）
        /// </summary>
        private async Task ValidateConfigAsync(MigrationConfig config)
        {
            if (config == null)
            {
                throw new ConfigurationValidationException("配置对象不能为空");
            }

            if (config.Source == null)
            {
                throw new ConfigurationValidationException("源数据库配置不能为空");
            }

            if (string.IsNullOrEmpty(config.Source.Type))
            {
                throw new ConfigurationValidationException("源数据库类型不能为空");
            }

            if (string.IsNullOrEmpty(config.Source.ConnectionString))
            {
                throw new ConfigurationValidationException("源数据库连接字符串不能为空");
            }

            if (config.Target == null)
            {
                throw new ConfigurationValidationException("目标数据库配置不能为空");
            }

            if (string.IsNullOrEmpty(config.Target.Type))
            {
                throw new ConfigurationValidationException("目标数据库类型不能为空");
            }

            if (string.IsNullOrEmpty(config.Target.ConnectionString))
            {
                throw new ConfigurationValidationException("目标数据库连接字符串不能为空");
            }

            if (config.EnableCheckpoint && string.IsNullOrEmpty(config.CheckpointFilePath))
            {
                throw new ConfigurationValidationException("启用断点续传时必须指定断点文件路径");
            }

            if (config.MaxDegreeOfParallelism < 1)
            {
                throw new ConfigurationValidationException("最大并行度必须大于0");
            }

            if (config.Source.BatchSize < 1)
            {
                throw new ConfigurationValidationException("批处理大小必须大于0");
            }

            if (config.Source.CommandTimeout < 1)
            {
                throw new ConfigurationValidationException("命令超时时间必须大于0");
            }

            // 验证数据库配置
            ValidateDatabaseConfig(config.Source);
            ValidateDatabaseConfig(config.Target);

            // 验证表配置
            // 注释掉这段代码，允许同时指定包含表和排除表
            // if (config.Tables.Count > 0 && config.ExcludedTables.Count > 0)
            // {
            //     throw new ConfigurationException("不能同时指定包含表和排除表");
            // }

            // 验证配置文件路径是否可写
            if (config.EnableCheckpoint)
            {
                var checkpointDir = Path.GetDirectoryName(config.CheckpointFilePath);
                if (!string.IsNullOrEmpty(checkpointDir))
                {
                    try
                    {
                        if (!Directory.Exists(checkpointDir))
                        {
                            Directory.CreateDirectory(checkpointDir);
                        }

                        // 测试文件写入权限
                        var testFile = Path.Combine(checkpointDir, ".test");
                        await File.WriteAllTextAsync(testFile, string.Empty);
                        File.Delete(testFile);
                    }
                    catch (Exception ex)
                    {
                        throw new ConfigurationValidationException($"断点文件路径不可写: {ex.Message}", ex);
                    }
                }
            }
        }

        private void ValidateDatabaseConfig(DatabaseConfig config)
        {
            // 验证数据库配置
        }
    }
}
