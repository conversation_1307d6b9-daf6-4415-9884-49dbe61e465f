<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TryCode.DatabaseMigration.Core\TryCode.DatabaseMigration.Core.csproj" />
  </ItemGroup>

</Project>
