namespace TryCode.DatabaseMigration.Configuration.Models
{
    /// <summary>
    /// 数据库配置
    /// </summary>
    public class DatabaseConfig
    {
        /// <summary>
        /// 数据库类型（postgresql/mysql）
        /// </summary>
        public string Type { get; set; } = "postgresql";

        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString { get; set; } = "";

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 1000;

        /// <summary>
        /// 命令超时时间（秒）
        /// </summary>
        public int CommandTimeout { get; set; } = 30;
    }
}
