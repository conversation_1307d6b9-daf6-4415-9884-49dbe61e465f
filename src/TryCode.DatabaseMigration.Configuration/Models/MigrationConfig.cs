using System.Collections.Generic;

namespace TryCode.DatabaseMigration.Configuration.Models
{
    /// <summary>
    /// 迁移配置
    /// </summary>
    public class MigrationConfig
    {
        /// <summary>
        /// 源数据库配置
        /// </summary>
        public DatabaseConfig Source { get; set; } = new()
        {
            Type = "postgresql",
            ConnectionString = ""
        };

        /// <summary>
        /// 目标数据库配置
        /// </summary>
        public DatabaseConfig Target { get; set; } = new()
        {
            Type = "postgresql",
            ConnectionString = ""
        };

        /// <summary>
        /// 要迁移的表列表，为空则迁移所有表
        /// </summary>
        public List<string> Tables { get; set; } = new();

        /// <summary>
        /// 要排除的表列表，与Tables互斥
        /// </summary>
        public List<string> ExcludedTables { get; set; } = new();

        /// <summary>
        /// 是否为排除的表创建表结构（不迁移数据）
        /// </summary>
        public bool CreateSchemaForExcludedTables { get; set; } = false;

        /// <summary>
        /// 是否启用断点续传
        /// </summary>
        public bool EnableCheckpoint { get; set; }

        /// <summary>
        /// 断点续传文件路径
        /// </summary>
        public string CheckpointFilePath { get; set; } = "checkpoint.json";

        /// <summary>
        /// 是否跳过创建表结构
        /// </summary>
        public bool SkipSchemaCreation { get; set; }

        /// <summary>
        /// 是否跳过数据迁移（只迁移表结构）
        /// </summary>
        public bool SkipDataMigration { get; set; }

        /// <summary>
        /// 是否跳过创建外键
        /// </summary>
        public bool SkipForeignKeys { get; set; }

        /// <summary>
        /// 最大并行度
        /// </summary>
        public int MaxDegreeOfParallelism { get; set; } = 4;

        /// <summary>
        /// 失败重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟时间（秒）
        /// </summary>
        public int RetryDelay { get; set; } = 5;

        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingConfig Logging { get; set; } = new()
        {
            Level = "Information",
            FilePath = "migration.log"
        };

        /// <summary>
        /// 类型映射配置
        /// </summary>
        public Dictionary<string, string>? TypeMappings { get; set; }

        /// <summary>
        /// 是否忽略错误继续迁移其他表
        /// </summary>
        public bool IgnoreErrors { get; set; }

        /// <summary>
        /// 是否跳过失败的表，继续迁移其他表
        /// </summary>
        public bool SkipFailedTables { get; set; } = true;

        /// <summary>
        /// 迁移报告文件路径
        /// </summary>
        public string ReportFilePath { get; set; } = "migration_report.txt";
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfig
    {
        /// <summary>
        /// 日志级别（Debug、Information、Warning、Error）
        /// </summary>
        public string Level { get; set; } = "Information";

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string FilePath { get; set; } = "migration.log";

        /// <summary>
        /// 是否输出到控制台
        /// </summary>
        public bool ConsoleOutput { get; set; } = true;
    }
}
