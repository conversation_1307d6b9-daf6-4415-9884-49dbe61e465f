<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.1" />
    <PackageReference Include="System.Threading.Tasks.Dataflow" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TryCode.DatabaseMigration.Checkpoint\TryCode.DatabaseMigration.Checkpoint.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Configuration\TryCode.DatabaseMigration.Configuration.csproj" />
    <ProjectReference Include="..\TryCode.DatabaseMigration.Core\TryCode.DatabaseMigration.Core.csproj" />
  </ItemGroup>

</Project>
