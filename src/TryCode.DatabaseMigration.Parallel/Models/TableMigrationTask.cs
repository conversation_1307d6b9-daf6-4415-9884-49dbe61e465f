using System.Threading;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Parallel.Models
{
    /// <summary>
    /// 表迁移任务
    /// </summary>
    public class TableMigrationTask
    {
        /// <summary>
        /// 表结构
        /// </summary>
        public TableSchema TableSchema { get; set; } = null!;

        /// <summary>
        /// 取消令牌
        /// </summary>
        public CancellationToken CancellationToken { get; set; }

        /// <summary>
        /// 进度回调
        /// </summary>
        public IProgress<MigrationProgress>? Progress { get; set; }
    }
}
