namespace TryCode.DatabaseMigration.Parallel.Models
{
    /// <summary>
    /// 迁移进度
    /// </summary>
    public class MigrationProgress
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string TableName { get; }

        /// <summary>
        /// 当前操作描述
        /// </summary>
        public string Message { get; }
        
        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; }
        
        /// <summary>
        /// 已处理行数
        /// </summary>
        public long ProcessedRows { get; }

        /// <summary>
        /// 初始化进度信息
        /// </summary>
        public MigrationProgress(string tableName, string message, long totalRows, long processedRows)
        {
            TableName = tableName;
            Message = message;
            TotalRows = totalRows;
            ProcessedRows = processedRows;
        }
    }
}
