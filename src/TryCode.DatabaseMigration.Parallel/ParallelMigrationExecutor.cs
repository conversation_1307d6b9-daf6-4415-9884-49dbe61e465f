using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using TryCode.DatabaseMigration.Checkpoint;
using TryCode.DatabaseMigration.Configuration.Models;
using TryCode.DatabaseMigration.Core.Exceptions;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Reports;
using TryCode.DatabaseMigration.Parallel.Models;

namespace TryCode.DatabaseMigration.Parallel
{
    /// <summary>
    /// 并行迁移执行器
    /// </summary>
    public class ParallelMigrationExecutor
    {
        private readonly IDataReader _sourceReader;
        private readonly IDataWriter _targetWriter;
        private readonly ICheckpointManager _checkpointManager;
        private readonly MigrationConfig _config;
        private readonly IProgress<Core.Models.MigrationProgress>? _progress;
        private readonly ILogger _logger;
        private readonly MigrationReportGenerator _reportGenerator;

        public ParallelMigrationExecutor(
            IDataReader sourceReader,
            IDataWriter targetWriter,
            ICheckpointManager checkpointManager,
            MigrationConfig config,
            IProgress<Core.Models.MigrationProgress>? progress = null,
            ILogger? logger = null)
        {
            _sourceReader = sourceReader ?? throw new ArgumentNullException(nameof(sourceReader));
            _targetWriter = targetWriter ?? throw new ArgumentNullException(nameof(targetWriter));
            _checkpointManager = checkpointManager ?? throw new ArgumentNullException(nameof(checkpointManager));
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _progress = progress;

            _logger = logger ?? NullLogger.Instance;
            _reportGenerator = new MigrationReportGenerator(_logger);
        }

        /// <summary>
        /// 执行并行迁移
        /// </summary>
        public async Task ExecuteAsync(CancellationToken cancellationToken = default)
        {
            // 初始化断点信息
            await _checkpointManager.InitializeMigrationCheckpointAsync(0, cancellationToken);

            // 获取要迁移的表
            var allTables = await _sourceReader.GetTableSchemasAsync(cancellationToken);
            _logger.LogInformation("获取到{Count}个表", allTables.Count);

            // 如果配置了为排除的表创建表结构，则更新断点文件中的表信息
            if (_config.CreateSchemaForExcludedTables && _config.ExcludedTables?.Any() == true)
            {
                _logger.LogInformation("正在更新断点文件中的排除表信息...");

                foreach (var tableName in _config.ExcludedTables)
                {
                    var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(tableName, cancellationToken);

                    // 如果表在排除列表中且配置了创建表结构，则将其标记为仅创建结构
                    tableCheckpoint.SchemaOnly = true;
                    await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);

                    _logger.LogInformation("已将表 {TableName} 在断点文件中标记为仅创建结构", tableName);
                }
            }

            // 应用包含/排除规则
            var filteredTables = _config.Tables?.Any() == true
                ? allTables.Where(t => _config.Tables.Contains(t.Name)).ToList()
                : allTables.Where(t => !_config.ExcludedTables.Contains(t.Name)).ToList();

            // 处理分区表：识别主分区表和分区的关系，避免重复迁移
            filteredTables = ProcessPartitionTables(filteredTables);

            // 如果配置了为排除的表创建表结构，则添加这些表到处理列表中
            if (_config.CreateSchemaForExcludedTables && _config.ExcludedTables?.Any() == true)
            {
                var excludedTablesForSchemaOnly = allTables
                    .Where(t => _config.ExcludedTables.Contains(t.Name))
                    .ToList();

                _logger.LogInformation("将为以下排除的表创建表结构（不迁移数据）: {Tables}",
                    string.Join(", ", excludedTablesForSchemaOnly.Select(t => t.Name)));

                // 标记这些表为仅创建结构
                foreach (var table in excludedTablesForSchemaOnly)
                {
                    table.SchemaOnly = true;
                }

                // 将这些表添加到过滤后的表列表中
                filteredTables.AddRange(excludedTablesForSchemaOnly);
            }

            _logger.LogInformation("过滤后剩余{Count}个表", filteredTables.Count);

            var dependencyGraph = new TableDependencyGraph();

            // 创建数据流块 - 减少并行度以避免死锁
            var parallelismDegree = Math.Min(_config.MaxDegreeOfParallelism, 4); // 限制最大并行度为2
            _logger.LogInformation("使用并行度: {Parallelism}", parallelismDegree);

            var options = new ExecutionDataflowBlockOptions
            {
                MaxDegreeOfParallelism = parallelismDegree,
                CancellationToken = cancellationToken
            };

            var createSchemaBlock = new TransformBlock<TableSchema, TableSchema>(
                async table =>
                {
                    try
                    {
                        // 检查断点信息，判断表是否已经创建过
                        var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(table.Name, cancellationToken);

                        // 如果启用了断点续传，并且表已经创建过结构，则跳过表结构创建
                        bool skipTableCreation = _config.EnableCheckpoint && tableCheckpoint.SchemaCreated;

                        // 如果表已经完全迁移完成，则跳过该表的所有操作
                        if (_config.EnableCheckpoint && tableCheckpoint.Status == "Completed")
                        {
                            _logger.LogInformation("表 {TableName} 已经完成迁移，跳过所有操作", table.Name);
                            return table;
                        }

                        // 如果表被标记为仅创建结构，则更新断点信息
                        if (table.SchemaOnly)
                        {
                            _logger.LogInformation("表 {TableName} 被标记为仅创建结构（在排除列表中但配置了创建表结构）", table.Name);
                            tableCheckpoint.SchemaOnly = true;
                            await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
                        }

                        ReportProgress(table.Name, "创建表结构", skipTableCreation ? "表结构已存在，跳过创建" : "开始创建表结构");
                        _reportGenerator.RecordTableStart(table.Name);

                        // 添加重试逻辑
                        int retryCount = 0;
                        int maxRetries = _config.RetryCount > 0 ? _config.RetryCount : 3;
                        int retryDelay = _config.RetryDelay > 0 ? _config.RetryDelay : 5;

                        while (true)
                        {
                            try
                            {
                                // 如果表已经创建过结构，则跳过表结构创建
                                await _targetWriter.CreateTableAsync(table, _config.SkipSchemaCreation || skipTableCreation, cancellationToken);

                                // 更新表结构创建状态
                                if (!tableCheckpoint.SchemaCreated)
                                {
                                    tableCheckpoint.SchemaCreated = true;
                                    await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
                                }

                                break; // 成功则跳出循环
                            }
                            catch (Exception ex) when (ex.Message.Contains("Deadlock") && retryCount < maxRetries)
                            {
                                retryCount++;
                                this._logger.LogWarning("创建表 {TableName} 时发生死锁，正在重试 ({RetryCount}/{MaxRetries})...",
                                    table.Name, retryCount, maxRetries);
                                await Task.Delay(retryDelay * 1000, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                // 记录详细错误信息，包括SQL语法
                                string errorDetails = $"创建表结构时出错: {ex.Message}";
                                if (ex.InnerException != null)
                                {
                                    errorDetails += $" 详细错误: {ex.InnerException.Message}";
                                }
                                await _checkpointManager.MarkTableFailedAsync(
                                    table.Name,
                                    errorDetails,
                                    cancellationToken);

                                this._logger.LogError("表 {TableName} 创建失败: {ErrorDetails}", table.Name, errorDetails);
                                _reportGenerator.RecordTableEnd(table.Name);

                                // 如果配置了忽略错误，则返回null而不是抛出异常
                                if (_config.IgnoreErrors)
                                {
                                    return null;
                                }
                                throw;
                            }
                        }

                        ReportProgress(table.Name, "创建表结构", "表结构创建完成");
                        return table;
                    }
                    catch (Exception ex)
                    {
                        // 记录失败状态
                        string errorDetails = $"创建表结构时出错: {ex.Message}";
                        if (ex.InnerException != null)
                        {
                            errorDetails += $" 详细错误: {ex.InnerException.Message}";
                        }
                        await _checkpointManager.MarkTableFailedAsync(
                            table.Name,
                            errorDetails,
                            cancellationToken);

                        this._logger.LogError("表 {TableName} 创建失败: {ErrorDetails}", table.Name, errorDetails);
                        _reportGenerator.RecordTableEnd(table.Name);

                        // 如果配置了忽略错误，则返回null而不是抛出异常
                        if (_config.IgnoreErrors)
                        {
                            return null;
                        }
                        throw;
                    }
                },
                options);

            var migrateDataBlock = new TransformBlock<TableSchema, TableSchema>(
                async table =>
                {
                    // 如果前一个流程返回了null(表创建失败且忽略错误)，则跳过数据迁移
                    if (table == null) return null;

                    // 获取表的断点信息
                    var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(table.Name, cancellationToken);

                    // 如果配置了只迁移表结构，或者表被标记为仅创建结构，或者断点中标记了表为仅创建结构，则跳过数据迁移
                    if (_config.SkipDataMigration || table.SchemaOnly || tableCheckpoint.SchemaOnly)
                    {
                        string reason = table.SchemaOnly || tableCheckpoint.SchemaOnly
                            ? "表在排除列表中但配置了创建表结构"
                            : "只迁移表结构模式";
                        _logger.LogInformation("表 {TableName} 跳过数据迁移（{Reason}）", table.Name, reason);

                        // // 如果表被标记为仅创建结构，则标记为已完成
                        // if (table.SchemaOnly || tableCheckpoint.SchemaOnly)
                        // {
                        //     await _checkpointManager.MarkTableCompletedAsync(table.Name, cancellationToken);
                        //     ReportProgress(table.Name, "完成", "表结构创建完成（仅创建结构）");
                        // }

                        return table;
                    }

                    try
                    {
                        // 表的断点信息已经在前面获取

                        // 如果表已经完全迁移完成，则跳过数据迁移
                        if (_config.EnableCheckpoint && tableCheckpoint.Status == "Completed")
                        {
                            _logger.LogInformation("表 {TableName} 已经完成迁移，跳过数据迁移", table.Name);
                            return table;
                        }

                        // 检查是否为分区表，如果是则使用分区表迁移逻辑
                        if (table.IsPartitioned && table.Partitions.Count > 0)
                        {
                            _logger.LogInformation("表 {TableName} 是分区表，包含 {PartitionCount} 个分区，使用分区表迁移逻辑",
                                table.Name, table.Partitions.Count);

                            // 标记表检查点为分区表
                            tableCheckpoint.IsPartitioned = true;
                            await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);

                            return await MigratePartitionedTableAsync(table, tableCheckpoint, cancellationToken);
                        }

                        // 如果表已经迁移了部分数据，则从断点处继续迁移
                        long processedRows = 0;
                        if (_config.EnableCheckpoint && tableCheckpoint.MigratedRows > 0)
                        {
                            processedRows = tableCheckpoint.MigratedRows;
                            _logger.LogInformation("表 {TableName} 已迁移 {MigratedRows} 行数据，从断点处继续迁移",
                                table.Name, processedRows);
                            ReportProgress(table.Name, "迁移数据", $"从断点处继续迁移，已完成 {processedRows} 行");
                        }
                        else
                        {
                            ReportProgress(table.Name, "迁移数据", "开始迁移数据");
                        }

                        // 标记表为进行中
                        await _checkpointManager.MarkTableInProgressAsync(table.Name, cancellationToken);

                        // 获取表的总行数
                        long totalRows = 0;
                        try
                        {
                            totalRows = await _sourceReader.GetTableRowCountAsync(
                                table.Name,
                                cancellationToken);

                            _logger.LogInformation("表 {TableName} 总行数: {TotalRows}", table.Name, totalRows);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "获取表 {TableName} 行数失败", table.Name);
                            await _checkpointManager.MarkTableFailedAsync(
                                table.Name,
                                $"获取表行数失败: {ex.Message}",
                                cancellationToken);
                            return table;
                        }

                        // 更新处理进度
                        await _checkpointManager.UpdateTableRowsAsync(
                            table.Name,
                            totalRows,
                            processedRows,
                            "数据迁移",
                            cancellationToken);
                        while (processedRows < totalRows)
                        {
                            // 如果任务已取消，则中断迁移
                            if (cancellationToken.IsCancellationRequested)
                            {
                                break;
                            }

                            var data = await _sourceReader.ReadTableDataAsync(
                                table.Name,
                                _config.Source.BatchSize,
                                processedRows,
                                cancellationToken);

                            var rows = data.ToList();
                            if (!rows.Any()) break;  // 如果没有数据，提前退出

                            // 处理 DBNull 值,增加空值检查
                            foreach (var row in rows)
                            {
                                if (row == null) continue;

                                try
                                {
                                    if (row is IDictionary<string, object> dict)
                                    {
                                        foreach (var key in dict.Keys.ToList())
                                        {
                                            if (key == null) continue;
                                            if (dict[key] is DBNull)
                                            {
                                                dict[key] = null;
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    this._logger.LogWarning($"处理行数据时出错: {ex.Message}");
                                    continue;
                                }
                            }

                            // 添加重试逻辑
                            int retryCount = 0;
                            int maxRetries = _config.RetryCount > 0 ? _config.RetryCount : 3;
                            int retryDelay = _config.RetryDelay > 0 ? _config.RetryDelay : 5;

                            bool success = false;
                            while (!success && retryCount <= maxRetries)
                            {
                                try
                                {
                                    await _targetWriter.BulkWriteAsync(table.Name, rows, cancellationToken);
                                    success = true;
                                }
                                catch (Exception ex) when (ex.Message.Contains("Deadlock") && retryCount < maxRetries)
                                {
                                    retryCount++;
                                    this._logger.LogWarning("迁移表 {TableName} 数据时发生死锁，正在重试 ({RetryCount}/{MaxRetries})...",
                                        table.Name, retryCount, maxRetries);
                                    await Task.Delay(retryDelay * 1000, cancellationToken);
                                }
                                catch (Exception ex)
                                {
                                    this._logger.LogError(ex, "迁移表 {TableName} 数据批次失败，批次大小: {BatchSize}，当前行: {CurrentRow}，总行数: {TotalRows}",
                                        table.Name, rows.Count, processedRows, totalRows);

                                    // 如果出现MySqlException并且错误消息包含唯一键冲突，则记录更详细的信息
                                    if (ex.Message.Contains("Duplicate entry") || ex.Message.Contains("unique constraint"))
                                    {
                                        this._logger.LogError("可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在");
                                        
                                        // 出现唯一键问题，代表此批次已经迁移过了，算作成功
                                        break;
                                    }

                                    throw;
                                }
                            }

                            processedRows += rows.Count;

                            // 更新进度
                            await _checkpointManager.UpdateTableRowsAsync(
                                table.Name,
                                totalRows,
                                processedRows,
                                "数据迁移",
                                cancellationToken);

                            // 更新进度显示
                            _progress?.Report(new Core.Models.MigrationProgress
                            {
                                TableName = table.Name,
                                Status = $"已迁移 {processedRows}/{totalRows} 行",
                                Total = totalRows,
                                Processed = processedRows
                            });

                        }

                        return table;
                    }
                    catch (Exception ex)
                    {
                        // 记录失败状态
                        string errorDetails = $"迁移表 {table.Name} 数据时出错: {ex.Message}";
                        if (ex.InnerException != null)
                        {
                            errorDetails += $" 详细错误: {ex.InnerException.Message}";
                        }
                        await _checkpointManager.MarkTableFailedAsync(
                            table.Name,
                            errorDetails,
                            cancellationToken);

                        this._logger.LogError(errorDetails);
                        _reportGenerator.RecordTableEnd(table.Name);

                        if (_config.IgnoreErrors)
                        {
                            return null;
                        }
                        throw;
                    }
                },
                options);

            // 修改外键约束创建块的执行方式
            var createForeignKeysBlock = new ActionBlock<TableSchema>(
                async table =>
                {
                    // 如果前一个流程返回了null(表创建或数据迁移失败且忽略错误)，则跳过外键创建
                    if (table == null) return;

                    try
                    {
                        // 获取表的断点信息
                        var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(table.Name, cancellationToken);

                        // 如果表已经完全迁移完成，则跳过外键创建
                        if (_config.EnableCheckpoint && tableCheckpoint.Status == "Completed")
                        {
                            _logger.LogInformation("表 {TableName} 已经完成迁移，跳过外键创建", table.Name);
                            return;
                        }

                        // // 如果表被标记为仅创建结构，则跳过外键创建
                        // if (tableCheckpoint.SchemaOnly)
                        // {
                        //     _logger.LogInformation("表 {TableName} 被标记为仅创建结构，跳过外键创建", table.Name);
                        //
                        //     // 标记表迁移完成
                        //     await _checkpointManager.MarkTableCompletedAsync(table.Name, cancellationToken);
                        //     ReportProgress(table.Name, "完成", "表结构创建完成（仅创建结构）");
                        //     _reportGenerator.RecordTableEnd(table.Name);
                        //
                        //     return;
                        // }

                        // 如果表已经创建了外键，则跳过外键创建
                        if (_config.EnableCheckpoint && tableCheckpoint.ForeignKeysCreated)
                        {
                            _logger.LogInformation("表 {TableName} 已经创建了外键，跳过外键创建", table.Name);

                            // 标记表迁移完成
                            await _checkpointManager.MarkTableCompletedAsync(table.Name, cancellationToken);
                            ReportProgress(table.Name, "完成", "表迁移完成");
                            _reportGenerator.RecordTableEnd(table.Name);

                            return;
                        }

                        // 等待一段时间，确保所有表的数据都已经迁移完成
                        await Task.Delay(1000, cancellationToken);

                        ReportProgress(table.Name, "创建外键", "开始创建外键约束");

                        // 添加重试逻辑
                        int retryCount = 0;
                        int maxRetries = _config.RetryCount > 0 ? _config.RetryCount : 3;
                        int retryDelay = _config.RetryDelay > 0 ? _config.RetryDelay : 5;

                        while (true)
                        {
                            try
                            {
                                // 添加空值检查
                                if (table.ForeignKeys != null && table.ForeignKeys.Any())
                                {
                                    await _targetWriter.CreateForeignKeysAsync(table.Name, table.ForeignKeys, cancellationToken);

                                    // 更新外键创建状态
                                    tableCheckpoint.ForeignKeysCreated = true;
                                    await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
                                }
                                else
                                {
                                    // this._logger.LogInformation("表 {TableName} 没有外键需要创建", table.Name);
                                    tableCheckpoint.ForeignKeysCreated = true;
                                    await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
                                }
                                break; // 成功则跳出循环
                            }
                            catch (Exception ex) when (ex.Message.Contains("Deadlock") && retryCount < maxRetries)
                            {
                                retryCount++;
                                this._logger.LogWarning("创建表 {TableName} 外键时发生死锁，正在重试 ({RetryCount}/{MaxRetries})...",
                                    table.Name, retryCount, maxRetries);
                                await Task.Delay(retryDelay * 1000, cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                string errorDetails = $"创建表 {table.Name} 外键时出错: {ex.Message}";
                                if (ex.InnerException != null)
                                {
                                    errorDetails += $" 详细错误: {ex.InnerException.Message}";
                                }

                                await _checkpointManager.MarkTableFailedAsync(
                                    table.Name,
                                    errorDetails,
                                    cancellationToken);

                                this._logger.LogError(errorDetails);
                                _reportGenerator.RecordTableEnd(table.Name);

                                if (_config.IgnoreErrors)
                                {
                                    // 即使外键创建失败，仍然要标记表迁移完成
                                    await _checkpointManager.MarkTableCompletedAsync(table.Name, cancellationToken);
                                    ReportProgress(table.Name, "完成", "表迁移完成，但外键创建失败");
                                    return;
                                }
                                throw;
                            }
                        }

                        ReportProgress(table.Name, "创建外键", "外键约束创建完成");

                        // 标记表迁移完成
                        await _checkpointManager.MarkTableCompletedAsync(table.Name, cancellationToken);
                        ReportProgress(table.Name, "完成", "表迁移完成");
                        _reportGenerator.RecordTableEnd(table.Name);
                    }
                    catch (Exception ex)
                    {
                        string errorDetails = $"创建表 {table.Name} 外键时出错: {ex.Message}";
                        if (ex.InnerException != null)
                        {
                            errorDetails += $" 详细错误: {ex.InnerException.Message}";
                        }

                        await _checkpointManager.MarkTableFailedAsync(
                            table.Name,
                            errorDetails,
                            cancellationToken);

                        if (_config.IgnoreErrors)
                        {
                            // 即使外键创建失败，仍然要标记表为部分完成
                            ReportProgress(table.Name, "部分完成", "表数据迁移完成，但外键创建失败");
                            _reportGenerator.RecordTableEnd(table.Name);
                            return;
                        }
                        throw;
                    }
                },
                new ExecutionDataflowBlockOptions
                {
                    MaxDegreeOfParallelism = 1, // 串行执行外键创建
                    BoundedCapacity = 1,
                    CancellationToken = cancellationToken
                });

            // 连接数据流块 - 不传播异常，让每个任务独立处理自己的异常
            createSchemaBlock.LinkTo(migrateDataBlock, new DataflowLinkOptions { PropagateCompletion = false });
            migrateDataBlock.LinkTo(createForeignKeysBlock, new DataflowLinkOptions { PropagateCompletion = false });

            try
            {
                if (filteredTables == null || !filteredTables.Any())
                {
                    _logger.LogWarning("没有找到需要迁移的表");
                    return;
                }

                // 创建过滤后表名的HashSet，用于快速查找
                var filteredTableNames = new HashSet<string>(filteredTables.Select(t => t.Name));
                _logger.LogInformation("开始处理以下表的迁移: {Tables}", string.Join(", ", filteredTableNames));

                // 按照依赖关系排序表，只添加在过滤表集合中存在的外键依赖
                foreach (var table in filteredTables)
                {
                    foreach (var fk in table.ForeignKeys)
                    {
                        // 只有当被引用的表也在迁移范围内时，才添加依赖关系
                        if (filteredTableNames.Contains(fk.ReferencedTable))
                        {
                            dependencyGraph.AddDependency(table.Name, fk.ReferencedTable);
                        }
                        else
                        {
                            _logger.LogWarning("表 {TableName} 的外键引用了不在迁移范围内的表 {ReferencedTable}，此外键将被忽略",
                                table.Name, fk.ReferencedTable);
                        }
                    }
                }
                var sortedTables = dependencyGraph.TopologicalSort(filteredTables);
                _logger.LogInformation($"表的迁移顺序: {string.Join(", ", sortedTables.Select(t => t.Name))}");

                // 发送表到数据流网络
                foreach (var table in sortedTables)
                {
                    await createSchemaBlock.SendAsync(table, cancellationToken);
                }

                createSchemaBlock.Complete();

                // 等待表结构创建完成
                await createSchemaBlock.Completion;
                _logger.LogInformation("所有表结构已创建完成，等待数据迁移完成...");

                // 手动完成数据迁移块
                migrateDataBlock.Complete();
                await migrateDataBlock.Completion;
                _logger.LogInformation("数据迁移完成，等待外键创建完成...");

                // 手动完成外键创建块
                createForeignKeysBlock.Complete();
                await createForeignKeysBlock.Completion;
                _logger.LogInformation("数据迁移和外键创建全部完成");
            }
            catch (Exception ex)
            {
                // 只标记整体迁移失败，不要将错误传播到所有表
                // 具体的表错误应该在各自的处理逻辑中单独处理
                _logger.LogError(ex, "迁移过程中发生未处理的异常: {ErrorMessage}", ex.Message);
                throw;
            }
            finally
            {
                // 生成迁移报告
                if (!string.IsNullOrEmpty(_config.ReportFilePath))
                {
                    var checkpoint = await _checkpointManager.GetMigrationCheckpointAsync(cancellationToken);
                    await _reportGenerator.GenerateReportAsync(checkpoint, _config.ReportFilePath, cancellationToken);
                }
            }
        }

        /// <summary>
        /// 报告进度
        /// </summary>
        private void ReportProgress(
            string tableName,
            string stage,
            string status,
            long totalRows = 0,
            long migratedRows = 0)
        {
            _progress?.Report(new Core.Models.MigrationProgress
            {
                TableName = tableName,
                Stage = stage,
                Status = status,
                Total = totalRows,
                Processed = migratedRows
            });
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        private async Task<TableSchema> CreateTableSchemaAsync(
            TableSchema table,
            CancellationToken cancellationToken)
        {
            var tableName = table.Name;

            try
            {
                // 标记表迁移进行中
                await _checkpointManager.MarkTableInProgressAsync(tableName, cancellationToken);

                _logger.LogInformation("开始为表 {TableName} 创建表结构", tableName);
                await _targetWriter.CreateTableAsync(table, _config.SkipSchemaCreation, cancellationToken);
                _logger.LogInformation("成功创建表 {TableName} 的表结构", tableName);

                return table;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建表 {TableName} 结构失败: {ErrorMessage}", tableName, ex.Message);

                // 根据配置决定是否跳过失败的表
                if (_config.SkipFailedTables)
                {
                    _logger.LogWarning("已跳过创建表 {TableName} 的表结构，将继续处理其他表", tableName);
                    await _checkpointManager.MarkTableSkippedAsync(
                        tableName,
                        $"创建表结构失败: {ex.Message.Substring(0, Math.Min(ex.Message.Length, 500))}...",
                        cancellationToken);

                    // 返回表，但标记为已跳过
                    return table;
                }
                else
                {
                    // 如果不跳过，则标记为失败并抛出异常中断迁移
                    await _checkpointManager.MarkTableFailedAsync(
                        tableName,
                        $"创建表结构失败: {ex.Message}",
                        cancellationToken);
                    throw;
                }
            }
        }

        /// <summary>
        /// 迁移表数据
        /// </summary>
        private async Task<TableSchema> MigrateTableDataAsync(
            TableSchema table,
            CancellationToken cancellationToken)
        {
            var tableName = table.Name;

            try
            {
                _logger.LogInformation("开始为表 {TableName} 迁移数据", tableName);

                // 获取记录数
                long totalRows;
                try
                {
                    totalRows = await _sourceReader.GetTableRowCountAsync(tableName, cancellationToken);
                    _logger.LogInformation("表 {TableName} 共有 {RowCount} 行数据需要迁移", tableName, totalRows);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取表 {TableName} 行数失败: {ErrorMessage}", tableName, ex.Message);

                    // 根据配置决定是否跳过失败的表
                    if (_config.SkipFailedTables)
                    {
                        _logger.LogWarning("已跳过迁移表 {TableName} 的数据，将继续处理其他表", tableName);
                        await _checkpointManager.MarkTableSkippedAsync(
                            tableName,
                            $"获取表行数失败: {ex.Message.Substring(0, Math.Min(ex.Message.Length, 500))}...",
                            cancellationToken);
                        return table;
                    }
                    else
                    {
                        await _checkpointManager.MarkTableFailedAsync(
                            table.Name,
                            $"获取表行数失败: {ex.Message}",
                            cancellationToken);
                        throw;
                    }
                }

                // 更新处理进度
                await _checkpointManager.UpdateTableRowsAsync(
                    table.Name,
                    totalRows,
                    0,
                    "数据迁移",
                    cancellationToken);

                // 分批读取和写入数据
                long processedRows = 0;
                while (processedRows < totalRows)
                {
                    // 如果任务已取消，则中断迁移
                    if (cancellationToken.IsCancellationRequested)
                    {
                        break;
                    }

                    // 动态调整批处理大小，对于大表使用较小的批处理大小以避免超时
                    int batchSize = _config.Source.BatchSize;
                    if (totalRows > 100000) // 对于超过10万行的大表
                    {
                        batchSize = Math.Min(batchSize, 500); // 限制批处理大小不超过500
                        _logger.LogInformation("表 {TableName} 是大表（{TotalRows}行），使用较小的批处理大小：{BatchSize}",
                            table.Name, totalRows, batchSize);
                    }

                    var data = await _sourceReader.ReadTableDataAsync(
                        table.Name,
                        batchSize,
                        processedRows,
                        cancellationToken);

                    var rows = data.ToList();
                    if (!rows.Any()) break;  // 如果没有数据，提前退出

                    // 处理 DBNull 值,增加空值检查
                    foreach (var row in rows)
                    {
                        if (row == null) continue;

                        try
                        {
                            if (row is IDictionary<string, object> dict)
                            {
                                foreach (var key in dict.Keys.ToList())
                                {
                                    if (key == null) continue;
                                    if (dict[key] is DBNull)
                                    {
                                        dict[key] = null;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            this._logger.LogWarning($"处理行数据时出错: {ex.Message}");
                            continue;
                        }
                    }

                    // 添加重试逻辑
                    int retryCount = 0;
                    int maxRetries = _config.RetryCount > 0 ? _config.RetryCount : 3;
                    int retryDelay = _config.RetryDelay > 0 ? _config.RetryDelay : 5;

                    bool success = false;
                    while (!success && retryCount <= maxRetries)
                    {
                        try
                        {
                            await _targetWriter.BulkWriteAsync(table.Name, rows, cancellationToken);
                            success = true;
                        }
                        catch (Exception ex) when (ex.Message.Contains("Deadlock") && retryCount < maxRetries)
                        {
                            retryCount++;
                            this._logger.LogWarning("迁移表 {TableName} 数据时发生死锁，正在重试 ({RetryCount}/{MaxRetries})...",
                                table.Name, retryCount, maxRetries);
                            await Task.Delay(retryDelay * 1000, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            this._logger.LogError(ex, "迁移表 {TableName} 数据批次失败，批次大小: {BatchSize}，当前行: {CurrentRow}，总行数: {TotalRows}",
                                table.Name, rows.Count, processedRows, totalRows);

                            // 如果出现MySqlException并且错误消息包含唯一键冲突，则记录更详细的信息
                            if (ex.Message.Contains("Duplicate entry") || ex.Message.Contains("unique constraint"))
                            {
                                this._logger.LogError("检测到主键冲突，这可能是由于ORDER BY不稳定导致的重复数据读取");
                                throw new InvalidOperationException($"表 {table.Name} 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", ex);
                            }

                            throw;
                        }
                    }

                    processedRows += rows.Count;

                    // 更新进度
                    await _checkpointManager.UpdateTableRowsAsync(
                        table.Name,
                        totalRows,
                        processedRows,
                        "数据迁移",
                        cancellationToken);

                    // 更新进度显示
                    _progress?.Report(new Core.Models.MigrationProgress
                    {
                        TableName = table.Name,
                        Status = $"已迁移 {processedRows}/{totalRows} 行",
                        Total = totalRows,
                        Processed = processedRows
                    });

                }

                return table;
            }
            catch (Exception ex)
            {
                // 记录失败状态
                string errorDetails = $"迁移表 {table.Name} 数据时出错: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorDetails += $" 详细错误: {ex.InnerException.Message}";
                }
                await _checkpointManager.MarkTableFailedAsync(
                    table.Name,
                    errorDetails,
                    cancellationToken);

                this._logger.LogError(errorDetails);
                _reportGenerator.RecordTableEnd(table.Name);

                if (_config.IgnoreErrors)
                {
                    return null;
                }
                throw;
            }
        }

        /// <summary>
        /// 迁移分区表数据
        /// </summary>
        private async Task<TableSchema> MigratePartitionedTableAsync(
            TableSchema table,
            Checkpoint.Models.TableCheckpoint tableCheckpoint,
            CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始迁移分区表 {TableName}，共 {PartitionCount} 个分区",
                    table.Name, table.Partitions.Count);

                // 标记表开始数据迁移
                await _checkpointManager.MarkTableInProgressAsync(table.Name, cancellationToken);

                long totalTableRows = 0;
                long totalMigratedRows = 0;

                // 获取分区数据源信息
                var partitionDataSources = GetPartitionDataSources(table);

                _logger.LogInformation("分区表 {TableName} 包含 {SourceCount} 个数据源",
                    table.Name, partitionDataSources.Count);

                // 遍历每个分区数据源进行迁移
                foreach (var partitionSource in partitionDataSources)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    _logger.LogInformation("开始迁移分区数据源 {PartitionName}（表 {TableName}）",
                        partitionSource.Name, table.Name);

                    // 获取分区的检查点信息
                    var partitionCheckpoint = await ((CheckpointManagerAdapter)_checkpointManager)
                        .GetPartitionCheckpointAsync(table.Name, partitionSource.Name, cancellationToken);

                    // 如果分区已经完成迁移，跳过
                    if (partitionCheckpoint.Status == "Completed")
                    {
                        _logger.LogInformation("分区数据源 {PartitionName} 已完成迁移，跳过", partitionSource.Name);
                        totalTableRows += partitionCheckpoint.TotalRows;
                        totalMigratedRows += partitionCheckpoint.MigratedRows;
                        continue;
                    }

                    // 获取分区的总行数（使用普通表查询，因为这些是独立的表）
                    long partitionTotalRows = await _sourceReader.GetTableRowCountAsync(partitionSource.Name, cancellationToken);
                    partitionCheckpoint.TotalRows = partitionTotalRows;
                    totalTableRows += partitionTotalRows;

                    _logger.LogInformation("分区数据源 {PartitionName} 共有 {RowCount} 行数据",
                        partitionSource.Name, partitionTotalRows);

                    // 如果分区没有数据，标记为完成
                    if (partitionTotalRows == 0)
                    {
                        await ((CheckpointManagerAdapter)_checkpointManager)
                            .MarkPartitionCompletedAsync(table.Name, partitionSource.Name, cancellationToken);
                        continue;
                    }

                    // 标记分区开始迁移
                    partitionCheckpoint.Status = "InProgress";
                    partitionCheckpoint.StartTime = DateTime.Now;
                    await ((CheckpointManagerAdapter)_checkpointManager)
                        .UpdatePartitionCheckpointAsync(table.Name, partitionCheckpoint, cancellationToken);

                    // 从断点处继续迁移
                    long partitionProcessedRows = partitionCheckpoint.MigratedRows;

                    // 分批迁移分区数据
                    while (partitionProcessedRows < partitionTotalRows)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;

                        // 动态调整批处理大小
                        int batchSize = _config.Source.BatchSize;
                        if (partitionTotalRows > 100000)
                        {
                            batchSize = Math.Min(batchSize, 500);
                        }

                        // 从分区数据源读取数据（使用普通表读取，因为这些是独立的表）
                        var data = await _sourceReader.ReadTableDataAsync(
                            partitionSource.Name,
                            batchSize,
                            partitionProcessedRows,
                            cancellationToken);

                        var rows = data.ToList();
                        if (rows.Count == 0)
                            break;

                        try
                        {
                            // 将数据写入目标数据库的主表（让目标数据库自动分区）
                            await _targetWriter.BulkWriteAsync(table.Name, rows, cancellationToken);

                            partitionProcessedRows += rows.Count;
                            totalMigratedRows += rows.Count;

                            // 更新分区检查点
                            partitionCheckpoint.MigratedRows = partitionProcessedRows;
                            await ((CheckpointManagerAdapter)_checkpointManager)
                                .UpdatePartitionCheckpointAsync(table.Name, partitionCheckpoint, cancellationToken);

                            // 更新进度显示
                            _progress?.Report(new Core.Models.MigrationProgress
                            {
                                TableName = $"{table.Name}[{partitionSource.Name}]",
                                Status = $"已迁移 {partitionProcessedRows}/{partitionTotalRows} 行",
                                Total = partitionTotalRows,
                                Processed = partitionProcessedRows
                            });

                            _logger.LogDebug("分区数据源 {PartitionName} 已迁移 {ProcessedRows}/{TotalRows} 行",
                                partitionSource.Name, partitionProcessedRows, partitionTotalRows);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "迁移分区数据源 {PartitionName} 数据批次失败，批次大小: {BatchSize}，当前行: {CurrentRow}，总行数: {TotalRows}",
                                partitionSource.Name, rows.Count, partitionProcessedRows, partitionTotalRows);

                            // 处理唯一键冲突
                            if (ex.Message.Contains("Duplicate entry") || ex.Message.Contains("unique constraint"))
                            {
                                _logger.LogError("分区 {PartitionName} 检测到主键冲突，这可能是由于ORDER BY不稳定导致的重复数据读取", partitionSource.Name);
                                throw new InvalidOperationException($"分区 {partitionSource.Name} 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", ex);
                            }

                            throw;
                        }
                    }

                    // 标记分区迁移完成
                    await ((CheckpointManagerAdapter)_checkpointManager)
                        .MarkPartitionCompletedAsync(table.Name, partitionSource.Name, cancellationToken);

                    _logger.LogInformation("分区数据源 {PartitionName} 迁移完成，共迁移 {MigratedRows} 行",
                        partitionSource.Name, partitionProcessedRows);
                }

                // 标记表迁移完成
                await _checkpointManager.MarkTableCompletedAsync(table.Name, cancellationToken);

                _logger.LogInformation("分区表 {TableName} 迁移完成，共迁移 {TotalMigratedRows}/{TotalRows} 行",
                    table.Name, totalMigratedRows, totalTableRows);

                return table;
            }
            catch (Exception ex)
            {
                string errorDetails = $"迁移分区表 {table.Name} 时出错: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorDetails += $" 详细错误: {ex.InnerException.Message}";
                }

                await _checkpointManager.MarkTableFailedAsync(table.Name, errorDetails, cancellationToken);
                _logger.LogError(errorDetails);

                if (_config.IgnoreErrors)
                {
                    return null;
                }
                throw;
            }
        }



        /// <summary>
        /// 处理分区表：识别主分区表和分区的关系，避免重复迁移
        /// </summary>
        private List<TableSchema> ProcessPartitionTables(List<TableSchema> tables)
        {
            var result = new List<TableSchema>();
            var processedTables = new HashSet<string>();

            // 按表名排序，确保主表在分区之前处理
            var sortedTables = tables.OrderBy(t => t.Name).ToList();

            foreach (var table in sortedTables)
            {
                if (processedTables.Contains(table.Name))
                    continue;

                // 检查是否是分区表的主表
                if (table.IsPartitioned && table.Partitions.Count > 0)
                {
                    _logger.LogInformation("发现分区表 {TableName}，包含 {PartitionCount} 个分区",
                        table.Name, table.Partitions.Count);

                    // 收集所有相关的分区表数据
                    var allPartitionData = new List<TableSchema>();
                    allPartitionData.Add(table); // 主表

                    // 查找所有分区表
                    foreach (var partition in table.Partitions)
                    {
                        var partitionTable = tables.FirstOrDefault(t => t.Name == partition.Name);
                        if (partitionTable != null)
                        {
                            allPartitionData.Add(partitionTable);
                            processedTables.Add(partitionTable.Name);
                            _logger.LogDebug("找到分区表 {PartitionName}，将合并到主表 {MainTable}",
                                partition.Name, table.Name);
                        }
                    }

                    // 创建合并后的分区表结构
                    var mergedTable = CreateMergedPartitionTable(table, allPartitionData);
                    result.Add(mergedTable);
                    processedTables.Add(table.Name);

                    _logger.LogInformation("分区表 {TableName} 处理完成，将作为单个分区表迁移", table.Name);
                }
                else
                {
                    // 检查是否是某个分区表的分区
                    bool isPartitionOfAnotherTable = sortedTables.Any(t =>
                        t.IsPartitioned &&
                        t.Partitions.Any(p => p.Name == table.Name));

                    if (!isPartitionOfAnotherTable)
                    {
                        // 普通表，直接添加
                        result.Add(table);
                        processedTables.Add(table.Name);
                    }
                    else
                    {
                        _logger.LogDebug("表 {TableName} 是分区表的分区，将被合并到主表中", table.Name);
                        processedTables.Add(table.Name);
                    }
                }
            }

            _logger.LogInformation("分区表处理完成，原始表数: {OriginalCount}，处理后表数: {ProcessedCount}",
                tables.Count, result.Count);

            return result;
        }

        /// <summary>
        /// 创建合并后的分区表结构
        /// </summary>
        private TableSchema CreateMergedPartitionTable(TableSchema mainTable, List<TableSchema> allPartitionData)
        {
            var mergedTable = new TableSchema
            {
                Name = mainTable.Name,
                Columns = mainTable.Columns,
                ForeignKeys = mainTable.ForeignKeys,
                PrimaryKeys = mainTable.PrimaryKeys,
                IsPartitioned = true,
                PartitionKey = mainTable.PartitionKey,
                PartitionKeys = mainTable.PartitionKeys,
                PartitionType = mainTable.PartitionType,
                PartitionCount = mainTable.PartitionCount,
                Partitions = mainTable.Partitions,
                AdditionalInfo = mainTable.AdditionalInfo
            };

            // 计算总行数（所有分区的行数之和）
            mergedTable.EstimatedRowCount = allPartitionData.Sum(t => t.EstimatedRowCount);

            // 添加分区数据信息到AdditionalInfo中，供后续迁移使用
            var partitionDataInfo = allPartitionData.Where(t => t.Name != mainTable.Name)
                .Select(t => new Dictionary<string, object>
                {
                    ["Name"] = t.Name,
                    ["RowCount"] = t.EstimatedRowCount
                })
                .ToList();

            mergedTable.AdditionalInfo["PartitionDataSources"] = partitionDataInfo;

            // 根据实际的分区数据源数量设置分区数量
            mergedTable.PartitionCount = partitionDataInfo.Count;

            _logger.LogInformation("设置分区表 {TableName} 的分区数量为 {PartitionCount}（基于实际分区数据源数量）",
                mergedTable.Name, mergedTable.PartitionCount);

            _logger.LogInformation("合并分区表 {TableName}，总行数: {TotalRows}，分区数据源: {PartitionSources}",
                mergedTable.Name, mergedTable.EstimatedRowCount,
                string.Join(", ", partitionDataInfo.Select(p => $"{p["Name"]}({p["RowCount"]})")));

            return mergedTable;
        }

        /// <summary>
        /// 获取分区数据源信息
        /// </summary>
        private List<(string Name, long RowCount)> GetPartitionDataSources(TableSchema table)
        {
            var dataSources = new List<(string Name, long RowCount)>();

            // 从AdditionalInfo中获取分区数据源信息
            if (table.AdditionalInfo.TryGetValue("PartitionDataSources", out var partitionDataSourcesObj))
            {
                if (partitionDataSourcesObj is List<object> partitionDataSourcesList)
                {
                    foreach (var item in partitionDataSourcesList)
                    {
                        if (item is Dictionary<string, object> partitionInfo)
                        {
                            var name = partitionInfo.TryGetValue("Name", out var nameObj) ? nameObj?.ToString() : null;
                            var rowCount = partitionInfo.TryGetValue("RowCount", out var rowCountObj) &&
                                         long.TryParse(rowCountObj?.ToString(), out var rc) ? rc : 0;

                            if (!string.IsNullOrEmpty(name))
                            {
                                dataSources.Add((name, rowCount));
                            }
                        }
                    }
                }
            }

            // 如果没有找到分区数据源信息，使用原始的分区定义
            if (dataSources.Count == 0 && table.Partitions.Count > 0)
            {
                foreach (var partition in table.Partitions)
                {
                    dataSources.Add((partition.Name, 0)); // 行数将在迁移时重新计算
                }
            }

            return dataSources;
        }

        /// <summary>
        /// 检查表是否在配置中被明确指定
        /// </summary>
        private bool IsTableExplicitlyConfigured(string tableName)
        {
            // 如果配置了Tables列表且表名在其中，则认为是明确配置的
            if (_config.Tables?.Any() == true)
            {
                return _config.Tables.Contains(tableName);
            }

            // 如果没有配置Tables列表，则检查是否在排除列表中
            // 不在排除列表中的表都会被迁移，但不算明确配置
            return false;
        }
    }
}
