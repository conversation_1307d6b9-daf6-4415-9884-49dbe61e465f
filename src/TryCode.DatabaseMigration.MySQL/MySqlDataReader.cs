using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.Core.DataReaders;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.MySQL.Extensions;

namespace TryCode.DatabaseMigration.MySQL
{
    /// <summary>
    /// MySQL数据读取器
    /// </summary>
    public class MySqlDataReader : DataReaderBase, IDbConnectionProvider
    {
        private readonly string _databaseName;

        /// <summary>
        /// 创建MySQL数据读取器实例
        /// </summary>
        public MySqlDataReader(string connectionString, ILogger<MySqlDataReader>? logger = null, 
            int maxRetries = 3, int retryDelaySeconds = 5)
            : base(connectionString, logger, maxRetries, retryDelaySeconds)
        {
            _databaseName = new MySqlConnectionStringBuilder(connectionString).Database;
            Logger.LogDebug("MySQL数据读取器已初始化，数据库: {Database}", _databaseName);
        }

        public DbConnection GetConnection()
        {
            return new MySqlConnection(ConnectionString);
        }

        public override async Task<TableSchema> GetTableSchemaAsync(string tableName, 
            IEnumerable<string>? excludedColumns = null, CancellationToken cancellationToken = default)
        {
            ValidateTableName(tableName);

            Logger.LogDebug("开始获取表 {TableName} 的结构信息", tableName);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var schema = new TableSchema { Name = tableName };
                var excludedColumnsList = excludedColumns?.ToList() ?? new List<string>();

                // 加载列信息
                await connection.LoadColumnsInfoAsync(schema, excludedColumnsList, Logger);
                
                // 加载主键和外键信息
                await connection.LoadKeysInfoAsync(schema, _databaseName);

                // 获取表的预计行数
                var rowCount = await connection.ExecuteScalarAsync<long>(
                    MySqlDataReaderExtensions.SQL_GET_ESTIMATED_ROW_COUNT,
                    new { tableName });

                schema.EstimatedRowCount = rowCount;

                Logger.LogDebug("表 {TableName} 结构信息获取完成，共有 {ColumnCount} 列，{PrimaryKeyCount} 个主键",
                    tableName, schema.Columns.Count, schema.PrimaryKeys.Count);

                return schema;
            }, $"获取表{tableName}的结构信息", cancellationToken);
        }

        public override async Task<List<TableSchema>> GetTableSchemasAsync(CancellationToken cancellationToken = default)
        {
            Logger.LogInformation("开始获取数据库中所有表的结构信息");

            return await ExecuteWithRetryAsync(async () =>
            {
                var schemas = new List<TableSchema>();
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                // 获取所有表
                var tables = await connection.QueryAsync<string>(
                    MySqlDataReaderExtensions.SQL_GET_ALL_TABLES,
                    new { database = _databaseName });

                foreach (var tableName in tables)
                {
                    var schema = await GetTableSchemaAsync(tableName, null, cancellationToken);
                    schemas.Add(schema);
                }

                Logger.LogInformation("成功获取了 {TableCount} 个表的结构信息", schemas.Count);
                return schemas;
            }, "获取所有表的结构信息", cancellationToken);
        }

        public override async Task<IEnumerable<dynamic>> ReadTableDataAsync(string tableName, int pageSize, long offset, 
            CancellationToken cancellationToken = default)
        {
            ValidateTableName(tableName);
            ValidatePaginationParams(pageSize, offset);

            Logger.LogDebug("开始读取表 {TableName} 数据，页大小: {PageSize}, 偏移量: {Offset}", 
                tableName, pageSize, offset);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var sql = $"SELECT * FROM `{tableName}` LIMIT {offset}, {pageSize}";
                var data = await connection.QueryAsync(sql);

                Logger.LogDebug("成功从表 {TableName} 读取了 {Count} 行数据", tableName, data.Count());
                return data;
            }, $"从表{tableName}读取数据", cancellationToken);
        }

        public override async Task<long> GetTableRowCountAsync(string tableName, CancellationToken cancellationToken = default)
        {
            ValidateTableName(tableName);

            Logger.LogDebug("开始获取表 {TableName} 的总行数", tableName);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var sql = $"SELECT COUNT(*) FROM `{tableName}`";
                var count = await connection.ExecuteScalarAsync<long>(sql);

                Logger.LogDebug("表 {TableName} 共有 {RowCount} 行数据", tableName, count);
                return count;
            }, $"获取表{tableName}的总行数", cancellationToken);
        }

        public override async Task<IEnumerable<dynamic>> ReadPartitionDataAsync(string partitionName, int pageSize, long offset,
            CancellationToken cancellationToken = default)
        {
            ValidateTableName(partitionName);
            ValidatePaginationParams(pageSize, offset);

            Logger.LogDebug("开始读取分区 {PartitionName} 数据，页大小: {PageSize}, 偏移量: {Offset}",
                partitionName, pageSize, offset);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                // 对于MySQL分区表，直接查询分区名即可
                var sql = $"SELECT * FROM `{partitionName}` LIMIT {offset}, {pageSize}";
                var data = await connection.QueryAsync(sql);

                Logger.LogDebug("成功从分区 {PartitionName} 读取了 {Count} 行数据", partitionName, data.Count());
                return data;
            }, $"从分区{partitionName}读取数据", cancellationToken);
        }

        public override async Task<long> GetPartitionRowCountAsync(string partitionName, CancellationToken cancellationToken = default)
        {
            ValidateTableName(partitionName);

            Logger.LogDebug("开始获取分区 {PartitionName} 的总行数", partitionName);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var sql = $"SELECT COUNT(*) FROM `{partitionName}`";
                var count = await connection.ExecuteScalarAsync<long>(sql);

                Logger.LogDebug("分区 {PartitionName} 共有 {RowCount} 行数据", partitionName, count);
                return count;
            }, $"获取分区{partitionName}的总行数", cancellationToken);
        }
    }
}
