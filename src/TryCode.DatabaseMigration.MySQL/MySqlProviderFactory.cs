using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.MySQL.TypeConverters;

namespace TryCode.DatabaseMigration.MySQL
{
    /// <summary>
    /// MySQL数据库提供程序工厂
    /// </summary>
    public class MySqlProviderFactory : IDatabaseProviderFactory
    {
        private readonly string _connectionString;
        private readonly Dictionary<string, string>? _typeMappings;
        private readonly ILogger<MySqlDataWriter>? _logger;

        public MySqlProviderFactory(string connectionString, ILogger<MySqlDataWriter>? logger = null, Dictionary<string, string>? typeMappings = null)
        {
            _connectionString = connectionString;
            _logger = logger ?? NullLogger<MySqlDataWriter>.Instance;
            _typeMappings = typeMappings;
        }

        public IDataReader CreateReader()
        {
            return new MySqlDataReader(_connectionString);
        }

        public IDataWriter CreateWriter()
        {
            return new MySqlDataWriter(_connectionString, _logger,_typeMappings);
        }

        public ITypeConverter CreateTypeConverter()
        {
            return new MySqlTypeConverter();
        }
    }
}
