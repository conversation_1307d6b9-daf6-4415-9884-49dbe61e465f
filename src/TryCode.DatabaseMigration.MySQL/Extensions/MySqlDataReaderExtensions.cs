using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using TryCode.DatabaseMigration.Core.Models;
using Microsoft.Extensions.Logging;

namespace TryCode.DatabaseMigration.MySQL.Extensions
{
    /// <summary>
    /// MySQL数据读取器扩展方法
    /// </summary>
    public static class MySqlDataReaderExtensions
    {
        #region SQL查询常量

        public const string SQL_GET_TABLE_COLUMNS = @"
            SELECT 
                c.COLUMN_NAME,
                c.DATA_TYPE,
                c.IS_NULLABLE,
                c.COLUMN_DEFAULT,
                c.CHARACTER_MAXIMUM_LENGTH,
                c.NUMERIC_PRECISION,
                c.NUMERIC_SCALE,
                c.EXTRA,
                CASE WHEN k.COLUMN_NAME IS NOT NULL THEN true ELSE false END as IS_PRIMARY_KEY
            FROM INFORMATION_SCHEMA.COLUMNS c
            LEFT JOIN (
                SELECT k.COLUMN_NAME
                FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS t
                JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE k
                     ON t.CONSTRAINT_NAME = k.CONSTRAINT_NAME AND t.TABLE_SCHEMA = k.TABLE_SCHEMA AND t.TABLE_NAME = k.TABLE_NAME
                WHERE t.CONSTRAINT_TYPE = 'PRIMARY KEY'
                    AND t.TABLE_NAME = @tableName
                    AND t.TABLE_SCHEMA = DATABASE()
            ) k ON c.COLUMN_NAME = k.COLUMN_NAME
            WHERE c.TABLE_NAME = @tableName
                AND c.TABLE_SCHEMA = DATABASE()
            ORDER BY ORDINAL_POSITION";

        public const string SQL_GET_PRIMARY_KEYS = @"
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = @database 
            AND TABLE_NAME = @table
            AND CONSTRAINT_NAME = 'PRIMARY'
            ORDER BY ORDINAL_POSITION";

        public const string SQL_GET_FOREIGN_KEYS = @"
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = @database 
            AND TABLE_NAME = @table
            AND REFERENCED_TABLE_NAME IS NOT NULL";

        public const string SQL_GET_ALL_TABLES = @"
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = @database 
            AND TABLE_TYPE = 'BASE TABLE'";

        public const string SQL_GET_ESTIMATED_ROW_COUNT = @"
            SELECT TABLE_ROWS
            FROM INFORMATION_SCHEMA.TABLES
            WHERE table_schema = DATABASE()
                AND table_name = @tableName";

        #endregion

        /// <summary>
        /// 加载表的列信息
        /// </summary>
        public static async Task LoadColumnsInfoAsync(this DbConnection connection, TableSchema schema, 
            IList<string> excludedColumns, ILogger logger)
        {
            var columns = await connection.QueryAsync<dynamic>(
                SQL_GET_TABLE_COLUMNS,
                new { tableName = schema.Name });

            foreach (var col in columns)
            {
                string columnName = col.COLUMN_NAME.ToString();
                if (excludedColumns.Contains(columnName))
                    continue;

                var column = CreateColumnSchema(col);
                schema.Columns.Add(column);
                if (col.IS_PRIMARY_KEY == 1)
                {
                    schema.AddPrimaryKey(column.Name);
                }
            }
        }

        /// <summary>
        /// 加载表的主键和外键信息
        /// </summary>
        public static async Task LoadKeysInfoAsync(this DbConnection connection, TableSchema schema, string databaseName)
        {
            // 获取主键信息
            var primaryKeys = await connection.QueryAsync<string>(
                SQL_GET_PRIMARY_KEYS,
                new { database = databaseName, table = schema.Name });

            schema.AddPrimaryKeys(primaryKeys);

            // 获取外键信息
            var foreignKeys = await connection.QueryAsync<dynamic>(
                SQL_GET_FOREIGN_KEYS,
                new { database = databaseName, table = schema.Name });

            foreach (var fk in foreignKeys)
            {
                schema.ForeignKeys.Add(new ForeignKeySchema
                {
                    Name = fk.CONSTRAINT_NAME,
                    ColumnName = fk.COLUMN_NAME,
                    ReferencedTable = fk.REFERENCED_TABLE_NAME,
                    ReferencedColumn = fk.REFERENCED_COLUMN_NAME
                });
            }
        }

        /// <summary>
        /// 创建列Schema对象
        /// </summary>
        public static ColumnSchema CreateColumnSchema(dynamic column)
        {
            return new ColumnSchema
            {
                Name = column.COLUMN_NAME,
                DataType = column.DATA_TYPE,
                IsNullable = column.IS_NULLABLE == "YES",
                DefaultValue = column.COLUMN_DEFAULT?.ToString(),
                MaxLength = column.CHARACTER_MAXIMUM_LENGTH != null
                    ? (int?)(long)Convert.ToUInt64(column.CHARACTER_MAXIMUM_LENGTH)
                    : null,
                NumericPrecision = column.NUMERIC_PRECISION != null
                    ? (int?)(long)Convert.ToUInt64(column.NUMERIC_PRECISION)
                    : null,
                Scale = column.NUMERIC_SCALE != null
                    ? (int?)(long)Convert.ToUInt64(column.NUMERIC_SCALE)
                    : null,
                IsAutoIncrement = column.EXTRA?.ToString().Contains("auto_increment") ?? false,
                IsBoolean = column.DATA_TYPE == "TINYINT" && column.NUMERIC_PRECISION == 1,
                IsPrimaryKey = column.IS_PRIMARY_KEY == 1
            };
        }
    }
}
