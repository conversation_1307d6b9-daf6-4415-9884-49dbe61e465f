using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;
using TryCode.DatabaseMigration.MySQL.Writers;

namespace TryCode.DatabaseMigration.MySQL
{
    /// <summary>
    /// MySQL数据写入实现
    /// </summary>
    public class MySqlDataWriter : BaseDataWriter, IDataWriter
    {
        private readonly MySqlTableBuilder _tableBuilder;
        private readonly MySqlBulkWriter _bulkWriter;
        private readonly MySqlForeignKeyManager _foreignKeyManager;
        private readonly MySqlDataTypeConverter _typeConverter;

        /// <summary>
        /// 初始化MySQL数据写入器
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="typeMappings"></param>
        /// <param name="batchSize">批处理大小</param>
        public MySqlDataWriter(string connectionString, ILogger<MySqlDataWriter> logger, Dictionary<string, string>? typeMappings = null, int batchSize = DefaultBatchSize)
            : base(connectionString, logger, batchSize)
        {
            _typeConverter = new MySqlDataTypeConverter(logger,typeMappings);
            _tableBuilder = new MySqlTableBuilder(connectionString, logger, _typeConverter);
            _bulkWriter = new MySqlBulkWriter(connectionString, logger, batchSize, _typeConverter);
            _foreignKeyManager = new MySqlForeignKeyManager(connectionString, logger);
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <param name="skipSchemaCreation">是否跳过创建表结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override Task CreateTableAsync(TableSchema schema, bool skipSchemaCreation = false, CancellationToken cancellationToken = default)
        {
            if (skipSchemaCreation)
                return Task.CompletedTask;
            return _tableBuilder.CreateTableAsync(schema, skipSchemaCreation, cancellationToken);
        }

        /// <summary>
        /// 批量写入数据
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="data">数据行集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override Task BulkWriteAsync(string tableName, IEnumerable<dynamic> data, CancellationToken cancellationToken = default)
        {
            return _bulkWriter.BulkWriteAsync(tableName, data, cancellationToken);
        }

        /// <summary>
        /// 创建外键约束
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="foreignKeys">外键约束列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override Task CreateForeignKeysAsync(string tableName, List<ForeignKeySchema> foreignKeys, CancellationToken cancellationToken = default)
        {
            return _foreignKeyManager.CreateForeignKeysAsync(tableName, foreignKeys, cancellationToken);
        }

        /// <summary>
        /// 处理数据行值
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        protected override object? ProcessValue(object? value)
        {
            return _typeConverter.ProcessValue(value);
        }
    }
}