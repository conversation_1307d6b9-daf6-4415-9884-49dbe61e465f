using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.MySQL.Writers
{
    /// <summary>
    /// MySQL批量写入器
    /// </summary>
    public class MySqlBulkWriter : BaseBulkWriter
    {
        private readonly MySqlDataTypeConverter _typeConverter;
        private readonly int _commandTimeout;

        /// <summary>
        /// 初始化MySQL批量写入器
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="batchSize">批处理大小</param>
        /// <param name="typeConverter">数据类型转换器</param>
        /// <param name="commandTimeout">命令超时时间（秒）</param>
        public MySqlBulkWriter(string connectionString, ILogger logger, int batchSize, MySqlDataTypeConverter? typeConverter = null, int commandTimeout = 300)
            : base(connectionString, logger, batchSize)
        {
            _typeConverter = typeConverter ?? new MySqlDataTypeConverter(logger);
            _commandTimeout = commandTimeout;
        }

        /// <summary>
        /// 批量写入数据
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="data">数据行集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override async Task BulkWriteAsync(string tableName, IEnumerable<dynamic> data, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentNullException(nameof(tableName));

            if (data == null)
                throw new ArgumentNullException(nameof(data));

            Logger.LogInformation("开始批量写入数据到表 {TableName}", tableName);

            // 转换为字典列表便于操作
            var allRows = data.Select(row => row as IDictionary<string, object> ?? new Dictionary<string, object>()).ToList();

            if (allRows.Count == 0)
            {
                Logger.LogInformation("没有数据需要写入到表 {TableName}", tableName);
                return;
            }

            // 获取所有列名
            var columns = allRows.SelectMany(r => r.Keys)
                .Distinct()
                .Where(k => !string.IsNullOrEmpty(k))
                .ToList();

            if (columns.Count == 0)
            {
                Logger.LogWarning("表 {TableName} 的数据没有有效列", tableName);
                return;
            }

            // 批量处理
            Logger.LogInformation("将 {RowCount} 行数据写入表 {TableName}，批次大小：{BatchSize}",
                allRows.Count, tableName, BatchSize);

            for (int i = 0; i < allRows.Count; i += BatchSize)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    Logger.LogWarning("批量写入操作已取消");
                    break;
                }

                var batch = allRows.Skip(i).Take(BatchSize).ToList();
                await WriteDataBatchAsync(tableName, batch, columns, cancellationToken);
            }

            Logger.LogInformation("完成批量写入数据到表 {TableName}", tableName);
        }

        private async Task WriteDataBatchAsync(string tableName, List<IDictionary<string, object>> batch, List<string> columns, CancellationToken cancellationToken)
        {
            if (batch.Count == 0 || columns.Count == 0)
                return;

            try
            {
                using var conn = new MySqlConnection(ConnectionString);
                await conn.OpenAsync(cancellationToken);

                // 构建批量插入SQL - 根据实际批次大小调整
                string sql = BuildBatchInsertSql(tableName, columns, batch.Count);

                // 准备批次参数
                var parameters = PrepareBatchParameters(batch, columns);

                // 执行批量插入
                int retryCount = 0;
                bool success = false;

                while (!success && retryCount < MaxRetryAttempts)
                {
                    try
                    {
                        // 使用命令超时设置
                        await conn.ExecuteAsync(new CommandDefinition(
                            sql,
                            parameters,
                            commandTimeout: _commandTimeout,
                            cancellationToken: cancellationToken));
                        success = true;
                    }
                    catch (MySqlException ex) when (IsTransientError(ex) && retryCount < MaxRetryAttempts)
                    {
                        retryCount++;
                        Logger.LogWarning(ex, "批量写入时发生暂时性错误，将在 {Delay} 秒后重试",
                            Math.Pow(2, retryCount));

                        await Task.Delay(GetRetryDelay(retryCount), cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "批量写入数据到表 {TableName} 时出错: {ErrorMessage}", tableName, ex.Message);
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "批量写入数据到表 {TableName} 时出错: {ErrorMessage}", tableName, ex.Message);
                throw;
            }
        }

        private string BuildBatchInsertSql(string tableName, List<string> columns, int batchSize)
        {
            var backtickColumns = columns.Select(c => $"`{c}`").ToList();
            var valuesList = new List<string>();

            for (int i = 0; i < batchSize; i++)
            {
                var paramNames = columns.Select(c => $"@{c}_{i}").ToList();
                valuesList.Add($"({string.Join(", ", paramNames)})");
            }

            string columnList = string.Join(", ", backtickColumns);
            string valuesClauses = string.Join(", ", valuesList);

            // 构建批量插入SQL
            return $"INSERT INTO `{tableName}` ({columnList}) VALUES {valuesClauses}";
        }

        private string BuildInsertSql(string tableName, List<string> columns)
        {
            // 此方法保留用于兼容性，实际使用 BuildBatchInsertSql
            return BuildBatchInsertSql(tableName, columns, 1);
        }

        /// <summary>
        /// 处理值转换
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        public override object? ProcessValue(object? value)
        {
            return _typeConverter.ProcessValue(value);
        }

        /// <summary>
        /// 准备批量参数
        /// </summary>
        /// <param name="batch">批处理数据</param>
        /// <param name="columns">列名列表</param>
        /// <returns>动态参数</returns>
        protected override DynamicParameters PrepareBatchParameters(List<IDictionary<string, object>> batch, List<string> columns)
        {
            var parameters = new DynamicParameters();

            for (int i = 0; i < batch.Count; i++)
            {
                var row = batch[i];
                foreach (var column in columns)
                {
                    var value = row.TryGetValue(column, out var val) ? val : null;
                    parameters.Add($"@{column}_{i}", ProcessValue(value));
                }
            }

            return parameters;
        }

        /// <summary>
        /// 判断是否为可重试的暂时性错误
        /// </summary>
        /// <param name="ex">MySQL异常</param>
        /// <returns>是否可重试</returns>
        private bool IsTransientError(MySqlException ex)
        {
            // MySQL暂时性错误代码
            var transientErrorNumbers = new[] {
                1040,  // 连接数过多
                1205,  // 锁等待超时
                1213,  // 死锁
                2006,  // 服务器已消失
                2013,  // 连接丢失
                1159,  // 超时
                1161   // 超时
            };

            // 检查错误消息中是否包含超时相关信息
            bool isTimeoutError = ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
                                 ex.Message.Contains("timed out", StringComparison.OrdinalIgnoreCase);

            return transientErrorNumbers.Contains(ex.Number) || isTimeoutError;
        }
    }
}
