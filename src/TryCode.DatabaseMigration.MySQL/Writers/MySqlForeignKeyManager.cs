using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.MySQL.Writers
{
    /// <summary>
    /// MySQL外键管理器
    /// </summary>
    public class MySqlForeignKeyManager : BaseForeignKeyManager
    {
        /// <summary>
        /// 初始化MySQL外键管理器
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        public MySqlForeignKeyManager(string connectionString, ILogger logger) 
            : base(connectionString, logger)
        {
        }
        
        /// <summary>
        /// 创建外键约束
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="foreignKeys">外键约束列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override async Task CreateForeignKeysAsync(string tableName, List<ForeignKeySchema> foreignKeys, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentNullException(nameof(tableName));
                
            if (foreignKeys == null || !foreignKeys.Any())
            {
                Logger.LogInformation("表 {TableName} 没有外键需要创建", tableName);
                return;
            }
            
            Logger.LogInformation("开始为表 {TableName} 创建 {Count} 个外键约束", tableName, foreignKeys.Count);
            
            using var conn = new MySqlConnection(ConnectionString);
            await conn.OpenAsync(cancellationToken);
            
            // 关闭外键检查，以避免创建外键时的约束问题
            await conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 0;");
            
            try
            {
                foreach (var fk in foreignKeys)
                {
                    try
                    {
                        // 验证外键引用的表是否存在
                        var refTableExists = await ValidateReferencedTable(conn, fk.ReferencedTable);
                        if (!refTableExists)
                        {
                            Logger.LogWarning("跳过创建外键 {ConstraintName}，因为引用的表 {ReferencedTable} 不存在",
                                fk.Name, fk.ReferencedTable);
                            continue;
                        }
                        
                        string sql = BuildForeignKeySQL(tableName, fk);
                        await conn.ExecuteAsync(sql);
                        
                        Logger.LogInformation("已成功创建外键约束 {ConstraintName}", fk.Name);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "创建外键 {ConstraintName} 时出错: {ErrorMessage}", fk.Name, ex.Message);
                    }
                }
            }
            finally
            {
                // 恢复外键检查
                await conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 1;");
            }
        }
        
        private async Task<bool> ValidateReferencedTable(MySqlConnection conn, string referencedTable)
        {
            var sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = @tableName";
            var count = await conn.ExecuteScalarAsync<int>(sql, new { tableName = referencedTable });
            return count > 0;
        }
        
        /// <summary>
        /// 构建外键SQL
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="fk">外键信息</param>
        /// <returns>外键SQL语句</returns>
        protected override string BuildForeignKeySQL(string tableName, ForeignKeySchema fk)
        {
            if (string.IsNullOrEmpty(fk.Name))
            {
                // 生成外键名称
                fk.Name = $"FK_{tableName}_{fk.ReferencedTable}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            }
            
            var sql = new StringBuilder();
            
            // 确保 Columns 和 ReferencedColumns 包含数据
            if (fk.Columns.Count == 0 && !string.IsNullOrEmpty(fk.ColumnName))
            {
                fk.Columns.Add(fk.ColumnName);
            }
            
            if (fk.ReferencedColumns.Count == 0 && !string.IsNullOrEmpty(fk.ReferencedColumn))
            {
                fk.ReferencedColumns.Add(fk.ReferencedColumn);
            }
            
            // 列处理
            var columns = string.Join(", ", fk.Columns.Select(c => $"`{c}`"));
            var refColumns = string.Join(", ", fk.ReferencedColumns.Select(c => $"`{c}`"));
            
            sql.AppendLine($"ALTER TABLE `{tableName}`");
            sql.AppendLine($"ADD CONSTRAINT `{fk.Name}`");
            sql.AppendLine($"FOREIGN KEY ({columns})");
            sql.AppendLine($"REFERENCES `{fk.ReferencedTable}` ({refColumns})");
            
            // 添加约束动作
            if (!string.IsNullOrEmpty(fk.OnUpdate))
            {
                sql.AppendLine($"ON UPDATE {GetValidConstraintAction(fk.OnUpdate)}");
            }
            
            if (!string.IsNullOrEmpty(fk.OnDelete))
            {
                sql.AppendLine($"ON DELETE {GetValidConstraintAction(fk.OnDelete)}");
            }
            
            return sql.ToString();
        }
        
        private string GetValidConstraintAction(string action)
        {
            if (string.IsNullOrEmpty(action))
                return "RESTRICT";
                
            // 标准化约束动作名称
            var normalizedAction = action.ToUpper();
            
            switch (normalizedAction)
            {
                case "CASCADE":
                case "SET NULL":
                case "SET DEFAULT":
                case "RESTRICT":
                case "NO ACTION":
                    return normalizedAction;
                default:
                    Logger.LogWarning("不支持的约束动作 {Action}，使用默认值 RESTRICT", action);
                    return "RESTRICT";
            }
        }
    }
}
