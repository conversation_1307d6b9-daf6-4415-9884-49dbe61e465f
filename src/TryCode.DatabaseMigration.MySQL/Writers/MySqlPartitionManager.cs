using System;
using System.Text;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.MySQL.Writers
{
    /// <summary>
    /// MySQL分区管理器
    /// </summary>
    public class MySqlPartitionManager
    {
        private readonly ILogger _logger;
        
        /// <summary>
        /// 初始化MySQL分区管理器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MySqlPartitionManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 添加分区定义到SQL
        /// </summary>
        /// <param name="sql">SQL构建器</param>
        /// <param name="schema">表结构信息</param>
        public void AppendPartitionDefinition(StringBuilder sql, TableSchema schema)
        {
            _logger.LogInformation("开始为表 {TableName} 生成分区定义，分区类型: {PartitionType}，分区数量: {PartitionCount}，分区键: {PartitionKey}",
                schema.Name, schema.PartitionType, schema.PartitionCount, schema.PartitionKey);

            if (!schema.IsPartitioned || string.IsNullOrEmpty(schema.PartitionKey) || schema.PartitionKeys.Count == 0)
            {
                _logger.LogWarning("表 {TableName} 的分区配置无效，IsPartitioned: {IsPartitioned}，PartitionKey: {PartitionKey}，PartitionKeys数量: {PartitionKeysCount}",
                    schema.Name, schema.IsPartitioned, schema.PartitionKey, schema.PartitionKeys.Count);
                return;
            }
            
            try
            {
                // 处理不同类型的分区
                if (schema.PartitionType?.Equals("HASH", StringComparison.OrdinalIgnoreCase) == true)
                {
                    // 尝试HASH分区，如果失败则回退到KEY分区
                    if (!TryAppendHashPartition(sql, schema))
                    {
                        _logger.LogInformation("HASH分区不可用，回退到KEY分区");
                        AppendKeyPartition(sql, schema);
                    }
                }
                else if (schema.PartitionType?.Equals("RANGE", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendRangePartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("LIST", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendListPartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("KEY", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendKeyPartition(sql, schema);
                }
                else
                {
                    _logger.LogWarning("不支持的分区类型 {PartitionType}", schema.PartitionType);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成分区定义时出错: {ErrorMessage}", ex.Message);
            }
        }
        
        /// <summary>
        /// 尝试添加HASH分区定义
        /// </summary>
        /// <returns>如果成功添加HASH分区返回true，否则返回false</returns>
        private bool TryAppendHashPartition(StringBuilder sql, TableSchema schema)
        {
            // 使用实际的分区数量，如果为0则默认为4
            var partitionCount = schema.PartitionCount > 0 ? schema.PartitionCount : 4;

            // 智能选择合适的HASH分区键
            var partitionKey = SelectValidHashPartitionKey(schema);

            if (string.IsNullOrEmpty(partitionKey))
            {
                _logger.LogWarning("表 {TableName} 没有找到合适的HASH分区键", schema.Name);
                return false;
            }

            sql.AppendLine($"PARTITION BY HASH (`{partitionKey}`)");
            sql.AppendLine($"PARTITIONS {partitionCount}");

            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个HASH分区，分区键: {PartitionKey}",
                schema.Name, partitionCount, partitionKey);
            return true;
        }

        private void AppendHashPartition(StringBuilder sql, TableSchema schema)
        {
            TryAppendHashPartition(sql, schema);
        }
        
        private void AppendKeyPartition(StringBuilder sql, TableSchema schema)
        {
            // 使用实际的分区数量，如果为0则默认为4
            var partitionCount = schema.PartitionCount > 0 ? schema.PartitionCount : 4;

            // 智能选择KEY分区键
            var partitionKey = SelectValidKeyPartitionKey(schema);

            if (string.IsNullOrEmpty(partitionKey))
            {
                _logger.LogWarning("表 {TableName} 的KEY分区未找到合适的分区键", schema.Name);
                return;
            }

            sql.AppendLine($"PARTITION BY KEY (`{partitionKey}`)");
            sql.AppendLine($"PARTITIONS {partitionCount}");

            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个KEY分区，分区键: {PartitionKey}",
                schema.Name, partitionCount, partitionKey);
        }
        
        private void AppendRangePartition(StringBuilder sql, TableSchema schema)
        {
            if (schema.PartitionDefinitions == null || schema.PartitionDefinitions.Count == 0)
            {
                _logger.LogWarning("表 {TableName} 的RANGE分区定义为空", schema.Name);
                return;
            }
            
            // 确保分区键存在
            if (string.IsNullOrEmpty(schema.PartitionKey) && schema.PartitionKeys.Count > 0)
            {
                schema.PartitionKey = schema.PartitionKeys[0];
                _logger.LogInformation("使用第一个分区键 {PartitionKey} 作为RANGE分区键", schema.PartitionKey);
            }
            
            if (string.IsNullOrEmpty(schema.PartitionKey))
            {
                _logger.LogWarning("表 {TableName} 的RANGE分区键未指定", schema.Name);
                return;
            }
            
            sql.AppendLine($"PARTITION BY RANGE (`{schema.PartitionKey}`) (");
            
            for (int i = 0; i < schema.PartitionDefinitions.Count; i++)
            {
                var def = schema.PartitionDefinitions[i];
                
                // 从分区条件中提取分区值
                string partitionValue = ExtractRangePartitionValue(def);
                
                // 如果没有从Value属性或Condition属性中提取到值，则跳过此分区
                if (string.IsNullOrEmpty(partitionValue))
                {
                    _logger.LogWarning("无法从分区 {PartitionName} 中提取RANGE分区值，跳过此分区", def.Name);
                    continue;
                }
                
                sql.Append($"    PARTITION p{i} VALUES LESS THAN ({partitionValue})");
                
                if (i < schema.PartitionDefinitions.Count - 1)
                {
                    sql.AppendLine(",");
                }
                else
                {
                    sql.AppendLine();
                }
            }
            
            sql.AppendLine(")");
            
            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个RANGE分区", 
                schema.Name, schema.PartitionDefinitions.Count);
        }
        
        private string ExtractRangePartitionValue(PartitionDefinition def)
        {
            // 首先检查Value属性是否已有值
            if (!string.IsNullOrEmpty(def.Value))
            {
                return def.Value;
            }
            
            // 尝试从Condition属性中提取值
            // PostgreSQL的RANGE分区条件格式为: FOR VALUES FROM (2023) TO (2024)
            if (!string.IsNullOrEmpty(def.Condition))
            {
                var match = Regex.Match(def.Condition, @"TO\s*\((.*?)\)", RegexOptions.IgnoreCase);
                
                if (match.Success && match.Groups.Count > 1)
                {
                    // 将提取的值保存到Value属性中，以便后续使用
                    def.Value = match.Groups[1].Value.Trim();
                    return def.Value;
                }
            }
            
            return string.Empty;
        }
        
        private void AppendListPartition(StringBuilder sql, TableSchema schema)
        {
            if (schema.PartitionDefinitions == null || schema.PartitionDefinitions.Count == 0)
            {
                _logger.LogWarning("表 {TableName} 的LIST分区定义为空", schema.Name);
                return;
            }
            
            // 确保分区键存在
            if (string.IsNullOrEmpty(schema.PartitionKey) && schema.PartitionKeys.Count > 0)
            {
                schema.PartitionKey = schema.PartitionKeys[0];
                _logger.LogInformation("使用第一个分区键 {PartitionKey} 作为LIST分区键", schema.PartitionKey);
            }
            
            if (string.IsNullOrEmpty(schema.PartitionKey))
            {
                _logger.LogWarning("表 {TableName} 的LIST分区键未指定", schema.Name);
                return;
            }
            
            sql.AppendLine($"PARTITION BY LIST (`{schema.PartitionKey}`) (");
            
            for (int i = 0; i < schema.PartitionDefinitions.Count; i++)
            {
                var def = schema.PartitionDefinitions[i];
                
                // 从分区条件中提取分区值
                string partitionValues = ExtractListPartitionValues(def);
                
                // 如果没有从Value属性或Condition属性中提取到值，则跳过此分区
                if (string.IsNullOrEmpty(partitionValues))
                {
                    _logger.LogWarning("无法从分区 {PartitionName} 中提取LIST分区值，跳过此分区", def.Name);
                    continue;
                }
                
                sql.Append($"    PARTITION p{i} VALUES IN ({partitionValues})");
                
                if (i < schema.PartitionDefinitions.Count - 1)
                {
                    sql.AppendLine(",");
                }
                else
                {
                    sql.AppendLine();
                }
            }
            
            sql.AppendLine(")");
            
            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个LIST分区", 
                schema.Name, schema.PartitionDefinitions.Count);
        }
        
        /// <summary>
        /// 从PostgreSQL分区定义中提取LIST分区值
        /// </summary>
        private string ExtractListPartitionValues(PartitionDefinition def)
        {
            // 首先检查Value属性是否已有值
            if (!string.IsNullOrEmpty(def.Value))
            {
                return def.Value;
            }
            
            // 尝试从Condition属性中提取值
            // PostgreSQL的LIST分区条件格式为: FOR VALUES IN (1, 2, 3)
            if (!string.IsNullOrEmpty(def.Condition))
            {
                var match = System.Text.RegularExpressions.Regex.Match(
                    def.Condition, @"IN\s*\((.*?)\)", 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                
                if (match.Success && match.Groups.Count > 1)
                {
                    // 将提取的值保存到Value属性中，以便后续使用
                    def.Value = match.Groups[1].Value.Trim();
                    return def.Value;
                }
            }
            
            return string.Empty;
        }

        /// <summary>
        /// 选择合适的HASH分区键
        /// MySQL的HASH分区只支持整数类型，不支持字符串类型
        /// </summary>
        private string SelectValidHashPartitionKey(TableSchema schema)
        {
            // 1. 首先检查原始分区键是否有效
            if (!string.IsNullOrEmpty(schema.PartitionKey))
            {
                var originalColumn = schema.Columns.FirstOrDefault(c => c.Name.Equals(schema.PartitionKey, StringComparison.OrdinalIgnoreCase));
                if (originalColumn != null && IsValidHashPartitionType(originalColumn.DataType))
                {
                    _logger.LogInformation("使用原始分区键 {PartitionKey} 作为HASH分区键", schema.PartitionKey);
                    return schema.PartitionKey;
                }
                else
                {
                    _logger.LogWarning("原始分区键 {PartitionKey} 的类型 {DataType} 不支持HASH分区，将寻找替代方案",
                        schema.PartitionKey, originalColumn?.DataType ?? "未知");
                }
            }

            // 2. 检查分区键列表中是否有有效的键
            foreach (var key in schema.PartitionKeys)
            {
                var column = schema.Columns.FirstOrDefault(c => c.Name.Equals(key, StringComparison.OrdinalIgnoreCase));
                if (column != null && IsValidHashPartitionType(column.DataType))
                {
                    _logger.LogInformation("使用分区键 {PartitionKey} 作为HASH分区键", key);
                    return key;
                }
            }

            // 3. 检查主键中是否有有效的键
            foreach (var primaryKey in schema.PrimaryKeys)
            {
                var column = schema.Columns.FirstOrDefault(c => c.Name.Equals(primaryKey, StringComparison.OrdinalIgnoreCase));
                if (column != null && IsValidHashPartitionType(column.DataType))
                {
                    _logger.LogInformation("使用主键 {PrimaryKey} 作为HASH分区键", primaryKey);
                    return primaryKey;
                }
            }

            // // 4. 寻找任何整数类型的列作为分区键
            // var integerColumn = schema.Columns.FirstOrDefault(c => IsValidHashPartitionType(c.DataType));
            // if (integerColumn != null)
            // {
            //     _logger.LogInformation("使用整数列 {ColumnName} 作为HASH分区键", integerColumn.Name);
            //     return integerColumn.Name;
            // }

            // 5. 如果没有找到合适的列，考虑使用KEY分区替代HASH分区
            _logger.LogWarning("表 {TableName} 没有找到合适的整数类型列用于HASH分区", schema.Name);
            return string.Empty;
        }

        /// <summary>
        /// 选择合适的KEY分区键
        /// MySQL的KEY分区支持更多数据类型，包括字符串类型
        /// </summary>
        private string SelectValidKeyPartitionKey(TableSchema schema)
        {
            // 1. 首先检查原始分区键
            if (!string.IsNullOrEmpty(schema.PartitionKey))
            {
                var originalColumn = schema.Columns.FirstOrDefault(c => c.Name.Equals(schema.PartitionKey, StringComparison.OrdinalIgnoreCase));
                if (originalColumn != null)
                {
                    _logger.LogInformation("使用原始分区键 {PartitionKey} 作为KEY分区键", schema.PartitionKey);
                    return schema.PartitionKey;
                }
            }

            // 2. 检查分区键列表
            foreach (var key in schema.PartitionKeys)
            {
                var column = schema.Columns.FirstOrDefault(c => c.Name.Equals(key, StringComparison.OrdinalIgnoreCase));
                if (column != null)
                {
                    _logger.LogInformation("使用分区键 {PartitionKey} 作为KEY分区键", key);
                    return key;
                }
            }

            // 3. 使用主键
            if (schema.PrimaryKeys.Count > 0)
            {
                var primaryKey = schema.PrimaryKeys[0];
                _logger.LogInformation("使用主键 {PrimaryKey} 作为KEY分区键", primaryKey);
                return primaryKey;
            }

            // 4. 寻找任何非空列作为分区键
            var nonNullableColumn = schema.Columns.FirstOrDefault(c => !c.IsNullable);
            if (nonNullableColumn != null)
            {
                _logger.LogInformation("使用非空列 {ColumnName} 作为KEY分区键", nonNullableColumn.Name);
                return nonNullableColumn.Name;
            }

            // 5. 使用第一个列
            if (schema.Columns.Count > 0)
            {
                var firstColumn = schema.Columns[0];
                _logger.LogInformation("使用第一个列 {ColumnName} 作为KEY分区键", firstColumn.Name);
                return firstColumn.Name;
            }

            return string.Empty;
        }

        /// <summary>
        /// 检查数据类型是否支持HASH分区
        /// MySQL的HASH分区只支持整数类型
        /// </summary>
        private bool IsValidHashPartitionType(string dataType)
        {
            if (string.IsNullOrEmpty(dataType))
                return false;

            var lowerType = dataType.ToLower();

            // MySQL HASH分区支持的类型
            return lowerType.Contains("int") ||          // int, bigint, smallint, tinyint
                   lowerType.Contains("serial") ||       // serial, bigserial
                   lowerType.Contains("year") ||         // year
                   lowerType == "bit";                   // bit
        }
    }
}
