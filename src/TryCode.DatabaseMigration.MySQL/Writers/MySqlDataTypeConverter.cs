using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.MySQL.Writers
{
    /// <summary>
    /// MySQL数据类型转换器
    /// </summary>
    public class MySqlDataTypeConverter : BaseDataTypeConverter
    {
        /// <summary>
        /// MySQL最大键长度（字节）
        /// </summary>
        private const int MaxKeyLengthBytes = 3072;
        
        /// <summary>
        /// utf8mb4每个字符最大字节数
        /// </summary>
        private const int Utf8mb4MaxBytesPerChar = 4;
        
        /// <summary>
        /// 类型映射字典
        /// </summary>
        private readonly Dictionary<string, string> _typeMappings;
        
        /// <summary>
        /// 初始化MySQL数据类型转换器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="typeMappings">类型映射</param>
        public MySqlDataTypeConverter(ILogger logger, Dictionary<string, string>? typeMappings = null) 
            : base(logger)
        {
            _typeMappings = typeMappings ?? new Dictionary<string, string>();
        }
        
        /// <summary>
        /// 转换为MySQL数据类型
        /// </summary>
        /// <param name="column">列信息</param>
        /// <returns>MySQL数据类型</returns>
        public override string ConvertDataType(ColumnSchema column)
        {
            if (column == null)
                throw new ArgumentNullException(nameof(column));
                
            var dataType = column.DataType.ToLower();

            // 如果是主键或索引列，需要确保不超过长度限制
            if (column.IsPrimaryKey || column.IsIndexed || column.Name == "Id")
            {
                // 确保字符类型的列不会产生过长的索引
                if (dataType.Contains("char") || dataType.Contains("varchar") || dataType.Contains("text"))
                {
                    var maxBytes = column.MaxLength.GetValueOrDefault(255) * Utf8mb4MaxBytesPerChar;
                    if (maxBytes > MaxKeyLengthBytes)
                    {
                        var newLength = MaxKeyLengthBytes / Utf8mb4MaxBytesPerChar;
                        Logger.LogWarning(
                            "列 {ColumnName} 的长度已从 {OldLength} 调整为 {NewLength}",
                            column.Name,
                            column.MaxLength,
                            newLength
                        );
                        return $"VARCHAR({newLength})";
                    }
                }
            }

            // 首先检查是否有自定义类型映射
            if (_typeMappings.TryGetValue(dataType, out var mappedType))
            {
                if (column.IsPrimaryKey || column.IsIndexed || column.Name == "Id")
                {
                    if (mappedType.ToLower().Contains("text") || mappedType.ToLower().Contains("blob"))
                    {
                        if(column.MaxLength.HasValue)
                            return $"VARCHAR({column.MaxLength})";
                        return "VARCHAR(255)";
                    }
                }
                if(column.MaxLength.HasValue && mappedType.ToLower().Contains("[num]"))
                    return column.MaxLength> 262144 ? "longtext" : mappedType.Replace("[num]", column.MaxLength.ToString());
                return mappedType;
            }

            switch (dataType)
            {
                case "char":
                case "nchar":
                    return column.MaxLength.HasValue ? $"CHAR({column.MaxLength})" : "CHAR(255)";
                
                case "json":
                case "jsonb":
                case "text[]":
                case "array":
                    return "json";
                case "varchar":
                case "nvarchar":
                case "character varying":
                    if (column.MaxLength.HasValue)
                    {
                        return column.IsPrimaryKey || column.IsIndexed ? "VARCHAR(255)" : column.MaxLength.Value > 16383 ? "LONGTEXT" : $"VARCHAR({column.MaxLength})";
                    }
                    
                    return "VARCHAR(255)";

                case "text":
                case "ntext":
                case "longtext":
                case "mediumtext":
                case "tinytext":
                case "xml":
                    return column.IsPrimaryKey || column.IsIndexed || column.Name == "Id" ? "VARCHAR(255)" : "LONGTEXT";

                case "decimal":
                case "numeric":
                    if (column.NumericPrecision.HasValue && column.Scale.HasValue)
                    {
                        return $"DECIMAL({column.NumericPrecision}, {column.Scale})";
                    }
                    return "DECIMAL(18, 2)";

                case "float":
                case "real":
                    return "FLOAT";

                case "double":
                    return "DOUBLE";

                case "bit":
                    return "BIT";

                case "tinyint":
                    return "TINYINT";

                case "smallint":
                    return "SMALLINT";

                case "int":
                case "integer":
                    return "INT";

                case "bigint":
                    return "BIGINT";

                case "date":
                    return "DATE";

                case "time":
                    return "TIME";

                case "datetime":
                case "datetime2":
                    return "DATETIME";
                case "timestamp":
                    return "TIMESTAMP";

                case "binary":
                case "varbinary":
                case "image":
                case "blob":
                case "longblob":
                case "mediumblob":
                case "tinyblob":
                case "bytea":
                    return column.IsPrimaryKey || column.IsIndexed ? "VARBINARY(255)" : "LONGBLOB";

                case "uniqueidentifier":
                case "guid":
                case "uuid":
                    return "CHAR(36) CHARACTER SET ascii"; // 使用 ascii 字符集提高性能

                case "money":
                case "smallmoney":
                    return "DECIMAL(19, 4)";

                case "citext": // PostgreSQL 不区分大小写的文本类型
                    return "VARCHAR(255) COLLATE utf8mb4_general_ci";

                case "inet": // PostgreSQL 网络地址类型
                    return "VARCHAR(45)"; // 足够存储 IPv6 地址

                case "interval": // PostgreSQL 时间间隔类型
                    return "VARCHAR(255)";

                default:
                    // 检查列名是否包含特定关键字来推断合适的类型
                    var columnName = column.Name.ToLower();
                    if (columnName.Contains("html") || columnName.Contains("description") ||
                        columnName.Contains("content") || columnName.Contains("text"))
                    {
                        return column.IsPrimaryKey || column.IsIndexed ? "VARCHAR(255)" : "LONGTEXT";
                    }
                    if (columnName.EndsWith("url") || columnName.Contains("link"))
                    {
                        return "VARCHAR(2048)";
                    }
                    if (columnName.Contains("email"))
                    {
                        return "VARCHAR(255)";
                    }
                    if (columnName.Contains("phone") || columnName.Contains("mobile"))
                    {
                        return "VARCHAR(50)";
                    }
                    if (columnName == "browserinfo" || columnName.Contains("useragent"))
                    {
                        return "VARCHAR(1024)";
                    }
                    if (columnName.Contains("ipaddress"))
                    {
                        return "VARCHAR(45)";
                    }
                    if (columnName.Contains("correlationid"))
                    {
                        return "VARCHAR(100)";
                    }
                    
                    return "VARCHAR(255)";
            }
        }
        
        /// <summary>
        /// 处理数据行值
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        public override object? ProcessValue(object? value)
        {
            try
            {
                if (value == null)
                {
                    return null;
                }

                if (value is DBNull)
                {
                    return null;
                }

                // 对于GUID类型的特殊处理
                if (value is Guid || value.GetType().Name == "Guid")
                {
                    return value.ToString();
                }

                return value switch
                {
                    // 处理常见的数据类型
                    Array arr => System.Text.Json.JsonSerializer.Serialize(arr),
                    DateTime dt => dt,
                    TimeSpan ts => ts.ToString(),

                    // 网络相关类型
                    System.Net.IPAddress ip => ip.ToString(),
                    System.Net.NetworkInformation.PhysicalAddress mac => mac.ToString(),

                    // 处理字符串，确保不超过 MySQL 的限制
                    string s when s.Length > 65535 => s.Substring(0, 65535),

                    // 处理 PostgreSQL 特定类型
                    _ => value.GetType().Name switch
                    {
                        "NpgsqlTsVector" => value.ToString(),
                        "NpgsqlTsQuery" => value.ToString(),
                        "NpgsqlInterval" => ((TimeSpan)value).ToString(),
                        "NpgsqlRange`1" => value.ToString(),
                        "NpgsqlPoint" => value.ToString(),
                        "NpgsqlLSeg" => value.ToString(),
                        "NpgsqlBox" => value.ToString(),
                        "NpgsqlPath" => value.ToString(),
                        "NpgsqlPolygon" => value.ToString(),
                        "NpgsqlCircle" => value.ToString(),
                        "NpgsqlInet" => value.ToString(),
                        "NpgsqlCidr" => value.ToString(),
                        "NpgsqlMacAddress" => value.ToString(),
                        "BitString" => value.ToString(),
                        _ => value
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "处理值时出错 ({ErrorMessage})，将返回 NULL", ex.Message);
                return null;
            }
        }
        
        /// <summary>
        /// 计算列的预估字节长度
        /// </summary>
        /// <param name="column">列信息</param>
        /// <returns>预估字节长度</returns>
        public int CalculateColumnByteLength(ColumnSchema column)
        {
            var dataType = column.DataType.ToLower();
            
            // 基于数据类型和长度计算预估字节长度
            if (dataType.Contains("char") || dataType.Contains("varchar"))
            {
                // 对于字符类型，每个字符最多占用4字节(utf8mb4)
                var charLength = column.MaxLength ?? 255;
                return charLength * Utf8mb4MaxBytesPerChar;
            }
            
            // 其他类型的预估长度
            switch (dataType)
            {
                case "int":
                case "integer":
                    return 4;
                case "bigint":
                    return 8;
                case "smallint":
                    return 2;
                case "tinyint":
                    return 1;
                case "text":
                case "longtext":
                case "mediumtext":
                case "tinytext":
                case "json":
                case "xml":
                    return 100 * Utf8mb4MaxBytesPerChar; // 限制为100个字符
                case "binary":
                case "varbinary":
                    return column.MaxLength ?? 255;
                case "uniqueidentifier":
                case "guid":
                case "uuid":
                    return 36 * Utf8mb4MaxBytesPerChar;
                default:
                    return 255; // 默认值
            }
        }
        
        /// <summary>
        /// 检查索引长度是否超出MySQL限制
        /// </summary>
        /// <param name="columnNames">列名列表</param>
        /// <param name="schema">表结构信息</param>
        /// <returns>是否有效</returns>
        public bool ValidateKeyLength(List<string> columnNames, TableSchema schema)
        {
            int totalLength = 0;
            foreach (var columnName in columnNames)
            {
                var column = schema.Columns.FirstOrDefault(c => c.Name == columnName);
                if (column != null)
                {
                    totalLength += CalculateColumnByteLength(column);
                }
            }
            
            if (totalLength > MaxKeyLengthBytes)
            {
                Logger.LogWarning("索引总长度({TotalLength}字节)超过MySQL限制({MaxLength}字节)", totalLength, MaxKeyLengthBytes);
                return false;
            }
            
            return true;
        }
    }
}
