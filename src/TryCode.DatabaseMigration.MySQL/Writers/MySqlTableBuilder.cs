using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using MySql.Data.MySqlClient;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.MySQL.Writers
{
    /// <summary>
    /// MySQL表结构构建器
    /// </summary>
    public class MySqlTableBuilder : BaseTableBuilder
    {
        private readonly MySqlDataTypeConverter _typeConverter;
        private readonly MySqlPartitionManager _partitionManager;

        /// <summary>
        /// 初始化MySQL表结构构建器
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="typeConverter">数据类型转换器</param>
        public MySqlTableBuilder(string connectionString, ILogger logger, MySqlDataTypeConverter? typeConverter = null)
            : base(connectionString, logger)
        {
            _typeConverter = typeConverter ?? new MySqlDataTypeConverter(logger);
            _partitionManager = new MySqlPartitionManager(logger);
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <param name="skipSchemaCreation">是否跳过创建表结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override async Task CreateTableAsync(TableSchema schema, bool skipSchemaCreation = false, CancellationToken cancellationToken = default)
        {
            if (schema == null)
                throw new ArgumentNullException(nameof(schema));

            // 如果配置为跳过表结构创建，则直接返回
            if (skipSchemaCreation)
            {
                Logger.LogInformation("跳过创建表 {TableName} 的表结构", schema.Name);
                return;
            }

            Logger.LogInformation("开始创建表 {TableName}", schema.Name);
            using var conn = new MySqlConnection(ConnectionString);
            await conn.OpenAsync(cancellationToken);

            try
            {
                await DisableForeignKeyChecks(conn);
                try
                {
                    // 检查表是否存在
                    bool tableExists = await CheckTableExistsAsync(conn, schema.Name);

                    if (tableExists)
                    {
                        Logger.LogInformation("表 {TableName} 已存在，将删除后重新创建", schema.Name);
                    }

                    await DropExistingTable(conn, schema.Name);
                    string sqlString = BuildCreateTableSql(schema);
                    await ExecuteCreateTableSql(conn, sqlString, schema.Name);
                    await ValidateTableCreation(conn, schema.Name);
                }
                finally
                {
                    await EnableForeignKeyChecks(conn);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "创建表 {TableName} 时发生错误: {ErrorMessage}", schema.Name, ex.Message);
                throw;
            }
        }

        private Task DisableForeignKeyChecks(MySqlConnection conn) =>
            conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 0;");

        private Task EnableForeignKeyChecks(MySqlConnection conn) =>
            conn.ExecuteAsync("SET FOREIGN_KEY_CHECKS = 1;");

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private async Task<bool> CheckTableExistsAsync(MySqlConnection conn, string tableName)
        {
            return await conn.ExecuteScalarAsync<bool>(
                "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = @tableName)",
                new { tableName });
        }

        private Task DropExistingTable(MySqlConnection conn, string tableName) =>
            conn.ExecuteAsync($"DROP TABLE IF EXISTS `{tableName}`");

        private async Task ExecuteCreateTableSql(MySqlConnection conn, string sqlString, string tableName)
        {
            Logger.LogDebug("生成的建表SQL:\n{SQL}", sqlString);

            try
            {
                await conn.ExecuteAsync(sqlString);
            }
            catch (Exception ex)
            {
                // 记录详细错误信息，包括SQL语句
                throw new Exception($"执行SQL失败: {ex.Message}。\n执行的SQL语句: {sqlString}", ex);
            }
        }

        private async Task ValidateTableCreation(MySqlConnection conn, string tableName)
        {
            var tableExists = await CheckTableExistsAsync(conn, tableName);

            if (!tableExists)
            {
                throw new Exception($"创建表 {tableName} 失败");
            }

            Logger.LogInformation("表 {TableName} 创建成功", tableName);
        }

        /// <summary>
        /// 构建创建表的SQL语句
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>创建表的SQL语句</returns>
        protected override string BuildCreateTableSql(TableSchema schema)
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE `{schema.Name}` (");

            // 添加列定义
            var columnDefinitions = BuildColumnDefinitions(schema);

            // 添加主键
            var primaryKeyDef = BuildPrimaryKeyDefinition(schema);
            if (!string.IsNullOrEmpty(primaryKeyDef))
            {
                columnDefinitions.Add(primaryKeyDef);
            }

            sql.AppendLine(string.Join(",\n    ", columnDefinitions));
            sql.AppendLine(")");

            // 添加表选项（引擎、字符集）
            sql.AppendLine("ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");

            // 添加分区定义
            if (schema.IsPartitioned)
            {
                _partitionManager.AppendPartitionDefinition(sql, schema);
            }

            sql.AppendLine(";");
            return sql.ToString();
        }

        /// <summary>
        /// 构建列定义
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>列定义列表</returns>
        protected override List<string> BuildColumnDefinitions(TableSchema schema)
        {
            var columnDefinitions = new List<string>();
            var autoIncrementColumn = schema.Columns.FirstOrDefault(c =>
                c.IsAutoIncrement ||
                (c.DefaultValue?.Contains("nextval") ?? false) ||
                c.DataType.ToLower() == "serial");

            // 如果有自增列但不是主键，将其添加为主键
            if (autoIncrementColumn != null && !schema.PrimaryKeys.Contains(autoIncrementColumn.Name))
            {
                schema.AddPrimaryKey(autoIncrementColumn.Name);
            }

            foreach (var column in schema.Columns)
            {
                var definition = new StringBuilder();

                // 获取MySQL数据类型
                var mysqlType = _typeConverter.ConvertDataType(column);
                definition.Append($"`{column.Name}` {mysqlType}");

                // 处理自增字段
                var isAutoIncrement = column.IsAutoIncrement ||
                                    (column.DefaultValue?.Contains("nextval") ?? false) ||
                                    column.DataType.ToLower() == "serial";

                if (isAutoIncrement)
                {
                    definition.Append(" NOT NULL AUTO_INCREMENT");
                }
                else
                {
                    // 添加是否可为空和默认值
                    if (column.IsNullable)
                    {
                        definition.Append(" DEFAULT NULL");
                    }
                    else
                    {
                        definition.Append(" NOT NULL");
                    }

                    // 添加默认值
                    if (!string.IsNullOrEmpty(column.DefaultValue) && !column.IsNullable)
                    {
                        if (!column.DefaultValue.Contains("nextval") && !column.DefaultValue.Contains("serial"))
                        {
                            if (column.DataType.ToLower() == "timestamp" &&
                                column.DefaultValue.ToUpper() == "CURRENT_TIMESTAMP")
                            {
                                definition.Append(" DEFAULT CURRENT_TIMESTAMP");
                            }
                            else if (column.DataType.ToLower() == "date" &&
                                column.DefaultValue.ToUpper().Contains("CURRENT_DATE"))
                            {
                                definition.Append(" DEFAULT (CURRENT_DATE)");
                            }
                            else if (column.DefaultValue.StartsWith("'") && column.DefaultValue.EndsWith("'"))
                            {
                                definition.Append($" DEFAULT {column.DefaultValue}");
                            }
                            else if (decimal.TryParse(column.DefaultValue, out _) ||
                                column.DefaultValue.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                                column.DefaultValue.Equals("false", StringComparison.OrdinalIgnoreCase))
                            {
                                definition.Append($" DEFAULT {column.DefaultValue}");
                            }
                        }
                    }
                    else if (column.DataType.ToLower() == "timestamp" && !column.IsNullable)
                    {
                        definition.Append(" DEFAULT CURRENT_TIMESTAMP");
                    }
                }

                columnDefinitions.Add(definition.ToString());
            }

            return columnDefinitions;
        }

        /// <summary>
        /// 构建主键定义
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>主键定义</returns>
        protected override string? BuildPrimaryKeyDefinition(TableSchema schema)
        {
            // 添加主键
            if (schema.PrimaryKeys.Any())
            {
                // 验证主键长度
                if (_typeConverter.ValidateKeyLength(schema.PrimaryKeys, schema))
                {
                    var primaryKeyColumns = schema.PrimaryKeys.Select(pkColumn =>
                    {
                        var column = schema.Columns.FirstOrDefault(c => c.Name == pkColumn);
                        if (column != null)
                        {
                            var dataType = column.DataType.ToLower();
                            bool isTextOrBlob = dataType.Contains("text") || dataType.Contains("blob") ||
                                            dataType == "json" || dataType == "xml" || dataType == "bytea";

                            // 修复TEXT/BLOB作为主键的索引前缀语法
                            // OceanBase可能不支持在主键定义中直接使用(100)的语法
                            // 因此这里直接返回列名，对于TEXT/BLOB类型可能需要在表创建前转换为其他类型
                            return $"`{pkColumn}`"; // 移除(100)前缀语法
                        }
                        return $"`{pkColumn}`";
                    });

                    return $"PRIMARY KEY ({string.Join(", ", primaryKeyColumns)})";
                }
                else
                {
                    Logger.LogWarning("跳过创建主键，因为总长度超过MySQL限制，表名: {TableName}", schema.Name);
                    return null;
                }
            }

            return null;
        }
    }
}
