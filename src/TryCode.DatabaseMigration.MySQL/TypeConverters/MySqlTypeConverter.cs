using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.TypeConverters;

namespace TryCode.DatabaseMigration.MySQL.TypeConverters
{
    /// <summary>
    /// MySQL类型转换器
    /// </summary>
    public class MySqlTypeConverter : TypeConverterBase
    {
        /// <summary>
        /// 检查是否支持特定类型的转换
        /// </summary>
        public override bool CanConvert(string sourceType, string targetType)
        {
            if (string.IsNullOrEmpty(sourceType) || string.IsNullOrEmpty(targetType))
                return false;

            var source = sourceType.ToLower();
            var target = targetType.ToLower();

            return (source, target) switch
            {
                // 数值类型转换
                ("tinyint", "bit") => true,
                ("tinyint", "tinyint") => true,
                ("tinyint", "smallint") => true, // 添加
                ("smallint", "smallint") => true,
                ("smallint", "int") => true, // 添加
                ("mediumint", "int") => true,
                ("int", "int") => true,
                ("int", "bigint") => true, // 添加
                ("bigint", "bigint") => true,
                ("decimal", var t) when t.StartsWith("decimal") => true,
                ("float", "float") => true,
                ("double", "float") => true,
                ("real", "real") => true,

                // 字符串类型转换
                ("char", var t) when t.StartsWith("char") => true,
                ("char", "varchar") => true, // 添加
                ("varchar", var t) when t.StartsWith("varchar") => true,
                ("varchar", "text") => true, // 添加
                ("tinytext", "varchar") => true,
                ("text", "varchar") => true,
                ("text", "mediumtext") => true, // 添加
                ("mediumtext", "varchar") => true,
                ("longtext", "varchar") => true,
                ("json", "longtext") => true, // 添加
                // 日期时间类型转换
                ("date", "date") => true,
                ("datetime", "datetime2") => true,
                ("timestamp", "datetime2") => true,
                ("time", "time") => true,
                ("year", "smallint") => true,

                // 二进制类型转换
                ("binary", var t) when t.StartsWith("binary") => true,
                ("varbinary", var t) when t.StartsWith("varbinary") => true,
                ("tinyblob", "varbinary") => true,
                ("blob", "varbinary") => true,
                ("mediumblob", "varbinary") => true,
                ("longblob", "varbinary") => true,

                // 其他类型转换
                ("enum", "varchar") => true,
                ("set", "varchar") => true,
                ("json", "nvarchar") => true,

                // 相同类型
                var (s, t) when s == t => true,

                // 默认情况
                _ => false
            };
        }

        /// <summary>
        /// 将MySQL数据类型转换为目标数据库的数据类型
        /// </summary>
        public override string ConvertDataType(string sourceType, ColumnSchema sourceColumn)
        {
            if (string.IsNullOrEmpty(sourceType))
                throw new ArgumentNullException(nameof(sourceType));

            if (sourceColumn == null)
                throw new ArgumentNullException(nameof(sourceColumn));

            var type = sourceType.ToLower();

            return type switch
            {
                // 数值类型
                "tinyint" when sourceColumn.IsBoolean => "tinyint(1)",
                "tinyint" => "tinyint",
                "smallint" => "smallint",
                "mediumint" => "int",
                "int" => "int",
                "bigint" => "bigint",
                "decimal" => $"decimal({sourceColumn.NumericPrecision ?? 10}, {sourceColumn.Scale ?? 0})",
                "float" => "float",
                "double" => "double",
                "real" => "real",

                // 字符串类型
                "char" => $"char({sourceColumn.MaxLength ?? 1})",
                "varchar" => $"varchar({sourceColumn.MaxLength ?? 255})",
                "tinytext" => "varchar(255)",
                "text" => "text",
                "mediumtext" => "mediumtext",
                "longtext" => "longtext",

                // 日期时间类型
                "date" => "date",
                "datetime" => "datetime2",
                "timestamp" => "timestamp",
                "time" => "time",
                "year" => "smallint",

                // 二进制类型
                "binary" => $"binary({sourceColumn.MaxLength ?? 1})",
                "varbinary" => $"varbinary({sourceColumn.MaxLength ?? 255})",
                "tinyblob" => "varbinary(255)",
                "blob" => "blob",
                "mediumblob" => "mediumblob",
                "longblob" => "longblob",

                // 其他类型
                "enum" => "varchar(255)",
                "set" => "varchar(255)",
                "json" => "longtext",

                _ => throw new NotSupportedException($"不支持的MySQL数据类型: {sourceType}")
            };
        }

        /// <summary>
        /// 将MySQL数据值转换为目标数据库的数据值
        /// </summary>
        public override async Task<object> ConvertValueAsync(object? value, string sourceType, string targetType)
        {
            if (value == null || value == DBNull.Value)
                return null;

            var sourceTypeLower = sourceType.ToLower();
            var targetTypeLower = targetType.ToLower();

            // 处理特殊类型转换
            if (sourceTypeLower == "tinyint" && targetTypeLower == "bit")
            {
                var result = await HandleSpecialTypeAsync(value, sourceType, targetType);
                return result ?? value;
            }

            // 处理JSON类型
            if (sourceTypeLower == "json" && targetTypeLower.Contains("varchar"))
            {
                return value.ToString() ?? string.Empty;
            }

            // 处理ENUM和SET类型
            if ((sourceTypeLower == "enum" || sourceTypeLower == "set") && targetTypeLower.Contains("varchar"))
            {
                return value.ToString() ?? string.Empty;
            }

            // 处理二进制类型
            if (sourceTypeLower.Contains("blob") && targetTypeLower.Contains("varbinary"))
            {
                return value;
            }

            // 处理日期时间类型
            if (sourceTypeLower == "timestamp" && targetTypeLower.Contains("datetime"))
            {
                if (value is DateTime dateTime)
                {
                    return dateTime;
                }
            }

            if (sourceTypeLower == "date" && targetTypeLower.Contains("datetime"))
            {
                if (value is string dateStr && DateTime.TryParse(dateStr, out var dateTime))
                {
                    return dateTime;
                }
            }

            if (sourceTypeLower == "time" && targetTypeLower.Contains("datetime"))
            {
                if (value is string timeStr && TimeSpan.TryParse(timeStr, out var time))
                {
                    return DateTime.Today.Add(time);
                }
            }

            // 处理其他类型
            return value;
        }

        /// <summary>
        /// 处理特殊类型转换
        /// </summary>
        protected override Task<object> HandleSpecialTypeAsync(object value, string sourceType, string targetType)
        {
            if (sourceType.ToLower() == "tinyint" && targetType.ToLower() == "bit")
            {
                if (value is byte b)
                {
                    return Task.FromResult<object>(b != 0);
                }
                if (value is sbyte sb)
                {
                    return Task.FromResult<object>(sb != 0);
                }
                if (value is short s)
                {
                    return Task.FromResult<object>(s != 0);
                }
                if (value is int i)
                {
                    return Task.FromResult<object>(i != 0);
                }
                if (value is long l)
                {
                    return Task.FromResult<object>(l != 0);
                }
            }

            return base.HandleSpecialTypeAsync(value, sourceType, targetType);
        }
    }
}
