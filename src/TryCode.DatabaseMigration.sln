﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.Core", "TryCode.DatabaseMigration.Core\TryCode.DatabaseMigration.Core.csproj", "{8AC3F95D-99A8-419E-9145-184444ADCCCE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.Configuration", "TryCode.DatabaseMigration.Configuration\TryCode.DatabaseMigration.Configuration.csproj", "{4B4D7409-7CBE-4236-91D2-D063026AD2F1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.MySQL", "TryCode.DatabaseMigration.MySQL\TryCode.DatabaseMigration.MySQL.csproj", "{04E4866C-2327-4870-9EC2-8BE3A77C470C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.PostgreSQL", "TryCode.DatabaseMigration.PostgreSQL\TryCode.DatabaseMigration.PostgreSQL.csproj", "{33487B2D-322F-46DA-8FD8-161A0477B6F7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.Parallel", "TryCode.DatabaseMigration.Parallel\TryCode.DatabaseMigration.Parallel.csproj", "{09D28241-F15C-4BE8-A171-F5ABC7425BF9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.Checkpoint", "TryCode.DatabaseMigration.Checkpoint\TryCode.DatabaseMigration.Checkpoint.csproj", "{DB643E87-3E22-44DB-B30D-967976412B14}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.CLI", "TryCode.DatabaseMigration.CLI\TryCode.DatabaseMigration.CLI.csproj", "{4C2254DB-AC7E-4EB7-A706-E3298F9D657F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TryCode.DatabaseMigration.Tests", "TryCode.DatabaseMigration.Tests\TryCode.DatabaseMigration.Tests.csproj", "{8D376883-8477-4B05-8D09-5E6CF81E79B4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8AC3F95D-99A8-419E-9145-184444ADCCCE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8AC3F95D-99A8-419E-9145-184444ADCCCE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8AC3F95D-99A8-419E-9145-184444ADCCCE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8AC3F95D-99A8-419E-9145-184444ADCCCE}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B4D7409-7CBE-4236-91D2-D063026AD2F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B4D7409-7CBE-4236-91D2-D063026AD2F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B4D7409-7CBE-4236-91D2-D063026AD2F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B4D7409-7CBE-4236-91D2-D063026AD2F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{04E4866C-2327-4870-9EC2-8BE3A77C470C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04E4866C-2327-4870-9EC2-8BE3A77C470C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04E4866C-2327-4870-9EC2-8BE3A77C470C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04E4866C-2327-4870-9EC2-8BE3A77C470C}.Release|Any CPU.Build.0 = Release|Any CPU
		{33487B2D-322F-46DA-8FD8-161A0477B6F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33487B2D-322F-46DA-8FD8-161A0477B6F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33487B2D-322F-46DA-8FD8-161A0477B6F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33487B2D-322F-46DA-8FD8-161A0477B6F7}.Release|Any CPU.Build.0 = Release|Any CPU
		{09D28241-F15C-4BE8-A171-F5ABC7425BF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09D28241-F15C-4BE8-A171-F5ABC7425BF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09D28241-F15C-4BE8-A171-F5ABC7425BF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09D28241-F15C-4BE8-A171-F5ABC7425BF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB643E87-3E22-44DB-B30D-967976412B14}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB643E87-3E22-44DB-B30D-967976412B14}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB643E87-3E22-44DB-B30D-967976412B14}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB643E87-3E22-44DB-B30D-967976412B14}.Release|Any CPU.Build.0 = Release|Any CPU
		{4C2254DB-AC7E-4EB7-A706-E3298F9D657F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4C2254DB-AC7E-4EB7-A706-E3298F9D657F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4C2254DB-AC7E-4EB7-A706-E3298F9D657F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4C2254DB-AC7E-4EB7-A706-E3298F9D657F}.Release|Any CPU.Build.0 = Release|Any CPU
		{8D376883-8477-4B05-8D09-5E6CF81E79B4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8D376883-8477-4B05-8D09-5E6CF81E79B4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8D376883-8477-4B05-8D09-5E6CF81E79B4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8D376883-8477-4B05-8D09-5E6CF81E79B4}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
