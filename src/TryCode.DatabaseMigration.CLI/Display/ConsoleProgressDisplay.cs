using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.CLI.Display
{
    /// <summary>
    /// 控制台进度显示
    /// </summary>
    public class ConsoleProgressDisplay : IProgress<MigrationProgress>, IDisposable
    {
        private readonly Dictionary<string, TaskStatus> _taskStatuses = new();
        private readonly char[] _spinnerFrames = new[] { '⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏' };
        private int _spinnerIndex;
        private readonly bool _verbose;
        private readonly bool _showSpinner;
        private readonly object _spinnerLock = new();
        private DateTime _lastUpdate;
        private bool _disposed;
        private readonly SemaphoreSlim _refreshLock = new SemaphoreSlim(1, 1);

        /// <summary>
        /// 任务状态
        /// </summary>
        private class TaskStatus
        {
            public string TableName { get; set; } = string.Empty;
            public string Message { get; set; }= string.Empty;
            public TableProgress? Progress { get; set; }
        }

        /// <summary>
        /// 表进度
        /// </summary>
        private class TableProgress
        {
            public long Total { get; set; }
            public long Current { get; set; }
            
            public double Percentage => Total <= 0 ? 0 : Math.Round((double)Current / Total * 100, 2);
        }

        /// <summary>
        /// 初始化新的<see cref="ConsoleProgressDisplay"/>实例
        /// </summary>
        /// <param name="verbose">是否显示详细信息</param>
        /// <param name="showSpinner">是否显示旋转器</param>
        public ConsoleProgressDisplay(bool verbose = false, bool showSpinner = true)
        {
            _verbose = verbose;
            _showSpinner = showSpinner;
            _lastUpdate = DateTime.Now;
            
            // 清除控制台
            Console.Clear();
        }

        /// <summary>
        /// 报告进度
        /// </summary>
        public void Report(MigrationProgress value)
        {
            if (_disposed) return;

            try
            {
                _refreshLock.Wait();
                
                var message = string.IsNullOrEmpty(value.Stage)
                    ? value.Status
                    : $"{value.Stage}: {value.Status}";
                
                _taskStatuses[value.TableName] = new TaskStatus
                {
                    TableName = value.TableName,
                    Message = message,
                    Progress = value.Total > 0 ? new TableProgress
                    {
                        Total = value.Total,
                        Current = value.Processed
                    } : null
                };
                
                // 如果距离上次更新超过100ms，则刷新显示
                if ((DateTime.Now - _lastUpdate).TotalMilliseconds > 100)
                {
                    RefreshDisplay();
                    _lastUpdate = DateTime.Now;
                }
            }
            finally
            {
                _refreshLock.Release();
            }
        }

        /// <summary>
        /// 刷新显示
        /// </summary>
        private void RefreshDisplay()
        {
            if (_disposed) return;

            // 清除控制台
            Console.SetCursorPosition(0, 0);
            
            // 创建状态文本
            var sb = new StringBuilder();
            sb.AppendLine("=== 数据库迁移进度 ===");
            sb.AppendLine();

            foreach (var status in _taskStatuses)
            {
                if (_showSpinner)
                {
                    var spinner = _spinnerFrames[_spinnerIndex];
                    _spinnerIndex = (_spinnerIndex + 1) % _spinnerFrames.Length;
                    sb.Append($"{spinner} ");
                }
                
                sb.Append($"{status.Key}: {status.Value.Message}");
                
                if (status.Value.Progress != null)
                {
                    sb.Append($" ({status.Value.Progress.Current}/{status.Value.Progress.Total} - {status.Value.Progress.Percentage}%)");
                }
                
                sb.AppendLine();
            }
            
            // 输出到控制台
            Console.Write(sb.ToString());
            
            // 清除光标
            Console.CursorVisible = false;
        }

        /// <summary>
        /// 终止当前行
        /// </summary>
        public void FinishLine()
        {
            if (_disposed) return;
            Console.WriteLine();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            _refreshLock.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
