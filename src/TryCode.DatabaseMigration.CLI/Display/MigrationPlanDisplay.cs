using System;
using System.Collections.Generic;
using System.Linq;
using Spectre.Console;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.CLI.Display
{
    /// <summary>
    /// 迁移计划显示
    /// </summary>
    public class MigrationPlanDisplay
    {
        private readonly List<TableSchema> _tables;
        private readonly List<TableSchema> _sortedTables;
        private readonly Dictionary<string, long> _tableRowCounts;

        /// <summary>
        /// 创建迁移计划显示
        /// </summary>
        /// <param name="tables">所有待迁移的表</param>
        /// <param name="sortedTables">按照依赖关系排序后的表</param>
        /// <param name="tableRowCounts">表行数信息</param>
        public MigrationPlanDisplay(
            List<TableSchema> tables,
            List<TableSchema> sortedTables,
            Dictionary<string, long> tableRowCounts)
        {
            _tables = tables ?? throw new ArgumentNullException(nameof(tables));
            _sortedTables = sortedTables ?? throw new ArgumentNullException(nameof(sortedTables));
            _tableRowCounts = tableRowCounts ?? throw new ArgumentNullException(nameof(tableRowCounts));
        }

        /// <summary>
        /// 显示迁移计划
        /// </summary>
        public void Display()
        {
            AnsiConsole.Clear();
            
            // 设置控制台宽度
            var width = Math.Min(Console.WindowWidth, 120);
            
            var layout = new Layout("Root")
                .SplitRows(
                    new Layout("Header").Size(3),
                    new Layout("Content").SplitRows(
                        new Layout("TablesInfo").Size(12),
                        new Layout("Dependencies").Size(10),
                        new Layout("MigrationOrder").Size(12),
                        new Layout("Summary").Size(10)
                    )
                );
                
            layout["Header"].Update(
                new Panel(
                    new Markup("[bold green]数据库迁移工具[/] - [yellow]迁移计划[/]")
                ).Padding(1, 0).Border(BoxBorder.Rounded)
            );
            
            // 显示表信息
            layout["TablesInfo"].Update(CreateTablesInfoPanel());
            
            // 显示依赖关系
            layout["Dependencies"].Update(CreateDependenciesPanel());
            
            // 显示迁移顺序
            layout["MigrationOrder"].Update(CreateMigrationOrderPanel());
            
            // 显示摘要
            layout["Summary"].Update(CreateSummaryPanel());
            
            AnsiConsole.Write(layout);
        }

        /// <summary>
        /// 创建表信息面板
        /// </summary>
        private Panel CreateTablesInfoPanel()
        {
            var table = new Table()
                .Border(TableBorder.Rounded)
                .Title("[yellow]表信息[/]")
                .AddColumn(new TableColumn("[b]表名[/]").Width(30))
                .AddColumn(new TableColumn("[b]数据量[/]").Width(12))
                .AddColumn(new TableColumn("[b]列数[/]").Width(8))
                .AddColumn(new TableColumn("[b]外键数[/]").Width(8));
                
            // 显示所有表，不再限制数量
            foreach (var tableSchema in _tables)
            {
                _tableRowCounts.TryGetValue(tableSchema.Name, out var rowCount);
                table.AddRow(
                    tableSchema.Name,
                    rowCount.ToString("N0"),
                    tableSchema.Columns.Count.ToString(),
                    tableSchema.ForeignKeys.Count.ToString()
                );
            }
            
            return new Panel(table)
                .Header($"共计 {_tables.Count} 个表需要迁移")
                .Border(BoxBorder.Rounded);
        }
        
        /// <summary>
        /// 创建依赖关系面板
        /// </summary>
        private Panel CreateDependenciesPanel()
        {
            var graph = new Dictionary<string, List<string>>();
            
            // 构建依赖关系图
            foreach (var table in _tables)
            {
                var dependencies = table.ForeignKeys
                    .Select(fk => fk.ReferencedTable)
                    .Distinct()
                    .ToList();
                
                if (dependencies.Any())
                {
                    graph[table.Name] = dependencies;
                }
            }
            
            var tree = new Tree("[yellow]表依赖关系[/]");
            
            if (graph.Count == 0)
            {
                tree.AddNode("[grey]没有发现表之间的依赖关系[/]");
            }
            else
            {
                // 显示所有依赖关系，不再限制数量
                foreach (var entry in graph)
                {
                    var node = tree.AddNode($"[white]{entry.Key}[/] [yellow]依赖于[/]");
                    foreach (var dependency in entry.Value)
                    {
                        node.AddNode($"[green]{dependency}[/]");
                    }
                }
            }
            
            return new Panel(tree).Border(BoxBorder.Rounded);
        }
        
        /// <summary>
        /// 创建迁移顺序面板
        /// </summary>
        private Panel CreateMigrationOrderPanel()
        {
            var table = new Table()
                .Border(TableBorder.Rounded)
                .Title("[yellow]迁移顺序[/]")
                .AddColumn(new TableColumn("[b]序号[/]").Width(6))
                .AddColumn(new TableColumn("[b]表名[/]").Width(30))
                .AddColumn(new TableColumn("[b]被依赖[/]").Width(8));
                
            // 显示所有表的迁移顺序，不再限制数量
            for (int i = 0; i < _sortedTables.Count; i++)
            {
                var isReferenced = _sortedTables.Skip(i + 1)
                    .SelectMany(t => t.ForeignKeys)
                    .Any(fk => fk.ReferencedTable == _sortedTables[i].Name);
                    
                table.AddRow(
                    (i + 1).ToString(),
                    _sortedTables[i].Name,
                    isReferenced ? "[green]是[/]" : "[grey]否[/]"
                );
            }
            
            return new Panel(table)
                .Header("表迁移顺序（被依赖的表优先迁移）")
                .Border(BoxBorder.Rounded);
        }
        
        /// <summary>
        /// 创建摘要面板
        /// </summary>
        private Panel CreateSummaryPanel()
        {
            var totalRows = _tableRowCounts.Values.Sum();
            var tablesWithForeignKeys = _tables.Count(t => t.ForeignKeys.Any());
            
            var table = new Table()
                .Border(TableBorder.Rounded)
                .Title("[yellow]迁移摘要[/]")
                .AddColumn(new TableColumn("[b]指标[/]").Width(20))
                .AddColumn(new TableColumn("[b]值[/]"));
                
            table.AddRow("总表数", _tables.Count.ToString());
            table.AddRow("总数据量", $"{totalRows:N0} 行");
            table.AddRow("有外键依赖的表", $"{tablesWithForeignKeys} 个");
            table.AddRow("无外键依赖的表", $"{_tables.Count - tablesWithForeignKeys} 个");
            
            var panel = new Panel(table).Border(BoxBorder.Rounded);
            
            if (totalRows > 1000000)
            {
                // 创建一个新的面板，包含警告信息
                var warningPanel = new Panel(
                    new Markup("[bold yellow]注意: 数据量较大，迁移可能需要较长时间。建议开启断点续传功能。[/]")
                ).Border(BoxBorder.Rounded);
                
                // 创建一个布局，包含表格和警告信息
                var layout = new Layout()
                    .SplitRows(
                        new Layout().Update(table),
                        new Layout().Size(3).Update(warningPanel)
                    );
                
                return new Panel(layout).Border(BoxBorder.Rounded);
            }
            
            return panel;
        }
    }
}
