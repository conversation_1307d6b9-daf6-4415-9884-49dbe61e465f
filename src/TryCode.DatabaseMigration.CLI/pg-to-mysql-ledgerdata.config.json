{
  "Source": {
    "Type": "PostgreSQL",
    "ConnectionString": "Host=***********;Port=5866;Database=ledger_test;User Id=test;Password=**$!4mGo5STvQ3;SslMode=Disable;",
    "BatchSize": 500,
    "CommandTimeout": 180
  },
//  "Target": {
//    "Type": "MySQL",
//    "ConnectionString": "Server=*************;Port=2881;Database=ybt_ledger;User=ybt@test;Password=******;",
//    "BatchSize": 200,
//    "CommandTimeout": 180
//  },
  "Target": {
    "Type": "MySQL",
    "ConnectionString": "Server=************;Port=2881;Database=ybt_ledger;User=ybt@ybt;Password=**************;",
    "BatchSize": 200,
    "CommandTimeout": 180
  },
  "Tables":[
//    "LedgerData_LedgerAuditedData",
//    "LedgerData_LedgerAuditedData_0",
//    "LedgerData_LedgerAuditedData_1",
//    "LedgerData_LedgerAuditedData_2",
//    "LedgerData_LedgerAuditedData_3",
//    "LedgerData_LedgerAuditedData_4",
//    "LedgerData_LedgerAuditedData_5",
//    "LedgerData_LedgerAuditedData_6",
//    "LedgerData_LedgerAuditedData_7",
//    "LedgerData_LedgerAuditedData_8",
//    "LedgerData_LedgerAuditedData_9",
//    "LedgerData_LedgerAuditedData_10",
//    "LedgerData_LedgerAuditedData_11",
//    "LedgerData_LedgerAuditedData_12",
//    "LedgerData_LedgerAuditedData_13",
//    "LedgerData_LedgerAuditedData_14",
//    "LedgerData_LedgerAuditedData_15",
//    "LedgerData_LedgerAuditedData_16",
//    "LedgerData_LedgerAuditedData_17",
//    "LedgerData_LedgerAuditedData_18",
//    "LedgerData_LedgerAuditedData_19",
//    "LedgerData_LedgerAuditedData_20",
//    "LedgerData_LedgerAuditedData_21",
//    "LedgerData_LedgerAuditedData_22",
//    "LedgerData_LedgerAuditedData_23",
//    "LedgerData_LedgerAuditedData_24",
//    "LedgerData_LedgerAuditedData_25",
//    "LedgerData_LedgerAuditedData_26",
//    "LedgerData_LedgerAuditedData_27",
//    "LedgerData_LedgerAuditedData_28",
//    "LedgerData_LedgerAuditedData_29",
//    "LedgerData_LedgerAuditedData_30",
//    "LedgerData_LedgerAuditedData_31",
//    "LedgerData_LedgerAuditedData_32",
//    "LedgerData_LedgerAuditedData_33",
//    "LedgerData_LedgerAuditedData_34",
//    "LedgerData_LedgerAuditedData_35",
//    "LedgerData_LedgerAuditedData_36",
//    "LedgerData_LedgerAuditedData_37",
//    "LedgerData_LedgerAuditedData_38",
//    "LedgerData_LedgerAuditedData_39",
//    "LedgerData_LedgerAuditedData_40",
//    "LedgerData_LedgerAuditedData_41",
//    "LedgerData_LedgerAuditedData_42",
//    "LedgerData_LedgerAuditedData_43",
//    "LedgerData_LedgerAuditedData_44",
//    "LedgerData_LedgerAuditedData_45",
//    "LedgerData_LedgerAuditedData_46",
//    "LedgerData_LedgerAuditedData_47",
//    "LedgerData_LedgerAuditedData_48",
//    "LedgerData_LedgerAuditedData_49",
//    "LedgerData_LedgerAuditedData_50",
//    "LedgerData_LedgerAuditedData_51",
//    "LedgerData_LedgerAuditedData_52",
//    "LedgerData_LedgerAuditedData_53",
//    "LedgerData_LedgerAuditedData_54",
//    "LedgerData_LedgerAuditedData_55",
//    "LedgerData_LedgerAuditedData_56",
//    "LedgerData_LedgerAuditedData_57",
//    "LedgerData_LedgerAuditedData_58",
//    "LedgerData_LedgerAuditedData_59",
//    "LedgerData_LedgerAuditedData_60",
//    "LedgerData_LedgerAuditedData_61",
//    "LedgerData_LedgerAuditedData_62",
//    "LedgerData_LedgerAuditedData_63",
//    "LedgerData_LedgerAuditedData_64",
//    "LedgerData_LedgerAuditedData_65",
//    "LedgerData_LedgerAuditedData_66",
//    "LedgerData_LedgerAuditedData_67",
//    "LedgerData_LedgerAuditedData_68",
//    "LedgerData_LedgerAuditedData_69",
//    "LedgerData_LedgerAuditedData_70",
//    "LedgerData_LedgerAuditedData_71",
//    "LedgerData_LedgerAuditedData_72",
//    "LedgerData_LedgerAuditedData_73",
//    "LedgerData_LedgerAuditedData_74",
//    "LedgerData_LedgerAuditedData_75",
//    "LedgerData_LedgerAuditedData_76",
//    "LedgerData_LedgerAuditedData_77",
//    "LedgerData_LedgerAuditedData_78",
//    "LedgerData_LedgerAuditedData_79",
//    "LedgerData_LedgerAuditedData_80",
//    "LedgerData_LedgerAuditedData_81",
//    "LedgerData_LedgerAuditedData_82",
//    "LedgerData_LedgerAuditedData_83",
//    "LedgerData_LedgerAuditedData_84",
//    "LedgerData_LedgerAuditedData_85",
//    "LedgerData_LedgerAuditedData_86",
//    "LedgerData_LedgerAuditedData_87",
//    "LedgerData_LedgerAuditedData_88",
//    "LedgerData_LedgerAuditedData_89",
//    "LedgerData_LedgerAuditedData_90",
//    "LedgerData_LedgerAuditedData_91",
//    "LedgerData_LedgerAuditedData_92",
//    "LedgerData_LedgerAuditedData_93",
//    "LedgerData_LedgerAuditedData_94",
//    "LedgerData_LedgerAuditedData_95",
//    "LedgerData_LedgerAuditedData_96",
//    "LedgerData_LedgerAuditedData_97",
//    "LedgerData_LedgerAuditedData_98",
//    "LedgerData_LedgerAuditedData_99"
  ],
  "ExcludedTables": [
    "hg_t_audit_log",
    "LedgerData_LedgerAuditedData",
    "LedgerData_LedgerAuditedData_0",
    "LedgerData_LedgerAuditedData_1",
    "LedgerData_LedgerAuditedData_2",
    "LedgerData_LedgerAuditedData_3",
    "LedgerData_LedgerAuditedData_4",
    "LedgerData_LedgerAuditedData_5",
    "LedgerData_LedgerAuditedData_6",
    "LedgerData_LedgerAuditedData_7",
    "LedgerData_LedgerAuditedData_8",
    "LedgerData_LedgerAuditedData_9",
    "LedgerData_LedgerAuditedData_10",
    "LedgerData_LedgerAuditedData_11",
    "LedgerData_LedgerAuditedData_12",
    "LedgerData_LedgerAuditedData_13",
    "LedgerData_LedgerAuditedData_14",
    "LedgerData_LedgerAuditedData_15",
    "LedgerData_LedgerAuditedData_16",
    "LedgerData_LedgerAuditedData_17",
    "LedgerData_LedgerAuditedData_18",
    "LedgerData_LedgerAuditedData_19",
    "LedgerData_LedgerAuditedData_20",
    "LedgerData_LedgerAuditedData_21",
    "LedgerData_LedgerAuditedData_22",
    "LedgerData_LedgerAuditedData_23",
    "LedgerData_LedgerAuditedData_24",
    "LedgerData_LedgerAuditedData_25",
    "LedgerData_LedgerAuditedData_26",
    "LedgerData_LedgerAuditedData_27",
    "LedgerData_LedgerAuditedData_28",
    "LedgerData_LedgerAuditedData_29",
    "LedgerData_LedgerAuditedData_30",
    "LedgerData_LedgerAuditedData_31",
    "LedgerData_LedgerAuditedData_32",
    "LedgerData_LedgerAuditedData_33",
    "LedgerData_LedgerAuditedData_34",
    "LedgerData_LedgerAuditedData_35",
    "LedgerData_LedgerAuditedData_36",
    "LedgerData_LedgerAuditedData_37",
    "LedgerData_LedgerAuditedData_38",
    "LedgerData_LedgerAuditedData_39",
    "LedgerData_LedgerAuditedData_40",
    "LedgerData_LedgerAuditedData_41",
    "LedgerData_LedgerAuditedData_42",
    "LedgerData_LedgerAuditedData_43",
    "LedgerData_LedgerAuditedData_44",
    "LedgerData_LedgerAuditedData_45",
    "LedgerData_LedgerAuditedData_46",
    "LedgerData_LedgerAuditedData_47",
    "LedgerData_LedgerAuditedData_48",
    "LedgerData_LedgerAuditedData_49",
    "LedgerData_LedgerAuditedData_50",
    "LedgerData_LedgerAuditedData_51",
    "LedgerData_LedgerAuditedData_52",
    "LedgerData_LedgerAuditedData_53",
    "LedgerData_LedgerAuditedData_54",
    "LedgerData_LedgerAuditedData_55",
    "LedgerData_LedgerAuditedData_56",
    "LedgerData_LedgerAuditedData_57",
    "LedgerData_LedgerAuditedData_58",
    "LedgerData_LedgerAuditedData_59",
    "LedgerData_LedgerAuditedData_60",
    "LedgerData_LedgerAuditedData_61",
    "LedgerData_LedgerAuditedData_62",
    "LedgerData_LedgerAuditedData_63",
    "LedgerData_LedgerAuditedData_64",
    "LedgerData_LedgerAuditedData_65",
    "LedgerData_LedgerAuditedData_66",
    "LedgerData_LedgerAuditedData_67",
    "LedgerData_LedgerAuditedData_68",
    "LedgerData_LedgerAuditedData_69",
    "LedgerData_LedgerAuditedData_70",
    "LedgerData_LedgerAuditedData_71",
    "LedgerData_LedgerAuditedData_72",
    "LedgerData_LedgerAuditedData_73",
    "LedgerData_LedgerAuditedData_74",
    "LedgerData_LedgerAuditedData_75",
    "LedgerData_LedgerAuditedData_76",
    "LedgerData_LedgerAuditedData_77",
    "LedgerData_LedgerAuditedData_78",
    "LedgerData_LedgerAuditedData_79",
    "LedgerData_LedgerAuditedData_80",
    "LedgerData_LedgerAuditedData_81",
    "LedgerData_LedgerAuditedData_82",
    "LedgerData_LedgerAuditedData_83",
    "LedgerData_LedgerAuditedData_84",
    "LedgerData_LedgerAuditedData_85",
    "LedgerData_LedgerAuditedData_86",
    "LedgerData_LedgerAuditedData_87",
    "LedgerData_LedgerAuditedData_88",
    "LedgerData_LedgerAuditedData_89",
    "LedgerData_LedgerAuditedData_90",
    "LedgerData_LedgerAuditedData_91",
    "LedgerData_LedgerAuditedData_92",
    "LedgerData_LedgerAuditedData_93",
    "LedgerData_LedgerAuditedData_94",
    "LedgerData_LedgerAuditedData_95",
    "LedgerData_LedgerAuditedData_96",
    "LedgerData_LedgerAuditedData_97",
    "LedgerData_LedgerAuditedData_98",
    "LedgerData_LedgerAuditedData_99"
  ],
  "EnableCheckpoint": true,
  "CheckpointFilePath": "pgtomysql_migration_checkpoint.json",
  "SkipSchemaCreation": false,
  "SkipForeignKeys": false,
  "CreateSchemaForExcludedTables": false,
  "MaxDegreeOfParallelism": 4,
  "Logging": {
    "LogLevel": "Information",
    "FilePath": "pgtomysql_migration.log",
    "ConsoleOutput": true
  },
  "TypeMappings": {
    "array": "longtext",
    "_int4": "longtext",
    "_int8": "longtext",
    "_text": "longtext",
    "_varchar": "longtext",
    "_float4": "longtext",
    "_float8": "longtext",
    "_bool": "longtext",
    "_date": "longtext",
    "_timestamp": "longtext",
    "jsonb": "longtext",
    "json": "longtext",
    "uuid": "char(36) CHARACTER SET ascii",
    "timestamp with time zone": "datetime(6)",
    "timestamp without time zone": "datetime(6)",
    "timestamp": "datetime(6)",
    "date": "date",
    "time with time zone": "time(6)",
    "time without time zone": "time(6)",
    "time": "time(6)",
    "bytea": "longblob",
    "interval": "varchar(255)",
    "cidr": "varchar(45)",
    "inet": "varchar(45)",
    "macaddr": "varchar(17)",
    "text": "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "character varying": "varchar([num]) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "varchar": "varchar([num]) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "char": "char([num]) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "bit": "bit(64)",
    "bit varying": "varchar(255)",
    "money": "decimal(19,4)",
    "point": "varchar(100)",
    "line": "varchar(255)",
    "lseg": "varchar(255)",
    "box": "varchar(255)",
    "path": "varchar(255)",
    "polygon": "varchar(255)",
    "circle": "varchar(255)",
    "hstore": "longtext",
    "int4range": "varchar(255)",
    "int8range": "varchar(255)",
    "numrange": "varchar(255)",
    "tsrange": "varchar(255)",
    "tstzrange": "varchar(255)",
    "daterange": "varchar(255)",
    "xml": "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "unknown": "varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "integer": "int",
    "bigint": "bigint",
    "boolean": "tinyint(1)",
    "smallint": "smallint",
    "real": "float",
    "double precision": "double",
    "numeric": "decimal(18,6)",
    "decimal": "decimal(18,6)",
    "text[]": "longtext"
  }
}