{"Source": {"Type": "PostgreSQL", "ConnectionString": "Host=localhost;Database=source_db;Username=postgres;Password=password", "BatchSize": 1000, "CommandTimeout": 30}, "Target": {"Type": "MySQL", "ConnectionString": "Server=localhost;Database=target_db;Uid=root;Pwd=password;", "BatchSize": 1000, "CommandTimeout": 30}, "Tables": ["users", "orders", "products"], "EnableCheckpoint": true, "CheckpointFilePath": "migration_checkpoint.json", "SkipSchemaCreation": false, "SkipForeignKeys": false, "MaxDegreeOfParallelism": 4, "Logging": {"LogLevel": "Information", "FilePath": "migration.log", "ConsoleOutput": true}}