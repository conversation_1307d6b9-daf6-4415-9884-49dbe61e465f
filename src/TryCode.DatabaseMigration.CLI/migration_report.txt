===== 数据库迁移报告 =====

开始时间: 2025-06-20 18:21:13
结束时间: 2025-06-20 18:21:22
执行时间: 0小时 0分钟 8秒

总表数: 0
成功: 0 (0%)
失败: 12 (0%)
跳过: 0 (0%)

总行数: 1,265,433
已迁移: 180,892 (14.29%)

===== 失败表列表 =====

- AbpAuditLogActions
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpAuditLogs
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpEntityChanges
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpEntityPropertyChanges
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpSecurityLogs
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpUserClaims
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppNotifications
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserNotifications
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserSubscribes
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerDataUpdateLogs
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerDepartments
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 168,892，已迁移: 168,892 (100%)

- Ledger_LedgerUsers
  原因: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 1,096,541，已迁移: 12,000 (1.09%)

===== 详细迁移状态 =====

表名	状态	总行数	已迁移	迁移率	耗时
--------------------------------------------------
AbpAuditLogActions	失败	0	0	0%	0秒
AbpAuditLogs	失败	0	0	0%	0秒
AbpEntityChanges	失败	0	0	0%	0秒
AbpEntityPropertyChanges	失败	0	0	0%	0秒
AbpSecurityLogs	失败	0	0	0%	0秒
AbpUserClaims	失败	0	0	0%	0秒
AppNotifications	失败	0	0	0%	0秒
AppUserNotifications	失败	0	0	0%	0秒
AppUserSubscribes	失败	0	0	0%	0秒
Ledger_LedgerDataUpdateLogs	失败	0	0	0%	0秒
Ledger_LedgerDepartments	失败	168,892	168,892	100%	0秒
Ledger_LedgerUsers	失败	1,096,541	12,000	1.09%	0秒
