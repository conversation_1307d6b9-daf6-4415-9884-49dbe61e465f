===== 数据库迁移报告 =====

开始时间: 2025-06-20 16:02:14
结束时间: 2025-06-20 16:43:39
执行时间: 0小时 41分钟 24秒

总表数: 0
成功: 0 (0%)
失败: 285 (0%)
跳过: 0 (0%)

总行数: 6,165,469
已迁移: 6,051,577 (98.15%)

===== 失败表列表 =====

- __EFMigrationsHistory
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 391，已迁移: 391 (100%)

- AbpAuditLogActions
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AbpAuditLogs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AbpClaimTypes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 35，已迁移: 35 (100%)

- AbpEditions
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpEntityChanges
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AbpEntityPropertyChanges
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AbpFeatureValues
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 24，已迁移: 24 (100%)

- AbpLinkUsers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpLocalizationLanguages
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- AbpLocalizationResources
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- AbpLocalizationTexts
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 20，已迁移: 20 (100%)

- AbpOrganizationUnitRoles
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpOrganizationUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 10，已迁移: 10 (100%)

- AbpPermissionGrants
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 5,696，已迁移: 5,696 (100%)

- AbpRoleClaims
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- AbpRoles
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 18，已迁移: 18 (100%)

- AbpSecurityLogs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AbpSettings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 13，已迁移: 13 (100%)

- AbpTenantConnectionStrings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpTenants
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- AbpUserClaims
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AbpUserLogins
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 269，已迁移: 269 (100%)

- AbpUserOrganizationUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3，已迁移: 3 (100%)

- AbpUserRoles
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 523,722，已迁移: 523,722 (100%)

- AbpUserRoles_923bf
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 455,580，已迁移: 455,580 (100%)

- AbpUsers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 483,309，已迁移: 483,309 (100%)

- AbpUserTokens
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpWebhooksEvents
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpWebhooksSendAttempts
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AbpWebhooksSubscriptions
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- Airport_AssetLiabilities
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_BudgetAccounts
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_BudgetOrganizations
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_FinanceAuditBatches
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_FinanceAuditedDatas
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_FinanceProjects
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_NoFinanceProjects
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_NoFinancialBudgetProjects
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Airport_OverheadCostCenters
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppChatGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppGroupChatBlacks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppGroupMessages
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppNotificationDefinitionGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppNotificationDefinitions
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppNotifications
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AppPlatformDataItems
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 27，已迁移: 27 (100%)

- AppPlatformDatas
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3，已迁移: 3 (100%)

- AppPlatformLayouts
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2，已迁移: 2 (100%)

- AppPlatformMenus
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 263，已迁移: 263 (100%)

- AppPlatformPackageBlobs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppPlatformPackages
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppPlatformRoleMenus
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 585，已迁移: 585 (100%)

- AppPlatformUserFavoriteMenus
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppPlatformUserMenus
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 5,281，已迁移: 5,281 (100%)

- AppPlatformVersion
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppPlatformVersionFile
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserChatCards
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 49,825，已迁移: 49,825 (100%)

- AppUserChatFriends
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserChatGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserChatSettings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 49,825，已迁移: 49,825 (100%)

- AppUserGroupCards
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserMessages
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- AppUserNotifications
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- AppUserSubscribes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Bank_Ledger_BaseInfoStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Bank_Ledger_ResettleHelpEducates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Bank_Ledger_RiskDetermines
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Bank_Ledger_ServeSentenceAndCriminalInfoStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- BigScreenAuditDatas
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 39，已迁移: 39 (100%)

- BSPUser
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 455,368，已迁移: 455,368 (100%)

- dept_new_zhong
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 194，已迁移: 194 (100%)

- excelbookuser
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,858，已迁移: 1,858 (100%)

- excepbook
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 92，已迁移: 92 (100%)

- ExistingTables
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 8,073，已迁移: 8,073 (100%)

- fill_user_book
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 205,200，已迁移: 205,200 (100%)

- Filling_AreaOrganizationUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 98,986，已迁移: 98,986 (100%)

- Filling_CollaborativeReports
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,111，已迁移: 1,111 (100%)

- Filling_FileInfos
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,570，已迁移: 1,570 (100%)

- Filling_FillingConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 33，已迁移: 33 (100%)

- Filling_PlanTaskAreaOrganizationUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 14,241，已迁移: 14,241 (100%)

- Filling_PlanTaskReportTableTemplates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3,832，已迁移: 3,832 (100%)

- Filling_PlanTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3,627，已迁移: 3,627 (100%)

- Filling_PlanTaskStaffAreaOrganizationUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 217，已迁移: 217 (100%)

- Filling_PlanTaskStaffs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,913，已迁移: 1,913 (100%)

- Filling_ReportMessageInfos
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Filling_ReportTableRows
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2,042，已迁移: 2,042 (100%)

- Filling_ReportTables
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,997，已迁移: 1,997 (100%)

- Filling_ReportTableTemplates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 4,819，已迁移: 4,819 (100%)

- Filling_ReportTaskAreaOrganizationUnitAudits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 957，已迁移: 957 (100%)

- Filling_ReportTaskAreaOrganizationUnitFillers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,067，已迁移: 1,067 (100%)

- Filling_ReportTaskAreaOrganizationUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,911，已迁移: 1,911 (100%)

- Filling_ReportTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 4,883，已迁移: 4,883 (100%)

- Filling_Staff
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 483,643，已迁移: 483,643 (100%)

- Filling_StaffPlanTaskHides
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 865，已迁移: 865 (100%)

- Filling_TableLedgerRuleConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Filling_TableTemplateColumns
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 175,494，已迁移: 175,494 (100%)

- Filling_TableTemplateDataRows
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 370，已迁移: 370 (100%)

- Filling_YkzOrgUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 97,762，已迁移: 97,762 (100%)

- hg_t_audit_log
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerApiResourceClaims
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 35，已迁移: 35 (100%)

- IdentityServerApiResourceProperties
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerApiResources
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3，已迁移: 3 (100%)

- IdentityServerApiResourceScopes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2，已迁移: 2 (100%)

- IdentityServerApiResourceSecrets
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerApiScopeClaims
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerApiScopeProperties
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerApiScopes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3，已迁移: 3 (100%)

- IdentityServerClientClaims
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerClientCorsOrigins
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- IdentityServerClientGrantTypes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 19，已迁移: 19 (100%)

- IdentityServerClientIdPRestrictions
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerClientPostLogoutRedirectUris
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 6，已迁移: 6 (100%)

- IdentityServerClientProperties
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerClientRedirectUris
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 12，已迁移: 12 (100%)

- IdentityServerClients
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 11，已迁移: 11 (100%)

- IdentityServerClientScopes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 58，已迁移: 58 (100%)

- IdentityServerClientSecrets
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 13，已迁移: 13 (100%)

- IdentityServerDeviceFlowCodes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerIdentityResourceClaims
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 34，已迁移: 34 (100%)

- IdentityServerIdentityResourceProperties
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- IdentityServerIdentityResources
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 8，已迁移: 8 (100%)

- IdentityServerPersistedGrants
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,023，已迁移: 1,023 (100%)

- Ledger_AjcBaseInfoStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_ApiAuxiliaryFillingRules
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 179，已迁移: 179 (100%)

- Ledger_ApiAuxiliaryFillings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 53，已迁移: 53 (100%)

- Ledger_ApiAuxiliaryFillSources
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2，已迁移: 2 (100%)

- Ledger_ApiDataSetMappingFields
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 278，已迁移: 278 (100%)

- Ledger_ApiDataSets
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 6，已迁移: 6 (100%)

- Ledger_ApiRequestParameters
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 6，已迁移: 6 (100%)

- Ledger_AssociatedConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 215，已迁移: 215 (100%)

- Ledger_AssociatedSynConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 296，已迁移: 296 (100%)

- Ledger_AssociatedSyns
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 43，已迁移: 43 (100%)

- Ledger_BaseInfoStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,072，已迁移: 1,072 (100%)

- Ledger_BigViewTypicalScenarios
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 44，已迁移: 44 (100%)

- Ledger_BigViewTypicalScenarioUploadRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_CommonGoalsTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_DataItemsManagements
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_DataItemsUpdateRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_DataSources
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 26，已迁移: 26 (100%)

- Ledger_DbTableFields
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 23,219，已迁移: 23,219 (100%)

- Ledger_DeductPoints
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 11，已迁移: 11 (100%)

- Ledger_DeductPointsPublishes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 61，已迁移: 61 (100%)

- Ledger_DeductPointsRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_Disabilities
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_HierarchicalLedgerDataPushOriginPlans
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_HierarchicalLedgers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3，已迁移: 3 (100%)

- Ledger_HierarchicalLedgerTaskItems
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2，已迁移: 2 (100%)

- Ledger_HistoryEvaluates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_IndicatorCockpitConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 35，已迁移: 35 (100%)

- Ledger_IndicatorCockpits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 45,623，已迁移: 45,623 (100%)

- Ledger_LedgerAuditBatches
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 73,785，已迁移: 73,785 (100%)

- Ledger_LedgerAuditBatchFlowNodes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 397，已迁移: 397 (100%)

- Ledger_LedgerAuditBatchFlows
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 169，已迁移: 169 (100%)

- Ledger_LedgerAuditBatchStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 67,421，已迁移: 67,421 (100%)

- Ledger_LedgerAuxiliaryFillingConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 172，已迁移: 172 (100%)

- Ledger_LedgerAuxiliaryFillingPreviews
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 159，已迁移: 159 (100%)

- Ledger_LedgerAuxiliaryFillings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 51，已迁移: 51 (100%)

- Ledger_LedgerChangeRecordDetails
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 135，已迁移: 135 (100%)

- Ledger_LedgerChangeRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 73，已迁移: 73 (100%)

- Ledger_LedgerDataAnalyses
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 539，已迁移: 539 (100%)

- Ledger_LedgerDataComparisonRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 38，已迁移: 38 (100%)

- Ledger_LedgerDataSetAuxiliaryFillingConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2，已迁移: 2 (100%)

- Ledger_LedgerDataSetAuxiliaryFillings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 2，已迁移: 2 (100%)

- Ledger_LedgerDataSyncConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- Ledger_LedgerDataSyncFields
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- Ledger_LedgerDataSyncTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- Ledger_LedgerDataUpdateLogs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerDepartmentDataDetailStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 86,453，已迁移: 86,453 (100%)

- Ledger_LedgerDepartmentDataStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,388，已迁移: 1,388 (100%)

- Ledger_LedgerDepartments
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 168,892，已迁移: 55,000 (32.57%)

- Ledger_LedgerHierarchicalAuthorizations
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1，已迁移: 1 (100%)

- Ledger_LedgerPermissionsAuthorizationModes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,263,126，已迁移: 1,263,126 (100%)

- Ledger_LedgerRunwayRelations
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerRunways
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_Ledgers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 8,404，已迁移: 8,404 (100%)

- Ledger_LedgerStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerTemplates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerTypes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 1,259，已迁移: 1,259 (100%)

- Ledger_LedgerUsers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_LedgerWayAndRelations
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_LlmMessages
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_MyLedgerExportRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_OneVoteRejectPublishes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_OneVoteRejectRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_OneVoteRejects
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_OnLineAnalysisConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_OnLineAnalysisEchartConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_PlanProgressConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_ResettleHelpEducates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_RiskDetermines
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_ServeSentenceAndCriminalInfoStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_SocialAssistances
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_SourceDbTableInfos
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 926，已迁移: 926 (100%)

- Ledger_SourceLedgerFillStatistics
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_SourceLedgerTypeRelations
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_SourceLedgerTypes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_StarEvaluatePublishes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 779，已迁移: 779 (100%)

- Ledger_StarEvaluateRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 0，已迁移: 0 (0%)

- Ledger_StarEvaluates
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 41，已迁移: 41 (100%)

- Ledger_StreetBigDataEchartAnalyses
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_StreetBigDataEchartAnalysisConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableDataExportRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableDataImportRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableDataSetJoinConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableDataSetJoinFieldConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableDataSetMappingFields
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 3,575，已迁移: 3,575 (100%)

- Ledger_TableDataSets
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 145，已迁移: 145 (100%)

- Ledger_TableDataSetSourceDbTableInfos
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableFieldCalculateRules
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableFieldClientSettings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableFieldGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 7,017，已迁移: 7,017 (100%)

- Ledger_TableFieldMultiples
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableFields
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 290,271，已迁移: 290,271 (100%)

- Ledger_TableFiledValidateRules
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TableInfos
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 9,037，已迁移: 9,037 (100%)

- Ledger_TableQueryConfigs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TaskChargePeople
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Ledger_TelecomTaskItems
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- NewFeature_AuthorityAudits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- NewFeature_AutomatedVerifications
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- NewFeature_DataProcesses
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- NewFeature_FunctionLogs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- NewFeature_ProgressManagementItems
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- NewFeature_ProgressManagements
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_ChartDicDatas
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_ChartGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_ChartListDatas
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_Charts
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_ChartTimeFlowDatas
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_DepartmentFavoriteGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_DepartmentFavorites
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_DepartmentGroupDepartments
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_DepartmentGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_Departments
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 258,980，已迁移: 258,980 (100%)

- Platform_Departments_error
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 219,108，已迁移: 219,108 (100%)

- Platform_QuestionFeedbacks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_RegionFavoriteGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_RegionFavorites
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_Regions
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_ReportLedgerNotices
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_Routes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_SyncRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_UserDepartmentFavoriteGroups
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_UserDepartmentRoles
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_UserDepartments
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_Users
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 外键创建
  行数: 483,649，已迁移: 483,649 (100%)

- Platform_WebWidgets
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_WFDataProcesses
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_WFPlanTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_WFTaskItems
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_WorkToDoRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_YbtUsers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_YkzOrganizations
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Platform_YkzOrgUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_ApplicationDepartments
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_Applications
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_ApplicationUsers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_AppTaskItems
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_AppTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_DataFieldMappings
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_DataSources
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_ProcessFields
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- RPA_ProcessServices
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- SrcOrgs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- TableInfosToCheck
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Temp_Batch
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Temp_Ledger_Department_Fill_Fix
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Temp_Ledger_Department_Operation_Count
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Temp_Ledger_Department_Operation_Count_NewV1
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- TempOrgs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- TempUsers
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- Test_DEC
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- TK_BackgroundJobLogs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- TK_BackgroundJobs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- UserLoginRecords
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowProcesses
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowSchemeAuths
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowSchemeInfoPermissionGrants
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowSchemeInfos
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowSchemes
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowStamps
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowTaskLogs
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowTasks
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

- WF_WorkflowUnits
  原因: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
  阶段: 数据迁移
  行数: 0，已迁移: 0 (0%)

===== 详细迁移状态 =====

表名	状态	总行数	已迁移	迁移率	耗时
--------------------------------------------------
__EFMigrationsHistory	失败	391	391	100%	1秒
AbpAuditLogActions	失败	0	0	0%	0秒
AbpAuditLogs	失败	0	0	0%	0秒
AbpClaimTypes	失败	35	35	100%	2秒
AbpEditions	失败	0	0	0%	3秒
AbpEntityChanges	失败	0	0	0%	0秒
AbpEntityPropertyChanges	失败	0	0	0%	0秒
AbpFeatureValues	失败	24	24	100%	4秒
AbpLinkUsers	失败	0	0	0%	5秒
AbpLocalizationLanguages	失败	1	1	100%	6秒
AbpLocalizationResources	失败	1	1	100%	7秒
AbpLocalizationTexts	失败	20	20	100%	8秒
AbpOrganizationUnitRoles	失败	0	0	0%	11秒
AbpOrganizationUnits	失败	10	10	100%	9秒
AbpPermissionGrants	失败	5,696	5,696	100%	11秒
AbpRoleClaims	失败	1	1	100%	12秒
AbpRoles	失败	18	18	100%	8秒
AbpSecurityLogs	失败	0	0	0%	0秒
AbpSettings	失败	13	13	100%	13秒
AbpTenantConnectionStrings	失败	0	0	0%	15秒
AbpTenants	失败	1	1	100%	14秒
AbpUserClaims	失败	0	0	0%	0秒
AbpUserLogins	失败	269	269	100%	8分16秒
AbpUserOrganizationUnits	失败	3	3	100%	8分17秒
AbpUserRoles	失败	523,722	523,722	100%	8分18秒
AbpUserRoles_923bf	失败	455,580	455,580	100%	8分19秒
AbpUsers	失败	483,309	483,309	100%	8分15秒
AbpUserTokens	失败	0	0	0%	8分20秒
AbpWebhooksEvents	失败	0	0	0%	8分21秒
AbpWebhooksSendAttempts	失败	0	0	0%	8分22秒
AbpWebhooksSubscriptions	失败	1	1	100%	8分23秒
Airport_AssetLiabilities	失败	0	0	0%	8分24秒
Airport_BudgetAccounts	失败	0	0	0%	8分25秒
Airport_BudgetOrganizations	失败	0	0	0%	8分26秒
Airport_FinanceAuditBatches	失败	0	0	0%	8分26秒
Airport_FinanceAuditedDatas	失败	0	0	0%	8分27秒
Airport_FinanceProjects	失败	0	0	0%	8分28秒
Airport_NoFinanceProjects	失败	0	0	0%	8分29秒
Airport_NoFinancialBudgetProjects	失败	0	0	0%	8分30秒
Airport_OverheadCostCenters	失败	0	0	0%	8分31秒
AppChatGroups	失败	0	0	0%	8分32秒
AppGroupChatBlacks	失败	0	0	0%	8分33秒
AppGroupMessages	失败	0	0	0%	8分34秒
AppNotificationDefinitionGroups	失败	0	0	0%	8分34秒
AppNotificationDefinitions	失败	0	0	0%	8分35秒
AppNotifications	失败	0	0	0%	0秒
AppPlatformDataItems	失败	27	27	100%	8分37秒
AppPlatformDatas	失败	3	3	100%	8分36秒
AppPlatformLayouts	失败	2	2	100%	8分38秒
AppPlatformMenus	失败	263	263	100%	8分38秒
AppPlatformPackageBlobs	失败	0	0	0%	8分40秒
AppPlatformPackages	失败	0	0	0%	8分39秒
AppPlatformRoleMenus	失败	585	585	100%	8分41秒
AppPlatformUserFavoriteMenus	失败	0	0	0%	8分42秒
AppPlatformUserMenus	失败	5,281	5,281	100%	8分42秒
AppPlatformVersion	失败	0	0	0%	8分43秒
AppPlatformVersionFile	失败	0	0	0%	8分44秒
AppUserChatCards	失败	49,825	49,825	100%	8分45秒
AppUserChatFriends	失败	0	0	0%	8分46秒
AppUserChatGroups	失败	0	0	0%	8分47秒
AppUserChatSettings	失败	49,825	49,825	100%	8分48秒
AppUserGroupCards	失败	0	0	0%	8分49秒
AppUserMessages	失败	0	0	0%	8分50秒
AppUserNotifications	失败	0	0	0%	0秒
AppUserSubscribes	失败	0	0	0%	0秒
Bank_Ledger_BaseInfoStatistics	失败	0	0	0%	8分50秒
Bank_Ledger_ResettleHelpEducates	失败	0	0	0%	8分51秒
Bank_Ledger_RiskDetermines	失败	0	0	0%	8分52秒
Bank_Ledger_ServeSentenceAndCriminalInfoStatistics	失败	0	0	0%	8分53秒
BigScreenAuditDatas	失败	39	39	100%	8分54秒
BSPUser	失败	455,368	455,368	100%	8分55秒
dept_new_zhong	失败	194	194	100%	8分56秒
excelbookuser	失败	1,858	1,858	100%	8分57秒
excepbook	失败	92	92	100%	8分57秒
ExistingTables	失败	8,073	8,073	100%	8分58秒
fill_user_book	失败	205,200	205,200	100%	8分59秒
Filling_AreaOrganizationUnits	失败	98,986	98,986	100%	9分0秒
Filling_CollaborativeReports	失败	1,111	1,111	100%	9分1秒
Filling_FileInfos	失败	1,570	1,570	100%	9分5秒
Filling_FillingConfigs	失败	33	33	100%	9分9秒
Filling_PlanTaskAreaOrganizationUnits	失败	14,241	14,241	100%	9分10秒
Filling_PlanTaskReportTableTemplates	失败	3,832	3,832	100%	9分12秒
Filling_PlanTasks	失败	3,627	3,627	100%	9分3秒
Filling_PlanTaskStaffAreaOrganizationUnits	失败	217	217	100%	9分13秒
Filling_PlanTaskStaffs	失败	1,913	1,913	100%	9分14秒
Filling_ReportMessageInfos	失败	0	0	0%	9分15秒
Filling_ReportTableRows	失败	2,042	2,042	100%	9分16秒
Filling_ReportTables	失败	1,997	1,997	100%	9分8秒
Filling_ReportTableTemplates	失败	4,819	4,819	100%	9分11秒
Filling_ReportTaskAreaOrganizationUnitAudits	失败	957	957	100%	9分17秒
Filling_ReportTaskAreaOrganizationUnitFillers	失败	1,067	1,067	100%	9分18秒
Filling_ReportTaskAreaOrganizationUnits	失败	1,911	1,911	100%	9分7秒
Filling_ReportTasks	失败	4,883	4,883	100%	9分4秒
Filling_Staff	失败	483,643	483,643	100%	9分2秒
Filling_StaffPlanTaskHides	失败	865	865	100%	9分19秒
Filling_TableLedgerRuleConfigs	失败	0	0	0%	9分20秒
Filling_TableTemplateColumns	失败	175,494	175,494	100%	9分21秒
Filling_TableTemplateDataRows	失败	370	370	100%	9分22秒
Filling_YkzOrgUnits	失败	97,762	97,762	100%	9分23秒
hg_t_audit_log	失败	0	0	0%	9分24秒
IdentityServerApiResourceClaims	失败	35	35	100%	9分26秒
IdentityServerApiResourceProperties	失败	0	0	0%	9分27秒
IdentityServerApiResources	失败	3	3	100%	9分25秒
IdentityServerApiResourceScopes	失败	2	2	100%	9分28秒
IdentityServerApiResourceSecrets	失败	0	0	0%	9分29秒
IdentityServerApiScopeClaims	失败	0	0	0%	9分31秒
IdentityServerApiScopeProperties	失败	0	0	0%	9分32秒
IdentityServerApiScopes	失败	3	3	100%	9分30秒
IdentityServerClientClaims	失败	0	0	0%	9分34秒
IdentityServerClientCorsOrigins	失败	1	1	100%	9分35秒
IdentityServerClientGrantTypes	失败	19	19	100%	9分36秒
IdentityServerClientIdPRestrictions	失败	0	0	0%	9分37秒
IdentityServerClientPostLogoutRedirectUris	失败	6	6	100%	9分38秒
IdentityServerClientProperties	失败	0	0	0%	9分39秒
IdentityServerClientRedirectUris	失败	12	12	100%	9分40秒
IdentityServerClients	失败	11	11	100%	9分33秒
IdentityServerClientScopes	失败	58	58	100%	9分41秒
IdentityServerClientSecrets	失败	13	13	100%	9分42秒
IdentityServerDeviceFlowCodes	失败	0	0	0%	9分43秒
IdentityServerIdentityResourceClaims	失败	34	34	100%	9分45秒
IdentityServerIdentityResourceProperties	失败	0	0	0%	9分46秒
IdentityServerIdentityResources	失败	8	8	100%	9分44秒
IdentityServerPersistedGrants	失败	1,023	1,023	100%	9分47秒
Ledger_AjcBaseInfoStatistics	失败	0	0	0%	9分48秒
Ledger_ApiAuxiliaryFillingRules	失败	179	179	100%	10分4秒
Ledger_ApiAuxiliaryFillings	失败	53	53	100%	10分5秒
Ledger_ApiAuxiliaryFillSources	失败	2	2	100%	10分3秒
Ledger_ApiDataSetMappingFields	失败	278	278	100%	10分7秒
Ledger_ApiDataSets	失败	6	6	100%	10分6秒
Ledger_ApiRequestParameters	失败	6	6	100%	10分8秒
Ledger_AssociatedConfigs	失败	215	215	100%	10分10秒
Ledger_AssociatedSynConfigs	失败	296	296	100%	10分14秒
Ledger_AssociatedSyns	失败	43	43	100%	10分12秒
Ledger_BaseInfoStatistics	失败	1,072	1,072	100%	10分15秒
Ledger_BigViewTypicalScenarios	失败	44	44	100%	10分16秒
Ledger_BigViewTypicalScenarioUploadRecords	失败	0	0	0%	13分29秒
Ledger_CommonGoalsTasks	失败	0	0	0%	13分30秒
Ledger_DataItemsManagements	失败	0	0	0%	13分30秒
Ledger_DataItemsUpdateRecords	失败	0	0	0%	13分32秒
Ledger_DataSources	失败	26	26	100%	9分50秒
Ledger_DbTableFields	失败	23,219	23,219	100%	9分52秒
Ledger_DeductPoints	失败	11	11	100%	13分33秒
Ledger_DeductPointsPublishes	失败	61	61	100%	13分35秒
Ledger_DeductPointsRecords	失败	0	0	0%	13分38秒
Ledger_Disabilities	失败	0	0	0%	13分39秒
Ledger_HierarchicalLedgerDataPushOriginPlans	失败	0	0	0%	13分40秒
Ledger_HierarchicalLedgers	失败	3	3	100%	13分41秒
Ledger_HierarchicalLedgerTaskItems	失败	2	2	100%	13分42秒
Ledger_HistoryEvaluates	失败	0	0	0%	13分36秒
Ledger_IndicatorCockpitConfigs	失败	35	35	100%	13分44秒
Ledger_IndicatorCockpits	失败	45,623	45,623	100%	13分43秒
Ledger_LedgerAuditBatches	失败	73,785	73,785	100%	13分45秒
Ledger_LedgerAuditBatchFlowNodes	失败	397	397	100%	13分47秒
Ledger_LedgerAuditBatchFlows	失败	169	169	100%	13分46秒
Ledger_LedgerAuditBatchStatistics	失败	67,421	67,421	100%	13分48秒
Ledger_LedgerAuxiliaryFillingConfigs	失败	172	172	100%	13分51秒
Ledger_LedgerAuxiliaryFillingPreviews	失败	159	159	100%	13分54秒
Ledger_LedgerAuxiliaryFillings	失败	51	51	100%	13分49秒
Ledger_LedgerChangeRecordDetails	失败	135	135	100%	13分56秒
Ledger_LedgerChangeRecords	失败	73	73	100%	13分55秒
Ledger_LedgerDataAnalyses	失败	539	539	100%	10分9秒
Ledger_LedgerDataComparisonRecords	失败	38	38	100%	13分57秒
Ledger_LedgerDataSetAuxiliaryFillingConfigs	失败	2	2	100%	13分59秒
Ledger_LedgerDataSetAuxiliaryFillings	失败	2	2	100%	13分52秒
Ledger_LedgerDataSyncConfigs	失败	1	1	100%	14分0秒
Ledger_LedgerDataSyncFields	失败	1	1	100%	14分2秒
Ledger_LedgerDataSyncTasks	失败	1	1	100%	14分1秒
Ledger_LedgerDataUpdateLogs	失败	0	0	0%	0秒
Ledger_LedgerDepartmentDataDetailStatistics	失败	86,453	86,453	100%	14分3秒
Ledger_LedgerDepartmentDataStatistics	失败	1,388	1,388	100%	14分4秒
Ledger_LedgerDepartments	失败	168,892	55,000	32.57%	10分14秒
Ledger_LedgerHierarchicalAuthorizations	失败	1	1	100%	14分5秒
Ledger_LedgerPermissionsAuthorizationModes	失败	1,263,126	1,263,126	100%	40分51秒
Ledger_LedgerRunwayRelations	失败	0	0	0%	0秒
Ledger_LedgerRunways	失败	0	0	0%	0秒
Ledger_Ledgers	失败	8,404	8,404	100%	10分2秒
Ledger_LedgerStatistics	失败	0	0	0%	0秒
Ledger_LedgerTemplates	失败	0	0	0%	0秒
Ledger_LedgerTypes	失败	1,259	1,259	100%	10分1秒
Ledger_LedgerUsers	失败	0	0	0%	0秒
Ledger_LedgerWayAndRelations	失败	0	0	0%	0秒
Ledger_LlmMessages	失败	0	0	0%	0秒
Ledger_MyLedgerExportRecords	失败	0	0	0%	0秒
Ledger_OneVoteRejectPublishes	失败	0	0	0%	0秒
Ledger_OneVoteRejectRecords	失败	0	0	0%	0秒
Ledger_OneVoteRejects	失败	0	0	0%	0秒
Ledger_OnLineAnalysisConfigs	失败	0	0	0%	0秒
Ledger_OnLineAnalysisEchartConfigs	失败	0	0	0%	0秒
Ledger_PlanProgressConfigs	失败	0	0	0%	0秒
Ledger_ResettleHelpEducates	失败	0	0	0%	0秒
Ledger_RiskDetermines	失败	0	0	0%	0秒
Ledger_ServeSentenceAndCriminalInfoStatistics	失败	0	0	0%	0秒
Ledger_SocialAssistances	失败	0	0	0%	0秒
Ledger_SourceDbTableInfos	失败	926	926	100%	9分51秒
Ledger_SourceLedgerFillStatistics	失败	0	0	0%	0秒
Ledger_SourceLedgerTypeRelations	失败	0	0	0%	0秒
Ledger_SourceLedgerTypes	失败	0	0	0%	0秒
Ledger_StarEvaluatePublishes	失败	779	779	100%	13分34秒
Ledger_StarEvaluateRecords	失败	0	0	0%	13分37秒
Ledger_StarEvaluates	失败	41	41	100%	13分32秒
Ledger_StreetBigDataEchartAnalyses	失败	0	0	0%	0秒
Ledger_StreetBigDataEchartAnalysisConfigs	失败	0	0	0%	0秒
Ledger_TableDataExportRecords	失败	0	0	0%	0秒
Ledger_TableDataImportRecords	失败	0	0	0%	0秒
Ledger_TableDataSetJoinConfigs	失败	0	0	0%	0秒
Ledger_TableDataSetJoinFieldConfigs	失败	0	0	0%	0秒
Ledger_TableDataSetMappingFields	失败	3,575	3,575	100%	9分54秒
Ledger_TableDataSets	失败	145	145	100%	9分53秒
Ledger_TableDataSetSourceDbTableInfos	失败	0	0	0%	0秒
Ledger_TableFieldCalculateRules	失败	0	0	0%	0秒
Ledger_TableFieldClientSettings	失败	0	0	0%	0秒
Ledger_TableFieldGroups	失败	7,017	7,017	100%	9分49秒
Ledger_TableFieldMultiples	失败	0	0	0%	0秒
Ledger_TableFields	失败	290,271	290,271	100%	9分55秒
Ledger_TableFiledValidateRules	失败	0	0	0%	0秒
Ledger_TableInfos	失败	9,037	9,037	100%	9分56秒
Ledger_TableQueryConfigs	失败	0	0	0%	0秒
Ledger_TaskChargePeople	失败	0	0	0%	0秒
Ledger_TelecomTaskItems	失败	0	0	0%	0秒
NewFeature_AuthorityAudits	失败	0	0	0%	0秒
NewFeature_AutomatedVerifications	失败	0	0	0%	0秒
NewFeature_DataProcesses	失败	0	0	0%	0秒
NewFeature_FunctionLogs	失败	0	0	0%	0秒
NewFeature_ProgressManagementItems	失败	0	0	0%	0秒
NewFeature_ProgressManagements	失败	0	0	0%	0秒
Platform_ChartDicDatas	失败	0	0	0%	0秒
Platform_ChartGroups	失败	0	0	0%	0秒
Platform_ChartListDatas	失败	0	0	0%	0秒
Platform_Charts	失败	0	0	0%	0秒
Platform_ChartTimeFlowDatas	失败	0	0	0%	0秒
Platform_DepartmentFavoriteGroups	失败	0	0	0%	0秒
Platform_DepartmentFavorites	失败	0	0	0%	0秒
Platform_DepartmentGroupDepartments	失败	0	0	0%	0秒
Platform_DepartmentGroups	失败	0	0	0%	0秒
Platform_Departments	失败	258,980	258,980	100%	10分0秒
Platform_Departments_error	失败	219,108	219,108	100%	9分57秒
Platform_QuestionFeedbacks	失败	0	0	0%	0秒
Platform_RegionFavoriteGroups	失败	0	0	0%	0秒
Platform_RegionFavorites	失败	0	0	0%	0秒
Platform_Regions	失败	0	0	0%	0秒
Platform_ReportLedgerNotices	失败	0	0	0%	0秒
Platform_Routes	失败	0	0	0%	0秒
Platform_SyncRecords	失败	0	0	0%	0秒
Platform_UserDepartmentFavoriteGroups	失败	0	0	0%	0秒
Platform_UserDepartmentRoles	失败	0	0	0%	0秒
Platform_UserDepartments	失败	0	0	0%	0秒
Platform_Users	失败	483,649	483,649	100%	13分27秒
Platform_WebWidgets	失败	0	0	0%	0秒
Platform_WFDataProcesses	失败	0	0	0%	0秒
Platform_WFPlanTasks	失败	0	0	0%	0秒
Platform_WFTaskItems	失败	0	0	0%	0秒
Platform_WorkToDoRecords	失败	0	0	0%	0秒
Platform_YbtUsers	失败	0	0	0%	0秒
Platform_YkzOrganizations	失败	0	0	0%	0秒
Platform_YkzOrgUnits	失败	0	0	0%	0秒
RPA_ApplicationDepartments	失败	0	0	0%	0秒
RPA_Applications	失败	0	0	0%	0秒
RPA_ApplicationUsers	失败	0	0	0%	0秒
RPA_AppTaskItems	失败	0	0	0%	0秒
RPA_AppTasks	失败	0	0	0%	0秒
RPA_DataFieldMappings	失败	0	0	0%	0秒
RPA_DataSources	失败	0	0	0%	0秒
RPA_ProcessFields	失败	0	0	0%	0秒
RPA_ProcessServices	失败	0	0	0%	0秒
SrcOrgs	失败	0	0	0%	0秒
TableInfosToCheck	失败	0	0	0%	0秒
Temp_Batch	失败	0	0	0%	0秒
Temp_Ledger_Department_Fill_Fix	失败	0	0	0%	0秒
Temp_Ledger_Department_Operation_Count	失败	0	0	0%	0秒
Temp_Ledger_Department_Operation_Count_NewV1	失败	0	0	0%	0秒
TempOrgs	失败	0	0	0%	0秒
TempUsers	失败	0	0	0%	0秒
Test_DEC	失败	0	0	0%	0秒
TK_BackgroundJobLogs	失败	0	0	0%	0秒
TK_BackgroundJobs	失败	0	0	0%	0秒
UserLoginRecords	失败	0	0	0%	0秒
WF_WorkflowProcesses	失败	0	0	0%	0秒
WF_WorkflowSchemeAuths	失败	0	0	0%	0秒
WF_WorkflowSchemeInfoPermissionGrants	失败	0	0	0%	0秒
WF_WorkflowSchemeInfos	失败	0	0	0%	0秒
WF_WorkflowSchemes	失败	0	0	0%	0秒
WF_WorkflowStamps	失败	0	0	0%	0秒
WF_WorkflowTaskLogs	失败	0	0	0%	0秒
WF_WorkflowTasks	失败	0	0	0%	0秒
WF_WorkflowUnits	失败	0	0	0%	0秒
