{"MigrationId": "d8b5a4f3-256c-4194-896f-37dfaee42a37", "SourceType": "MySQL", "TargetType": "MySQL", "StartTime": "2025-06-19T16:05:43.521265+08:00", "LastUpdateTime": "2025-06-19T16:48:12.836808+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 284, "TotalRows": 12110738, "MigratedRows": 12110738, "TableCheckpoints": {"AbpAuditLogActions": {"TableName": "AbpAuditLogActions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.855702+08:00", "LastUpdateTime": "2025-06-19T16:48:03.060023+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpAuditLogs": {"TableName": "AbpAuditLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.870308+08:00", "LastUpdateTime": "2025-06-19T16:48:04.079126+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityChanges": {"TableName": "AbpEntityChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.870869+08:00", "LastUpdateTime": "2025-06-19T16:48:05.164799+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityPropertyChanges": {"TableName": "AbpEntityPropertyChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.871222+08:00", "LastUpdateTime": "2025-06-19T16:48:06.303476+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSecurityLogs": {"TableName": "AbpSecurityLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.871488+08:00", "LastUpdateTime": "2025-06-19T16:48:07.311184+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserClaims": {"TableName": "AbpUserClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.872003+08:00", "LastUpdateTime": "2025-06-19T16:48:08.47669+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotifications": {"TableName": "AppNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.872328+08:00", "LastUpdateTime": "2025-06-19T16:48:09.484661+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserNotifications": {"TableName": "AppUserNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.872613+08:00", "LastUpdateTime": "2025-06-19T16:48:10.494551+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserSubscribes": {"TableName": "AppUserSubscribes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.873511+08:00", "LastUpdateTime": "2025-06-19T16:48:11.544567+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataUpdateLogs": {"TableName": "Ledger_LedgerDataUpdateLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.875091+08:00", "LastUpdateTime": "2025-06-19T16:48:12.844607+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "__EFMigrationsHistory": {"TableName": "__EFMigrationsHistory", "TotalRows": 390, "MigratedRows": 390, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.901221+08:00", "LastUpdateTime": "2025-06-19T16:05:58.052314+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpClaimTypes": {"TableName": "AbpClaimTypes", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.902156+08:00", "LastUpdateTime": "2025-06-19T16:05:59.05784+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEditions": {"TableName": "AbpEditions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.902414+08:00", "LastUpdateTime": "2025-06-19T16:06:00.061272+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpFeatureValues": {"TableName": "AbpFeature<PERSON><PERSON>ues", "TotalRows": 24, "MigratedRows": 24, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:55.902616+08:00", "LastUpdateTime": "2025-06-19T16:06:01.061557+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLinkUsers": {"TableName": "AbpLinkUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:56.668021+08:00", "LastUpdateTime": "2025-06-19T16:06:02.068014+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationLanguages": {"TableName": "AbpLocalizationLanguages", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:56.714101+08:00", "LastUpdateTime": "2025-06-19T16:06:03.075406+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationResources": {"TableName": "AbpLocalizationResources", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:56.715677+08:00", "LastUpdateTime": "2025-06-19T16:06:04.080473+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationTexts": {"TableName": "AbpLocalizationTexts", "TotalRows": 20, "MigratedRows": 20, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:56.918521+08:00", "LastUpdateTime": "2025-06-19T16:06:05.083295+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpRoles": {"TableName": "AbpRoles", "TotalRows": 18, "MigratedRows": 18, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.112259+08:00", "LastUpdateTime": "2025-06-19T16:06:06.087143+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpOrganizationUnits": {"TableName": "AbpOrganizationUnits", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.112535+08:00", "LastUpdateTime": "2025-06-19T16:06:07.310522+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpOrganizationUnitRoles": {"TableName": "AbpOrganizationUnitRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.151703+08:00", "LastUpdateTime": "2025-06-19T16:06:08.547797+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpPermissionGrants": {"TableName": "AbpPermissionGrants", "TotalRows": 5696, "MigratedRows": 5696, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.392849+08:00", "LastUpdateTime": "2025-06-19T16:06:09.553662+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpRoleClaims": {"TableName": "AbpRoleClaims", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.61388+08:00", "LastUpdateTime": "2025-06-19T16:06:10.689238+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSettings": {"TableName": "AbpSettings", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.653451+08:00", "LastUpdateTime": "2025-06-19T16:06:11.695326+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpTenants": {"TableName": "AbpTenants", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.653636+08:00", "LastUpdateTime": "2025-06-19T16:06:12.847832+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpTenantConnectionStrings": {"TableName": "AbpTenantConnectionStrings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:57.822477+08:00", "LastUpdateTime": "2025-06-19T16:06:13.993423+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUsers": {"TableName": "AbpUsers", "TotalRows": 483309, "MigratedRows": 483309, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.020763+08:00", "LastUpdateTime": "2025-06-19T16:13:25.078954+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserLogins": {"TableName": "AbpUserLogins", "TotalRows": 269, "MigratedRows": 269, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.020977+08:00", "LastUpdateTime": "2025-06-19T16:13:26.28979+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserOrganizationUnits": {"TableName": "AbpUserOrganizationUnits", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.092361+08:00", "LastUpdateTime": "2025-06-19T16:13:27.508887+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserRoles": {"TableName": "AbpUserRoles", "TotalRows": 523721, "MigratedRows": 523721, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.266195+08:00", "LastUpdateTime": "2025-06-19T16:13:28.713415+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserRoles_923bf": {"TableName": "AbpUserRoles_923bf", "TotalRows": 455580, "MigratedRows": 455580, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.471706+08:00", "LastUpdateTime": "2025-06-19T16:13:29.719876+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserTokens": {"TableName": "AbpUserTokens", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.518039+08:00", "LastUpdateTime": "2025-06-19T16:13:30.872014+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksEvents": {"TableName": "AbpWebhooksEvents", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.518392+08:00", "LastUpdateTime": "2025-06-19T16:13:31.880739+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksSendAttempts": {"TableName": "AbpWebhooksSendAttempts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.686956+08:00", "LastUpdateTime": "2025-06-19T16:13:33.016464+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksSubscriptions": {"TableName": "AbpWebhooksSubscriptions", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.884363+08:00", "LastUpdateTime": "2025-06-19T16:13:34.022859+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_AssetLiabilities": {"TableName": "Airport_AssetLiabilities", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:58.940514+08:00", "LastUpdateTime": "2025-06-19T16:13:35.026181+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_BudgetAccounts": {"TableName": "Airport_BudgetAccounts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:59.00237+08:00", "LastUpdateTime": "2025-06-19T16:13:36.032587+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_BudgetOrganizations": {"TableName": "Airport_BudgetOrganizations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:59.186803+08:00", "LastUpdateTime": "2025-06-19T16:13:37.038555+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceAuditBatches": {"TableName": "Airport_FinanceAuditBatches", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:59.436501+08:00", "LastUpdateTime": "2025-06-19T16:13:38.041279+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceAuditedDatas": {"TableName": "Airport_FinanceAuditedDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:59.436682+08:00", "LastUpdateTime": "2025-06-19T16:13:39.046407+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceProjects": {"TableName": "Airport_FinanceProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:59.484241+08:00", "LastUpdateTime": "2025-06-19T16:13:40.049575+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_NoFinanceProjects": {"TableName": "Airport_NoFinanceProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:05:59.814622+08:00", "LastUpdateTime": "2025-06-19T16:13:41.051379+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_NoFinancialBudgetProjects": {"TableName": "Airport_NoFinancialBudgetProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.082981+08:00", "LastUpdateTime": "2025-06-19T16:13:42.054807+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_OverheadCostCenters": {"TableName": "Airport_OverheadCostCenters", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.110882+08:00", "LastUpdateTime": "2025-06-19T16:13:43.058981+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppChatGroups": {"TableName": "AppChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.111034+08:00", "LastUpdateTime": "2025-06-19T16:13:44.063641+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppGroupChatBlacks": {"TableName": "AppGroupChatBlacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.42456+08:00", "LastUpdateTime": "2025-06-19T16:13:45.069544+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppGroupMessages": {"TableName": "AppGroupMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.668439+08:00", "LastUpdateTime": "2025-06-19T16:13:46.075596+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotificationDefinitionGroups": {"TableName": "AppNotificationDefinitionGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.678146+08:00", "LastUpdateTime": "2025-06-19T16:13:47.081897+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotificationDefinitions": {"TableName": "AppNotificationDefinitions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.766687+08:00", "LastUpdateTime": "2025-06-19T16:13:48.087313+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformDatas": {"TableName": "AppPlatformDatas", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:00.96404+08:00", "LastUpdateTime": "2025-06-19T16:13:49.09513+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformDataItems": {"TableName": "AppPlatformDataItems", "TotalRows": 27, "MigratedRows": 27, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.188294+08:00", "LastUpdateTime": "2025-06-19T16:13:50.240506+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformLayouts": {"TableName": "AppPlatformLayouts", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.308085+08:00", "LastUpdateTime": "2025-06-19T16:13:51.247627+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformMenus": {"TableName": "AppPlatformMenus", "TotalRows": 263, "MigratedRows": 263, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.333286+08:00", "LastUpdateTime": "2025-06-19T16:13:52.252516+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformPackages": {"TableName": "AppPlatformPackages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.590463+08:00", "LastUpdateTime": "2025-06-19T16:13:53.259294+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformPackageBlobs": {"TableName": "AppPlatformPackageBlobs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.897674+08:00", "LastUpdateTime": "2025-06-19T16:13:54.38037+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformRoleMenus": {"TableName": "AppPlatformRoleMenus", "TotalRows": 585, "MigratedRows": 585, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.897864+08:00", "LastUpdateTime": "2025-06-19T16:13:55.386935+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformUserFavoriteMenus": {"TableName": "AppPlatformUserFavoriteMenus", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:01.951146+08:00", "LastUpdateTime": "2025-06-19T16:13:56.393641+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformUserMenus": {"TableName": "AppPlatformUserMenus", "TotalRows": 5232, "MigratedRows": 5232, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.223987+08:00", "LastUpdateTime": "2025-06-19T16:13:57.396507+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformVersion": {"TableName": "AppPlatformVersion", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.441915+08:00", "LastUpdateTime": "2025-06-19T16:13:58.402605+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformVersionFile": {"TableName": "AppPlatformVersionFile", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.442216+08:00", "LastUpdateTime": "2025-06-19T16:13:59.557396+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatCards": {"TableName": "AppUserChatCards", "TotalRows": 49825, "MigratedRows": 49825, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.494984+08:00", "LastUpdateTime": "2025-06-19T16:14:00.563022+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatFriends": {"TableName": "AppUserChatFriends", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.699067+08:00", "LastUpdateTime": "2025-06-19T16:14:01.566875+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatGroups": {"TableName": "AppUserChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.885157+08:00", "LastUpdateTime": "2025-06-19T16:14:02.574898+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatSettings": {"TableName": "AppUserChatSettings", "TotalRows": 49825, "MigratedRows": 49825, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:02.958222+08:00", "LastUpdateTime": "2025-06-19T16:14:03.583116+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserGroupCards": {"TableName": "AppUserGroupCards", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.01271+08:00", "LastUpdateTime": "2025-06-19T16:14:04.584579+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserMessages": {"TableName": "AppUserMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.228463+08:00", "LastUpdateTime": "2025-06-19T16:14:05.591871+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_BaseInfoStatistics": {"TableName": "Bank_Ledger_BaseInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.402706+08:00", "LastUpdateTime": "2025-06-19T16:14:06.595918+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_ResettleHelpEducates": {"TableName": "Bank_Ledger_ResettleHelpEducates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.430044+08:00", "LastUpdateTime": "2025-06-19T16:14:07.601957+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_RiskDetermines": {"TableName": "Bank_Ledger_RiskDetermines", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.503293+08:00", "LastUpdateTime": "2025-06-19T16:14:08.608152+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_ServeSentenceAndCriminalInfoStatistics": {"TableName": "Bank_Ledger_ServeSentenceAndCriminalInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.762912+08:00", "LastUpdateTime": "2025-06-19T16:14:09.619062+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "BigScreenAuditDatas": {"TableName": "BigScreenAuditDatas", "TotalRows": 37, "MigratedRows": 37, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:03.982948+08:00", "LastUpdateTime": "2025-06-19T16:14:10.621006+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "BSPUser": {"TableName": "BSPUser", "TotalRows": 455368, "MigratedRows": 455368, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.071242+08:00", "LastUpdateTime": "2025-06-19T16:14:11.627441+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "dept_new_zhong": {"TableName": "dept_new_zhong", "TotalRows": 194, "MigratedRows": 194, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.072632+08:00", "LastUpdateTime": "2025-06-19T16:14:12.63086+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "excelbookuser": {"TableName": "excelbookuser", "TotalRows": 1858, "MigratedRows": 1858, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.303509+08:00", "LastUpdateTime": "2025-06-19T16:14:13.63743+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "excepbook": {"TableName": "excepbook", "TotalRows": 92, "MigratedRows": 92, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.500703+08:00", "LastUpdateTime": "2025-06-19T16:14:14.64391+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "ExistingTables": {"TableName": "ExistingTables", "TotalRows": 8073, "MigratedRows": 8073, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.555016+08:00", "LastUpdateTime": "2025-06-19T16:14:15.645823+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "fill_user_book": {"TableName": "fill_user_book", "TotalRows": 205200, "MigratedRows": 205200, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.559506+08:00", "LastUpdateTime": "2025-06-19T16:14:16.654096+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_AreaOrganizationUnits": {"TableName": "Filling_AreaOrganizationUnits", "TotalRows": 98986, "MigratedRows": 98986, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.736052+08:00", "LastUpdateTime": "2025-06-19T16:14:17.820528+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_CollaborativeReports": {"TableName": "Filling_CollaborativeReports", "TotalRows": 1111, "MigratedRows": 1111, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.927386+08:00", "LastUpdateTime": "2025-06-19T16:14:18.822231+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_Staff": {"TableName": "Filling_Staff", "TotalRows": 483643, "MigratedRows": 483643, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.947884+08:00", "LastUpdateTime": "2025-06-19T16:14:19.829223+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTasks": {"TableName": "Filling_PlanTasks", "TotalRows": 3627, "MigratedRows": 3627, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:04.954233+08:00", "LastUpdateTime": "2025-06-19T16:14:21.194617+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTasks": {"TableName": "Filling_ReportTasks", "TotalRows": 4883, "MigratedRows": 4883, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:05.21018+08:00", "LastUpdateTime": "2025-06-19T16:14:22.410107+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_FileInfos": {"TableName": "Filling_FileInfos", "TotalRows": 1570, "MigratedRows": 1570, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:05.481603+08:00", "LastUpdateTime": "2025-06-19T16:14:23.617837+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnits": {"TableName": "Filling_ReportTaskAreaOrganizationUnits", "TotalRows": 1910, "MigratedRows": 1910, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:05.486332+08:00", "LastUpdateTime": "2025-06-19T16:14:25.101294+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTables": {"TableName": "Filling_ReportTables", "TotalRows": 1996, "MigratedRows": 1996, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:05.527314+08:00", "LastUpdateTime": "2025-06-19T16:14:26.217177+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_FillingConfigs": {"TableName": "Filling_FillingConfigs", "TotalRows": 33, "MigratedRows": 33, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:05.76569+08:00", "LastUpdateTime": "2025-06-19T16:14:27.415765+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskAreaOrganizationUnits": {"TableName": "Filling_PlanTaskAreaOrganizationUnits", "TotalRows": 14241, "MigratedRows": 14241, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.043101+08:00", "LastUpdateTime": "2025-06-19T16:14:28.553747+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTableTemplates": {"TableName": "Filling_ReportTableTemplates", "TotalRows": 4819, "MigratedRows": 4819, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.043332+08:00", "LastUpdateTime": "2025-06-19T16:14:29.557562+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskReportTableTemplates": {"TableName": "Filling_PlanTaskReportTableTemplates", "TotalRows": 3832, "MigratedRows": 3832, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.082266+08:00", "LastUpdateTime": "2025-06-19T16:14:30.771779+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskStaffAreaOrganizationUnits": {"TableName": "Filling_PlanTaskStaffAreaOrganizationUnits", "TotalRows": 217, "MigratedRows": 217, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.266067+08:00", "LastUpdateTime": "2025-06-19T16:14:31.979397+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskStaffs": {"TableName": "Filling_PlanTaskStaffs", "TotalRows": 1913, "MigratedRows": 1913, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.493673+08:00", "LastUpdateTime": "2025-06-19T16:14:33.204351+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportMessageInfos": {"TableName": "Filling_ReportMessageInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.575969+08:00", "LastUpdateTime": "2025-06-19T16:14:34.215351+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTableRows": {"TableName": "Filling_ReportTableRows", "TotalRows": 2042, "MigratedRows": 2042, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.586011+08:00", "LastUpdateTime": "2025-06-19T16:14:35.344068+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnitAudits": {"TableName": "Filling_ReportTaskAreaOrganizationUnitAudits", "TotalRows": 957, "MigratedRows": 957, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.756289+08:00", "LastUpdateTime": "2025-06-19T16:14:36.487449+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnitFillers": {"TableName": "Filling_ReportTaskAreaOrganizationUnitFillers", "TotalRows": 1066, "MigratedRows": 1066, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:06.929003+08:00", "LastUpdateTime": "2025-06-19T16:14:37.766551+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_StaffPlanTaskHides": {"TableName": "Filling_StaffPlanTaskHides", "TotalRows": 865, "MigratedRows": 865, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.089243+08:00", "LastUpdateTime": "2025-06-19T16:14:38.967372+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableLedgerRuleConfigs": {"TableName": "Filling_TableLedgerRuleConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.296387+08:00", "LastUpdateTime": "2025-06-19T16:14:40.194962+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableTemplateColumns": {"TableName": "Filling_TableTemplateColumns", "TotalRows": 175494, "MigratedRows": 175494, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.458582+08:00", "LastUpdateTime": "2025-06-19T16:14:41.391275+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableTemplateDataRows": {"TableName": "Filling_TableTemplateDataRows", "TotalRows": 370, "MigratedRows": 370, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.630175+08:00", "LastUpdateTime": "2025-06-19T16:14:42.532741+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_YkzOrgUnits": {"TableName": "Filling_YkzOrgUnits", "TotalRows": 97762, "MigratedRows": 97762, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.635548+08:00", "LastUpdateTime": "2025-06-19T16:14:43.54605+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "hg_t_audit_log": {"TableName": "hg_t_audit_log", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.890223+08:00", "LastUpdateTime": "2025-06-19T16:14:44.55362+08:00", "Status": "Completed", "ErrorMessage": "获取表行数失败: 获取表hg_t_audit_log的总行数在3次尝试后操作仍然失败。最后一次错误: XX000: permission denied ", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResources": {"TableName": "IdentityServerApiResources", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:07.927077+08:00", "LastUpdateTime": "2025-06-19T16:14:45.561595+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceClaims": {"TableName": "IdentityServerApiResourceClaims", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.159005+08:00", "LastUpdateTime": "2025-06-19T16:14:46.77811+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceProperties": {"TableName": "IdentityServerApiResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.208019+08:00", "LastUpdateTime": "2025-06-19T16:14:48.01833+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceScopes": {"TableName": "IdentityServerApiResourceScopes", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.432263+08:00", "LastUpdateTime": "2025-06-19T16:14:49.170563+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceSecrets": {"TableName": "IdentityServerApiResourceSecrets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.477243+08:00", "LastUpdateTime": "2025-06-19T16:14:50.337786+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopes": {"TableName": "IdentityServerApiScopes", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.693855+08:00", "LastUpdateTime": "2025-06-19T16:14:51.365072+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopeClaims": {"TableName": "IdentityServerApiScopeClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.694971+08:00", "LastUpdateTime": "2025-06-19T16:14:52.631964+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopeProperties": {"TableName": "IdentityServerApiScopeProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.893828+08:00", "LastUpdateTime": "2025-06-19T16:14:53.794648+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClients": {"TableName": "IdentityServerClients", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:08.970238+08:00", "LastUpdateTime": "2025-06-19T16:14:54.805069+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientClaims": {"TableName": "IdentityServerClientClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.153135+08:00", "LastUpdateTime": "2025-06-19T16:14:56.034251+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientCorsOrigins": {"TableName": "IdentityServerClientCorsOrigins", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.220986+08:00", "LastUpdateTime": "2025-06-19T16:14:57.203976+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientGrantTypes": {"TableName": "IdentityServerClientGrantTypes", "TotalRows": 19, "MigratedRows": 19, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.358109+08:00", "LastUpdateTime": "2025-06-19T16:14:58.383056+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientIdPRestrictions": {"TableName": "IdentityServerClientIdPRestrictions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.47388+08:00", "LastUpdateTime": "2025-06-19T16:14:59.609741+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientPostLogoutRedirectUris": {"TableName": "IdentityServerClientPostLogoutRedirectUris", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.672948+08:00", "LastUpdateTime": "2025-06-19T16:15:00.78716+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientProperties": {"TableName": "IdentityServerClientProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.705758+08:00", "LastUpdateTime": "2025-06-19T16:15:01.934444+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientRedirectUris": {"TableName": "IdentityServerClientRedirectUris", "TotalRows": 12, "MigratedRows": 12, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.875116+08:00", "LastUpdateTime": "2025-06-19T16:15:03.137606+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientScopes": {"TableName": "IdentityServerClientScopes", "TotalRows": 58, "MigratedRows": 58, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:09.932871+08:00", "LastUpdateTime": "2025-06-19T16:15:04.304797+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientSecrets": {"TableName": "IdentityServerClientSecrets", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.091451+08:00", "LastUpdateTime": "2025-06-19T16:15:05.513194+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerDeviceFlowCodes": {"TableName": "IdentityServerDeviceFlowCodes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.131833+08:00", "LastUpdateTime": "2025-06-19T16:15:06.526559+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResources": {"TableName": "IdentityServerIdentityResources", "TotalRows": 8, "MigratedRows": 8, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.299217+08:00", "LastUpdateTime": "2025-06-19T16:15:07.541494+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResourceClaims": {"TableName": "IdentityServerIdentityResourceClaims", "TotalRows": 34, "MigratedRows": 34, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.338293+08:00", "LastUpdateTime": "2025-06-19T16:15:08.759166+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResourceProperties": {"TableName": "IdentityServerIdentityResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.514752+08:00", "LastUpdateTime": "2025-06-19T16:15:09.91315+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerPersistedGrants": {"TableName": "IdentityServerPersistedGrants", "TotalRows": 899, "MigratedRows": 899, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.570388+08:00", "LastUpdateTime": "2025-06-19T16:15:10.920744+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AjcBaseInfoStatistics": {"TableName": "Ledger_AjcBaseInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.825624+08:00", "LastUpdateTime": "2025-06-19T16:15:11.928988+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldGroups": {"TableName": "Ledger_TableFieldGroups", "TotalRows": 7015, "MigratedRows": 7015, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:10.844263+08:00", "LastUpdateTime": "2025-06-19T16:15:13.160428+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataSources": {"TableName": "Ledger_DataSources", "TotalRows": 26, "MigratedRows": 26, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.031724+08:00", "LastUpdateTime": "2025-06-19T16:15:14.166975+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceDbTableInfos": {"TableName": "Ledger_SourceDbTableInfos", "TotalRows": 926, "MigratedRows": 926, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.144701+08:00", "LastUpdateTime": "2025-06-19T16:15:15.289672+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DbTableFields": {"TableName": "Ledger_DbTableFields", "TotalRows": 23219, "MigratedRows": 23219, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.347303+08:00", "LastUpdateTime": "2025-06-19T16:15:16.438052+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSets": {"TableName": "Ledger_TableDataSets", "TotalRows": 145, "MigratedRows": 145, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.379003+08:00", "LastUpdateTime": "2025-06-19T16:15:17.570651+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetMappingFields": {"TableName": "Ledger_TableDataSetMappingFields", "TotalRows": 3575, "MigratedRows": 3575, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.587719+08:00", "LastUpdateTime": "2025-06-19T16:15:18.814238+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFields": {"TableName": "Ledger_TableFields", "TotalRows": 290211, "MigratedRows": 290211, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.636772+08:00", "LastUpdateTime": "2025-06-19T16:15:20.330064+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableInfos": {"TableName": "Ledger_TableInfos", "TotalRows": 9035, "MigratedRows": 9035, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.838085+08:00", "LastUpdateTime": "2025-06-19T16:15:21.525947+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Departments_error": {"TableName": "Platform_Departments_error", "TotalRows": 219108, "MigratedRows": 219108, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:11.883324+08:00", "LastUpdateTime": "2025-06-19T16:15:22.875485+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Departments": {"TableName": "Platform_Departments", "TotalRows": 258980, "MigratedRows": 258980, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.084847+08:00", "LastUpdateTime": "2025-06-19T16:15:28.817517+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerTypes": {"TableName": "Ledger_LedgerTypes", "TotalRows": 1259, "MigratedRows": 1259, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.170563+08:00", "LastUpdateTime": "2025-06-19T16:15:30.043303+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_Ledgers": {"TableName": "Ledger_Ledgers", "TotalRows": 8402, "MigratedRows": 8402, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.430696+08:00", "LastUpdateTime": "2025-06-19T16:15:31.438974+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillSources": {"TableName": "Ledger_ApiAuxiliaryFillSources", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.443047+08:00", "LastUpdateTime": "2025-06-19T16:15:32.445394+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillingRules": {"TableName": "Ledger_ApiAuxiliaryFillingRules", "TotalRows": 173, "MigratedRows": 173, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.664288+08:00", "LastUpdateTime": "2025-06-19T16:15:33.73733+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillings": {"TableName": "Ledger_ApiAuxiliaryFillings", "TotalRows": 53, "MigratedRows": 53, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.674661+08:00", "LastUpdateTime": "2025-06-19T16:15:34.741415+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiDataSets": {"TableName": "Ledger_ApiDataSets", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.939636+08:00", "LastUpdateTime": "2025-06-19T16:15:35.87285+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiDataSetMappingFields": {"TableName": "Ledger_ApiDataSetMappingFields", "TotalRows": 278, "MigratedRows": 278, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:12.953248+08:00", "LastUpdateTime": "2025-06-19T16:15:37.022926+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiRequestParameters": {"TableName": "Ledger_ApiRequestParameters", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:13.141387+08:00", "LastUpdateTime": "2025-06-19T16:15:38.157467+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataAnalyses": {"TableName": "Ledger_LedgerDataAnalyses", "TotalRows": 539, "MigratedRows": 539, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:13.17613+08:00", "LastUpdateTime": "2025-06-19T16:15:39.369977+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedConfigs": {"TableName": "Ledger_AssociatedConfigs", "TotalRows": 215, "MigratedRows": 215, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:13.422374+08:00", "LastUpdateTime": "2025-06-19T16:15:40.735965+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedSyns": {"TableName": "Ledger_AssociatedSyns", "TotalRows": 43, "MigratedRows": 43, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:13.431016+08:00", "LastUpdateTime": "2025-06-19T16:15:41.963372+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedSynConfigs": {"TableName": "Ledger_AssociatedSynConfigs", "TotalRows": 296, "MigratedRows": 296, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:13.697482+08:00", "LastUpdateTime": "2025-06-19T16:15:43.97092+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BaseInfoStatistics": {"TableName": "Ledger_BaseInfoStatistics", "TotalRows": 1072, "MigratedRows": 1072, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:13.749923+08:00", "LastUpdateTime": "2025-06-19T16:15:45.08757+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BigViewTypicalScenarios": {"TableName": "Ledger_BigViewTypicalScenarios", "TotalRows": 44, "MigratedRows": 44, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.009721+08:00", "LastUpdateTime": "2025-06-19T16:15:46.219996+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Users": {"TableName": "Platform_Users", "TotalRows": 483649, "MigratedRows": 483649, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.009991+08:00", "LastUpdateTime": "2025-06-19T16:18:41.625142+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BigViewTypicalScenarioUploadRecords": {"TableName": "Ledger_BigViewTypicalScenarioUploadRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.224437+08:00", "LastUpdateTime": "2025-06-19T16:18:43.099782+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_CommonGoalsTasks": {"TableName": "Ledger_CommonGoalsTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.244224+08:00", "LastUpdateTime": "2025-06-19T16:18:44.270367+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataItemsManagements": {"TableName": "Ledger_DataItemsManagements", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.474866+08:00", "LastUpdateTime": "2025-06-19T16:18:45.27674+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataItemsUpdateRecords": {"TableName": "Ledger_DataItemsUpdateRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.538943+08:00", "LastUpdateTime": "2025-06-19T16:18:46.437278+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluates": {"TableName": "Ledger_StarEvaluates", "TotalRows": 41, "MigratedRows": 41, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.711489+08:00", "LastUpdateTime": "2025-06-19T16:18:47.441673+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPoints": {"TableName": "Ledger_DeductPoints", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.772591+08:00", "LastUpdateTime": "2025-06-19T16:18:48.572075+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluatePublishes": {"TableName": "Ledger_StarEvaluatePublishes", "TotalRows": 779, "MigratedRows": 779, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.950305+08:00", "LastUpdateTime": "2025-06-19T16:18:49.744279+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPointsPublishes": {"TableName": "Ledger_DeductPointsPublishes", "TotalRows": 61, "MigratedRows": 61, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:14.982253+08:00", "LastUpdateTime": "2025-06-19T16:18:50.920086+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HistoryEvaluates": {"TableName": "Ledger_HistoryEvaluates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:15.190267+08:00", "LastUpdateTime": "2025-06-19T16:18:51.926153+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluateRecords": {"TableName": "Ledger_StarEvaluateRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:15.222461+08:00", "LastUpdateTime": "2025-06-19T16:18:53.084452+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPointsRecords": {"TableName": "Ledger_DeductPointsRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:15.462278+08:00", "LastUpdateTime": "2025-06-19T16:18:54.241216+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_Disabilities": {"TableName": "Ledger_Disabilities", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:15.499356+08:00", "LastUpdateTime": "2025-06-19T16:18:55.246847+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgerDataPushOriginPlans": {"TableName": "Ledger_HierarchicalLedgerDataPushOriginPlans", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:15.686049+08:00", "LastUpdateTime": "2025-06-19T16:18:56.253957+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgers": {"TableName": "Ledger_HierarchicalLedgers", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:15.749229+08:00", "LastUpdateTime": "2025-06-19T16:18:57.259542+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgerTaskItems": {"TableName": "Ledger_HierarchicalLedgerTaskItems", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:16.01013+08:00", "LastUpdateTime": "2025-06-19T16:18:58.269201+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_IndicatorCockpits": {"TableName": "Ledger_IndicatorCockpits", "TotalRows": 45613, "MigratedRows": 45613, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:16.050346+08:00", "LastUpdateTime": "2025-06-19T16:18:59.273952+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_IndicatorCockpitConfigs": {"TableName": "Ledger_IndicatorCockpitConfigs", "TotalRows": 21, "MigratedRows": 21, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:16.425847+08:00", "LastUpdateTime": "2025-06-19T16:19:00.486407+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatches": {"TableName": "Ledger_LedgerAuditBatches", "TotalRows": 73785, "MigratedRows": 73785, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:16.447581+08:00", "LastUpdateTime": "2025-06-19T16:19:01.941479+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchFlows": {"TableName": "Ledger_LedgerAuditBatchFlows", "TotalRows": 169, "MigratedRows": 169, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:16.646181+08:00", "LastUpdateTime": "2025-06-19T16:19:03.074317+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchFlowNodes": {"TableName": "Ledger_LedgerAuditBatchFlowNodes", "TotalRows": 397, "MigratedRows": 397, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:16.730891+08:00", "LastUpdateTime": "2025-06-19T16:19:04.349508+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchStatistics": {"TableName": "Ledger_LedgerAuditBatchStatistics", "TotalRows": 67419, "MigratedRows": 67419, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.020851+08:00", "LastUpdateTime": "2025-06-19T16:19:05.477929+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillings": {"TableName": "Ledger_LedgerAuxiliaryFillings", "TotalRows": 49, "MigratedRows": 49, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.062197+08:00", "LastUpdateTime": "2025-06-19T16:19:06.774697+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillingConfigs": {"TableName": "Ledger_LedgerAuxiliaryFillingConfigs", "TotalRows": 168, "MigratedRows": 168, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.336616+08:00", "LastUpdateTime": "2025-06-19T16:19:08.946278+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSetAuxiliaryFillings": {"TableName": "Ledger_LedgerDataSetAuxiliaryFillings", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.336967+08:00", "LastUpdateTime": "2025-06-19T16:19:10.231708+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillingPreviews": {"TableName": "Ledger_LedgerAuxiliaryFillingPreviews", "TotalRows": 159, "MigratedRows": 159, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.522857+08:00", "LastUpdateTime": "2025-06-19T16:19:12.463745+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerChangeRecords": {"TableName": "Ledger_LedgerChangeRecords", "TotalRows": 73, "MigratedRows": 73, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.589498+08:00", "LastUpdateTime": "2025-06-19T16:19:13.46744+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerChangeRecordDetails": {"TableName": "Ledger_LedgerChangeRecordDetails", "TotalRows": 135, "MigratedRows": 135, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.762446+08:00", "LastUpdateTime": "2025-06-19T16:19:14.601609+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataComparisonRecords": {"TableName": "Ledger_LedgerDataComparisonRecords", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:17.874775+08:00", "LastUpdateTime": "2025-06-19T16:19:16.015154+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSetAuxiliaryFillingConfigs": {"TableName": "Ledger_LedgerDataSetAuxiliaryFillingConfigs", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.069262+08:00", "LastUpdateTime": "2025-06-19T16:19:17.323806+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncConfigs": {"TableName": "Ledger_LedgerDataSyncConfigs", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.07276+08:00", "LastUpdateTime": "2025-06-19T16:19:18.696229+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncTasks": {"TableName": "Ledger_LedgerDataSyncTasks", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.30373+08:00", "LastUpdateTime": "2025-06-19T16:19:19.844119+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncFields": {"TableName": "Ledger_LedgerDataSyncFields", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.359646+08:00", "LastUpdateTime": "2025-06-19T16:19:21.141792+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartmentDataDetailStatistics": {"TableName": "Ledger_LedgerDepartmentDataDetailStatistics", "TotalRows": 86453, "MigratedRows": 86453, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.540976+08:00", "LastUpdateTime": "2025-06-19T16:19:22.346313+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartmentDataStatistics": {"TableName": "Ledger_LedgerDepartmentDataStatistics", "TotalRows": 1388, "MigratedRows": 1388, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.551797+08:00", "LastUpdateTime": "2025-06-19T16:19:23.508523+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartments": {"TableName": "Ledger_LedgerDepartments", "TotalRows": 168904, "MigratedRows": 168904, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.743463+08:00", "LastUpdateTime": "2025-06-19T16:19:24.800485+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerHierarchicalAuthorizations": {"TableName": "Ledger_LedgerHierarchicalAuthorizations", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.743916+08:00", "LastUpdateTime": "2025-06-19T16:19:25.806648+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerPermissionsAuthorizationModes": {"TableName": "Ledger_LedgerPermissionsAuthorizationModes", "TotalRows": 1263124, "MigratedRows": 1263124, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.94283+08:00", "LastUpdateTime": "2025-06-19T16:46:10.422264+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerRunways": {"TableName": "Ledger_LedgerRunways", "TotalRows": 2642, "MigratedRows": 2642, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:18.950766+08:00", "LastUpdateTime": "2025-06-19T16:46:11.573693+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerRunwayRelations": {"TableName": "Ledger_LedgerRunwayRelations", "TotalRows": 44, "MigratedRows": 44, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:19.184521+08:00", "LastUpdateTime": "2025-06-19T16:46:12.79405+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableQueryConfigs": {"TableName": "Ledger_TableQueryConfigs", "TotalRows": 256, "MigratedRows": 256, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:19.236625+08:00", "LastUpdateTime": "2025-06-19T16:46:14.000707+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerStatistics": {"TableName": "Ledger_LedgerStatistics", "TotalRows": 183, "MigratedRows": 183, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:19.463846+08:00", "LastUpdateTime": "2025-06-19T16:46:15.294131+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerTemplates": {"TableName": "Ledger_LedgerTemplates", "TotalRows": 139, "MigratedRows": 139, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:19.495744+08:00", "LastUpdateTime": "2025-06-19T16:46:16.308494+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerUsers": {"TableName": "Ledger_LedgerUsers", "TotalRows": 1096553, "MigratedRows": 1096553, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:19.71442+08:00", "LastUpdateTime": "2025-06-19T16:46:17.812297+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerWayAndRelations": {"TableName": "Ledger_LedgerWayAndRelations", "TotalRows": 297, "MigratedRows": 297, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:19.89044+08:00", "LastUpdateTime": "2025-06-19T16:46:18.822824+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LlmMessages": {"TableName": "Ledger_LlmMessages", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.112747+08:00", "LastUpdateTime": "2025-06-19T16:46:19.835449+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_MyLedgerExportRecords": {"TableName": "Ledger_MyLedgerExportRecords", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.161647+08:00", "LastUpdateTime": "2025-06-19T16:46:21.117998+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejectPublishes": {"TableName": "Ledger_OneVoteRejectPublishes", "TotalRows": 4, "MigratedRows": 4, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.16514+08:00", "LastUpdateTime": "2025-06-19T16:46:22.252905+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejectRecords": {"TableName": "Ledger_OneVoteRejectRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.357551+08:00", "LastUpdateTime": "2025-06-19T16:46:23.39364+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejects": {"TableName": "Ledger_OneVoteRejects", "TotalRows": 26, "MigratedRows": 26, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.613154+08:00", "LastUpdateTime": "2025-06-19T16:46:24.535365+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OnLineAnalysisConfigs": {"TableName": "Ledger_OnLineAnalysisConfigs", "TotalRows": 237, "MigratedRows": 237, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.637015+08:00", "LastUpdateTime": "2025-06-19T16:46:25.696516+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OnLineAnalysisEchartConfigs": {"TableName": "Ledger_OnLineAnalysisEchartConfigs", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.651434+08:00", "LastUpdateTime": "2025-06-19T16:46:26.841433+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_PlanProgressConfigs": {"TableName": "Ledger_PlanProgressConfigs", "TotalRows": 181, "MigratedRows": 181, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:20.870924+08:00", "LastUpdateTime": "2025-06-19T16:46:27.859252+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ResettleHelpEducates": {"TableName": "Ledger_ResettleHelpEducates", "TotalRows": 1072, "MigratedRows": 1072, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.137252+08:00", "LastUpdateTime": "2025-06-19T16:46:29.23381+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_RiskDetermines": {"TableName": "Ledger_RiskDetermines", "TotalRows": 1072, "MigratedRows": 1072, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.19441+08:00", "LastUpdateTime": "2025-06-19T16:46:30.618426+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ServeSentenceAndCriminalInfoStatistics": {"TableName": "Ledger_ServeSentenceAndCriminalInfoStatistics", "TotalRows": 1073, "MigratedRows": 1073, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.195423+08:00", "LastUpdateTime": "2025-06-19T16:46:31.906345+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SocialAssistances": {"TableName": "Ledger_SocialAssistances", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.443568+08:00", "LastUpdateTime": "2025-06-19T16:46:32.91826+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerTypes": {"TableName": "Ledger_SourceLedgerTypes", "TotalRows": 108, "MigratedRows": 108, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.776487+08:00", "LastUpdateTime": "2025-06-19T16:46:34.194442+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerFillStatistics": {"TableName": "Ledger_SourceLedgerFillStatistics", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.776816+08:00", "LastUpdateTime": "2025-06-19T16:46:35.408728+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerTypeRelations": {"TableName": "Ledger_SourceLedgerTypeRelations", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:21.818075+08:00", "LastUpdateTime": "2025-06-19T16:46:36.627812+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StreetBigDataEchartAnalyses": {"TableName": "Ledger_StreetBigDataEchartAnalyses", "TotalRows": 59, "MigratedRows": 59, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.070161+08:00", "LastUpdateTime": "2025-06-19T16:46:37.783677+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StreetBigDataEchartAnalysisConfigs": {"TableName": "Ledger_StreetBigDataEchartAnalysisConfigs", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.302784+08:00", "LastUpdateTime": "2025-06-19T16:46:38.931376+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataExportRecords": {"TableName": "Ledger_TableDataExportRecords", "TotalRows": 7798, "MigratedRows": 7798, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.303141+08:00", "LastUpdateTime": "2025-06-19T16:46:40.304358+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataImportRecords": {"TableName": "Ledger_TableDataImportRecords", "TotalRows": 78047, "MigratedRows": 78047, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.312656+08:00", "LastUpdateTime": "2025-06-19T16:46:41.439879+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetSourceDbTableInfos": {"TableName": "Ledger_TableDataSetSourceDbTableInfos", "TotalRows": 145, "MigratedRows": 145, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.492931+08:00", "LastUpdateTime": "2025-06-19T16:46:42.666456+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetJoinConfigs": {"TableName": "Ledger_TableDataSetJoinConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.749001+08:00", "LastUpdateTime": "2025-06-19T16:46:44.340152+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetJoinFieldConfigs": {"TableName": "Ledger_TableDataSetJoinFieldConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.791999+08:00", "LastUpdateTime": "2025-06-19T16:46:46.517047+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldCalculateRules": {"TableName": "Ledger_TableFieldCalculateRules", "TotalRows": 857, "MigratedRows": 857, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:22.836789+08:00", "LastUpdateTime": "2025-06-19T16:46:47.658336+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldClientSettings": {"TableName": "Ledger_TableFieldClientSettings", "TotalRows": 3686, "MigratedRows": 3686, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.014111+08:00", "LastUpdateTime": "2025-06-19T16:46:48.812634+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldMultiples": {"TableName": "Ledger_TableFieldMultiples", "TotalRows": 496, "MigratedRows": 496, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.212922+08:00", "LastUpdateTime": "2025-06-19T16:46:49.958601+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFiledValidateRules": {"TableName": "Ledger_TableFiledValidateRules", "TotalRows": 9, "MigratedRows": 9, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.217388+08:00", "LastUpdateTime": "2025-06-19T16:46:50.983113+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TelecomTaskItems": {"TableName": "Ledger_TelecomTaskItems", "TotalRows": 142, "MigratedRows": 142, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.270982+08:00", "LastUpdateTime": "2025-06-19T16:46:52.496908+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TaskChargePeople": {"TableName": "Ledger_TaskChargePeople", "TotalRows": 54, "MigratedRows": 54, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.423875+08:00", "LastUpdateTime": "2025-06-19T16:46:53.716949+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_AuthorityAudits": {"TableName": "NewFeature_AuthorityAudits", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.70566+08:00", "LastUpdateTime": "2025-06-19T16:46:54.72714+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_AutomatedVerifications": {"TableName": "NewFeature_AutomatedVerifications", "TotalRows": 8, "MigratedRows": 8, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.717679+08:00", "LastUpdateTime": "2025-06-19T16:46:55.739256+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_ProgressManagements": {"TableName": "NewFeature_ProgressManagements", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.718213+08:00", "LastUpdateTime": "2025-06-19T16:46:56.75127+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_ProgressManagementItems": {"TableName": "NewFeature_ProgressManagementItems", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:23.977304+08:00", "LastUpdateTime": "2025-06-19T16:46:57.894186+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_DataProcesses": {"TableName": "NewFeature_DataProcesses", "TotalRows": 5, "MigratedRows": 5, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.19668+08:00", "LastUpdateTime": "2025-06-19T16:46:59.051814+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_FunctionLogs": {"TableName": "NewFeature_FunctionLogs", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.281546+08:00", "LastUpdateTime": "2025-06-19T16:47:00.062767+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartGroups": {"TableName": "Platform_ChartGroups", "TotalRows": 1074, "MigratedRows": 1074, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.28604+08:00", "LastUpdateTime": "2025-06-19T16:47:01.075109+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Charts": {"TableName": "Platform_Charts", "TotalRows": 8760, "MigratedRows": 8760, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.591055+08:00", "LastUpdateTime": "2025-06-19T16:47:02.250594+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartDicDatas": {"TableName": "Platform_ChartDicDatas", "TotalRows": 26325, "MigratedRows": 26325, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.85971+08:00", "LastUpdateTime": "2025-06-19T16:47:03.431857+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartListDatas": {"TableName": "Platform_ChartListDatas", "TotalRows": 1298, "MigratedRows": 1298, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.861413+08:00", "LastUpdateTime": "2025-06-19T16:47:04.573977+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartTimeFlowDatas": {"TableName": "Platform_ChartTimeFlowDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:24.9032+08:00", "LastUpdateTime": "2025-06-19T16:47:05.727444+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentFavoriteGroups": {"TableName": "Platform_DepartmentFavoriteGroups", "TotalRows": 506, "MigratedRows": 506, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.081973+08:00", "LastUpdateTime": "2025-06-19T16:47:06.888577+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentFavorites": {"TableName": "Platform_DepartmentFavorites", "TotalRows": 13068, "MigratedRows": 13068, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.42422+08:00", "LastUpdateTime": "2025-06-19T16:47:08.029178+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentGroups": {"TableName": "Platform_DepartmentGroups", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.424945+08:00", "LastUpdateTime": "2025-06-19T16:47:09.168673+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentGroupDepartments": {"TableName": "Platform_DepartmentGroupDepartments", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.436592+08:00", "LastUpdateTime": "2025-06-19T16:47:10.381874+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Regions": {"TableName": "Platform_Regions", "TotalRows": 12381, "MigratedRows": 12381, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.628361+08:00", "LastUpdateTime": "2025-06-19T16:47:11.527069+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_QuestionFeedbacks": {"TableName": "Platform_QuestionFeedbacks", "TotalRows": 101, "MigratedRows": 101, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.844124+08:00", "LastUpdateTime": "2025-06-19T16:47:12.7426+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_RegionFavoriteGroups": {"TableName": "Platform_RegionFavoriteGroups", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.892024+08:00", "LastUpdateTime": "2025-06-19T16:47:13.900186+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_RegionFavorites": {"TableName": "Platform_RegionFavorites", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:25.902777+08:00", "LastUpdateTime": "2025-06-19T16:47:15.055931+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ReportLedgerNotices": {"TableName": "Platform_ReportLedgerNotices", "TotalRows": 44868, "MigratedRows": 44868, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.147068+08:00", "LastUpdateTime": "2025-06-19T16:47:16.069929+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_SyncRecords": {"TableName": "Platform_SyncRecords", "TotalRows": 812845, "MigratedRows": 812845, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.424776+08:00", "LastUpdateTime": "2025-06-19T16:47:18.097863+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Routes": {"TableName": "Platform_Routes", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.425723+08:00", "LastUpdateTime": "2025-06-19T16:47:17.088029+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartmentFavoriteGroups": {"TableName": "Platform_UserDepartmentFavoriteGroups", "TotalRows": 54, "MigratedRows": 54, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.469208+08:00", "LastUpdateTime": "2025-06-19T16:47:19.10888+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartmentRoles": {"TableName": "Platform_UserDepartmentRoles", "TotalRows": 522893, "MigratedRows": 522893, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.696559+08:00", "LastUpdateTime": "2025-06-19T16:47:20.124587+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartments": {"TableName": "Platform_UserDepartments", "TotalRows": 208402, "MigratedRows": 208402, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.905099+08:00", "LastUpdateTime": "2025-06-19T16:47:21.142373+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WebWidgets": {"TableName": "Platform_WebWidgets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.957141+08:00", "LastUpdateTime": "2025-06-19T16:47:22.159725+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFPlanTasks": {"TableName": "Platform_WFPlanTasks", "TotalRows": 2132, "MigratedRows": 2132, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:26.970702+08:00", "LastUpdateTime": "2025-06-19T16:47:23.395484+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFTaskItems": {"TableName": "Platform_WFTaskItems", "TotalRows": 44693, "MigratedRows": 44693, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.150941+08:00", "LastUpdateTime": "2025-06-19T16:47:24.542083+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFDataProcesses": {"TableName": "Platform_WFDataProcesses", "TotalRows": 2510, "MigratedRows": 2510, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.407313+08:00", "LastUpdateTime": "2025-06-19T16:47:25.709367+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WorkToDoRecords": {"TableName": "Platform_WorkToDoRecords", "TotalRows": 40142, "MigratedRows": 40142, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.431819+08:00", "LastUpdateTime": "2025-06-19T16:47:26.719824+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YbtUsers": {"TableName": "Platform_YbtUsers", "TotalRows": 280, "MigratedRows": 280, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.469498+08:00", "LastUpdateTime": "2025-06-19T16:47:27.734779+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YkzOrganizations": {"TableName": "Platform_YkzOrganizations", "TotalRows": 193002, "MigratedRows": 193002, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.709714+08:00", "LastUpdateTime": "2025-06-19T16:47:28.746319+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YkzOrgUnits": {"TableName": "Platform_YkzOrgUnits", "TotalRows": 248996, "MigratedRows": 248996, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.962869+08:00", "LastUpdateTime": "2025-06-19T16:47:29.759414+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_Applications": {"TableName": "RPA_Applications", "TotalRows": 8, "MigratedRows": 8, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:27.963243+08:00", "LastUpdateTime": "2025-06-19T16:47:30.778883+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ApplicationDepartments": {"TableName": "RPA_ApplicationDepartments", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.004831+08:00", "LastUpdateTime": "2025-06-19T16:47:31.92928+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ApplicationUsers": {"TableName": "RPA_ApplicationUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.194902+08:00", "LastUpdateTime": "2025-06-19T16:47:33.066872+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_AppTasks": {"TableName": "RPA_AppTasks", "TotalRows": 4, "MigratedRows": 4, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.400419+08:00", "LastUpdateTime": "2025-06-19T16:47:34.211351+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_AppTaskItems": {"TableName": "RPA_AppTaskItems", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.455254+08:00", "LastUpdateTime": "2025-06-19T16:47:35.353394+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_DataSources": {"TableName": "RPA_DataSources", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.456179+08:00", "LastUpdateTime": "2025-06-19T16:47:36.487648+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_DataFieldMappings": {"TableName": "RPA_DataFieldMappings", "TotalRows": 96, "MigratedRows": 96, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.662122+08:00", "LastUpdateTime": "2025-06-19T16:47:38.234173+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ProcessServices": {"TableName": "RPA_ProcessServices", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.900361+08:00", "LastUpdateTime": "2025-06-19T16:47:39.246293+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ProcessFields": {"TableName": "RPA_ProcessFields", "TotalRows": 21, "MigratedRows": 21, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.944853+08:00", "LastUpdateTime": "2025-06-19T16:47:40.38233+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "SrcOrgs": {"TableName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TotalRows": 98513, "MigratedRows": 98513, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:28.945223+08:00", "LastUpdateTime": "2025-06-19T16:47:41.391469+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TableInfosToCheck": {"TableName": "TableInfosToCheck", "TotalRows": 6938, "MigratedRows": 6938, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:29.129455+08:00", "LastUpdateTime": "2025-06-19T16:47:42.402486+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Batch": {"TableName": "Te<PERSON>_Batch", "TotalRows": 30500, "MigratedRows": 30500, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:29.299411+08:00", "LastUpdateTime": "2025-06-19T16:47:43.416175+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Fill_Fix": {"TableName": "Temp_Ledger_Department_Fill_Fix", "TotalRows": 41958, "MigratedRows": 41958, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:29.537363+08:00", "LastUpdateTime": "2025-06-19T16:47:44.425899+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Operation_Count": {"TableName": "Temp_Ledger_Department_Operation_Count", "TotalRows": 41845, "MigratedRows": 41845, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:29.6748+08:00", "LastUpdateTime": "2025-06-19T16:47:45.434071+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Operation_Count_NewV1": {"TableName": "Temp_Ledger_Department_Operation_Count_NewV1", "TotalRows": 70, "MigratedRows": 70, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:29.842043+08:00", "LastUpdateTime": "2025-06-19T16:47:46.450567+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TempOrgs": {"TableName": "TempOrgs", "TotalRows": 98513, "MigratedRows": 98513, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:29.892884+08:00", "LastUpdateTime": "2025-06-19T16:47:47.469265+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TempUsers": {"TableName": "TempUsers", "TotalRows": 455368, "MigratedRows": 455368, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.069395+08:00", "LastUpdateTime": "2025-06-19T16:47:48.478581+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Test_DEC": {"TableName": "Test_DEC", "TotalRows": 8, "MigratedRows": 8, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.13376+08:00", "LastUpdateTime": "2025-06-19T16:47:49.532026+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TK_BackgroundJobLogs": {"TableName": "TK_BackgroundJobLogs", "TotalRows": 1508750, "MigratedRows": 1508750, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.385838+08:00", "LastUpdateTime": "2025-06-19T16:47:50.554664+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TK_BackgroundJobs": {"TableName": "TK_Background<PERSON><PERSON>s", "TotalRows": 246015, "MigratedRows": 246015, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.386191+08:00", "LastUpdateTime": "2025-06-19T16:47:51.614413+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowTasks": {"TableName": "WF_WorkflowTasks", "TotalRows": 16853, "MigratedRows": 16853, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.596236+08:00", "LastUpdateTime": "2025-06-19T16:47:52.997489+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeInfos": {"TableName": "WF_WorkflowSchemeInfos", "TotalRows": 293, "MigratedRows": 293, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.609057+08:00", "LastUpdateTime": "2025-06-19T16:47:54.007111+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemes": {"TableName": "WF_WorkflowSchemes", "TotalRows": 595, "MigratedRows": 595, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.844429+08:00", "LastUpdateTime": "2025-06-19T16:47:55.13944+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowProcesses": {"TableName": "WF_WorkflowProcesses", "TotalRows": 5021, "MigratedRows": 5021, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:30.875043+08:00", "LastUpdateTime": "2025-06-19T16:47:56.434479+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeAuths": {"TableName": "WF_WorkflowSchemeAuths", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:31.08007+08:00", "LastUpdateTime": "2025-06-19T16:47:57.451754+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeInfoPermissionGrants": {"TableName": "WF_WorkflowSchemeInfoPermissionGrants", "TotalRows": 399, "MigratedRows": 399, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:31.123683+08:00", "LastUpdateTime": "2025-06-19T16:47:58.585085+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowStamps": {"TableName": "WF_WorkflowStamps", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:31.371232+08:00", "LastUpdateTime": "2025-06-19T16:47:59.600792+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowTaskLogs": {"TableName": "WF_WorkflowTaskLogs", "TotalRows": 9363, "MigratedRows": 9363, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:31.409455+08:00", "LastUpdateTime": "2025-06-19T16:48:00.915455+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowUnits": {"TableName": "WF_WorkflowUnits", "TotalRows": 4646, "MigratedRows": 4646, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-19T16:06:31.603069+08:00", "LastUpdateTime": "2025-06-19T16:48:02.04955+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}}, "ErrorMessage": "表 hg_t_audit_log 迁移失败: 获取表行数失败: 获取表hg_t_audit_log的总行数在3次尝试后操作仍然失败。最后一次错误: XX000: permission denied "}