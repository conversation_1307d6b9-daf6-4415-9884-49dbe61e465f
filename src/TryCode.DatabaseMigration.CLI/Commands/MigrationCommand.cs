using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using TryCode.DatabaseMigration.Checkpoint;
using TryCode.DatabaseMigration.CLI.Display;
using Spectre.Console;
using TryCode.DatabaseMigration.CLI.Options;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Extensions.Logging;
using ILogger = Microsoft.Extensions.Logging.ILogger;
using TryCode.DatabaseMigration.Configuration;
using TryCode.DatabaseMigration.Core.Exceptions;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.MySQL;
using TryCode.DatabaseMigration.Parallel;
using TryCode.DatabaseMigration.Parallel.Models;
using TryCode.DatabaseMigration.PostgreSQL;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.CLI.Commands
{
    /// <summary>
    /// 迁移命令
    /// </summary>
    public class MigrationCommand : IDisposable
    {
        private readonly MigrationOptions _options;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly ConsoleColor _defaultColor;
        private readonly ILogger _logger;
        private readonly ILoggerFactory _loggerFactory;

        private static readonly ILoggerFactory _staticLoggerFactory;

        static MigrationCommand()
        {
            // 初始化一个基本的日志记录器，后续会在实例构造函数中根据配置更新
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .CreateLogger();

            // 创建静态日志工厂
            _staticLoggerFactory = LoggerFactory.Create(builder => { builder.AddSerilog(Log.Logger, dispose: false); });
        }

        public MigrationCommand(MigrationOptions options)
        {
            _options = options ?? throw new ArgumentNullException(nameof(options));
            _cancellationTokenSource = new CancellationTokenSource();
            _defaultColor = Console.ForegroundColor;

            // 加载配置文件以获取日志设置
            try
            {
                var provider = new ConfigurationProvider(options.ConfigFile);
                var config = provider.GetMigrationConfig();

                // 配置Serilog
                var loggerConfig = new LoggerConfiguration()
                    .WriteTo.Console();

                // 设置日志级别
                switch (config.Logging.Level.ToLower())
                {
                    case "debug":
                        loggerConfig.MinimumLevel.Debug();
                        break;
                    case "information":
                        loggerConfig.MinimumLevel.Information();
                        break;
                    case "warning":
                        loggerConfig.MinimumLevel.Warning();
                        break;
                    case "error":
                        loggerConfig.MinimumLevel.Error();
                        break;
                    default:
                        loggerConfig.MinimumLevel.Information();
                        break;
                }

                // 添加文件日志
                if (!string.IsNullOrEmpty(config.Logging.FilePath))
                {
                    // 确保日志目录存在
                    var logDir = Path.GetDirectoryName(config.Logging.FilePath);
                    if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
                    {
                        Directory.CreateDirectory(logDir);
                    }

                    loggerConfig.WriteTo.File(
                        config.Logging.FilePath,
                        rollingInterval: RollingInterval.Day,
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}");
                }

                // 更新全局日志记录器
                Log.Logger = loggerConfig.CreateLogger();

                // 重新创建日志工厂
                _loggerFactory = LoggerFactory.Create(builder => { builder.AddSerilog(Log.Logger, dispose: false); });
            }
            catch (Exception ex)
            {
                // 如果配置加载失败，使用默认日志记录器
                Console.WriteLine($"警告: 无法加载日志配置，使用默认设置。错误: {ex.Message}");
                _loggerFactory = _staticLoggerFactory;
            }

            _logger = _loggerFactory.CreateLogger<MigrationCommand>();

            // 注册Ctrl+C处理
            Console.CancelKeyPress += (s, e) =>
            {
                e.Cancel = true;
                _cancellationTokenSource.Cancel();
                WriteWarning("\n正在取消迁移...");
            };
        }

        /// <summary>
        /// 执行迁移命令
        /// </summary>
        public async Task<int> ExecuteMigrationCommandAsync()
        {
            try
            {
                // 记录开始执行的日志
                _logger.LogInformation("开始执行数据库迁移命令，配置文件: {ConfigFile}", _options.ConfigFile);

                // 加载配置
                var provider = new ConfigurationProvider(_options.ConfigFile);
                var config = await provider.GetMigrationConfigAsync();

                // 应用命令行选项
                ApplyCommandLineOptions(config);

                // 如果是演习模式或仅显示计划模式，显示配置信息和迁移计划并返回
                if (_options.DryRun || _options.ShowPlanOnly)
                {
                    await DisplayDryRunInfo(config);
                    return 0;
                }

                // 创建数据库提供程序
                var (sourceReader, targetWriter) = CreateDatabaseProviders(config);

                // 如果不是无确认模式，显示迁移计划并请求确认
                if (!_options.NoConfirm)
                {
                    // 显示迁移计划
                    await DisplayMigrationPlanAsync(config);

                    Console.WriteLine();
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.Write("是否继续执行迁移操作？(y/N): ");
                    Console.ForegroundColor = _defaultColor;

                    var response = Console.ReadLine()?.Trim().ToLower();
                    if (string.IsNullOrEmpty(response) || response != "y")
                    {
                        WriteInfo("已取消迁移操作。");
                        return 0;
                    }
                }

                // 创建断点管理器
                CheckpointManager? checkpointManager = null;
                if (config.EnableCheckpoint)
                {
                    checkpointManager = new CheckpointManager(config.CheckpointFilePath);
                }

                // 创建进度显示
                using var progress = _options.NoProgress
                    ? null
                    : new SpectreConsoleDisplay(
                        verbose: _options.Verbose);

                // 创建并行迁移执行器
                var checkpointManagerObj = checkpointManager ?? new CheckpointManager(Path.GetTempFileName());
                var checkpointAdapter = new CheckpointManagerAdapter(checkpointManagerObj);

                var executor = new ParallelMigrationExecutor(
                    sourceReader,
                    targetWriter,
                    checkpointAdapter,
                    config,
                    progress,
                    _logger);

                // 执行迁移
                await executor.ExecuteAsync(_cancellationTokenSource.Token);

                _logger.LogInformation("数据库迁移成功完成");
                WriteSuccess("\n迁移完成！");
                return 0;
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("迁移操作被用户取消");
                WriteWarning("\n迁移已取消。");
                return 1;
            }
            catch (ConfigurationValidationException ex)
            {
                _logger.LogError(ex, "配置验证错误: {Message}", ex.Message);
                WriteError($"\n配置错误: {ex.Message}");
                return 1;
            }
            catch (DatabaseConnectionException ex)
            {
                _logger.LogError(ex, "数据库连接错误: {Message}", ex.Message);
                WriteError($"\n数据库连接错误: {ex.Message}");
                if (ex.InnerException != null)
                {
                    _logger.LogError(ex.InnerException, "数据库连接内部错误: {Message}", ex.InnerException.Message);
                    WriteError($"详细信息: {ex.InnerException.Message}");
                }

                return 1;
            }
            catch (MigrationException ex)
            {
                _logger.LogError(ex, "迁移错误 [{Stage}]: {Message}", ex.Stage, ex.Message);
                WriteError($"\n迁移错误 [{ex.Stage}]: {ex.Message}");
                if (ex.TableName != null)
                {
                    _logger.LogError("错误发生在表: {TableName}", ex.TableName);
                    WriteError($"表: {ex.TableName}");
                }

                if (ex.InnerException != null)
                {
                    _logger.LogError(ex.InnerException, "迁移内部错误: {Message}", ex.InnerException.Message);
                    WriteError($"详细信息: {ex.InnerException.Message}");
                }

                return 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "未知错误: {Message}", ex.Message);
                WriteError($"\n未知错误: {ex.Message}");
                if (_options.Verbose)
                {
                    WriteError(ex.StackTrace ?? string.Empty);
                }

                return 1;
            }
        }

        /// <summary>
        /// 应用命令行选项到配置
        /// </summary>
        private void ApplyCommandLineOptions(Configuration.Models.MigrationConfig config)
        {
            if (_options.Tables != null && _options.Tables.Any())
            {
                config.Tables = new List<string>(_options.Tables);
            }

            config.MaxDegreeOfParallelism = _options.MaxDegreeOfParallelism;
            config.SkipSchemaCreation = _options.SkipSchemaCreation;
            config.SkipDataMigration = _options.SchemaOnly;
            config.SkipForeignKeys = _options.SkipForeignKeys;
            config.RetryCount = _options.RetryCount;
            config.RetryDelay = _options.RetryDelay;
            config.IgnoreErrors = _options.IgnoreErrors;
            if (!string.IsNullOrEmpty(_options.ReportFile))
            {
                config.ReportFilePath = _options.ReportFile;
            }

            if (!string.IsNullOrEmpty(_options.CheckpointFile))
            {
                config.EnableCheckpoint = true;
                config.CheckpointFilePath = _options.CheckpointFile;
            }

            config.Source.BatchSize = _options.BatchSize;
            config.Source.CommandTimeout = _options.CommandTimeout;
            config.Target.BatchSize = _options.BatchSize;
            config.Target.CommandTimeout = _options.CommandTimeout;
        }

        /// <summary>
        /// 显示演习模式信息
        /// </summary>
        private async Task DisplayDryRunInfo(Configuration.Models.MigrationConfig config)
        {
            WriteInfo("\n迁移配置预览：");
            WriteInfo($"源数据库: {config.Source.Type}");
            WriteInfo($"目标数据库: {config.Target.Type}");
            WriteInfo($"要迁移的表: {(config.Tables?.Any() == true ? string.Join(", ", config.Tables) : "全部")}");
            if (config.ExcludedTables?.Any() == true)
            {
                WriteInfo($"排除的表: {string.Join(", ", config.ExcludedTables)}");
                WriteInfo($"为排除的表创建表结构: {config.CreateSchemaForExcludedTables}");
            }
            WriteInfo($"最大并行度: {config.MaxDegreeOfParallelism}");
            WriteInfo($"批处理大小: {config.Source.BatchSize}");
            WriteInfo($"命令超时: {config.Source.CommandTimeout}秒");
            WriteInfo($"重试次数: {config.RetryCount}");
            WriteInfo($"重试延迟: {config.RetryDelay}秒");
            WriteInfo($"跳过表结构: {config.SkipSchemaCreation}");
            WriteInfo($"只迁移表结构: {config.SkipDataMigration}");
            WriteInfo($"跳过外键: {config.SkipForeignKeys}");
            WriteInfo($"忽略错误继续迁移: {config.IgnoreErrors}");
            WriteInfo($"迁移报告文件: {config.ReportFilePath}");
            WriteInfo($"启用断点续传: {config.EnableCheckpoint}");
            if (config.EnableCheckpoint)
            {
                WriteInfo($"断点文件: {config.CheckpointFilePath}");
            }

            // 显示详细迁移计划
            await DisplayMigrationPlanAsync(config);
        }

        /// <summary>
        /// 显示详细迁移计划
        /// </summary>
        private async Task DisplayMigrationPlanAsync(Configuration.Models.MigrationConfig config)
        {
            try
            {
                // 创建数据库提供程序
                var (sourceReader, _) = CreateDatabaseProviders(config);

                // 获取所有表信息
                var allTables = await sourceReader.GetTableSchemasAsync();

                // 应用包含/排除规则
                var filteredTables = config.Tables?.Any() == true
                    ? allTables.Where(t => config.Tables.Contains(t.Name)).ToList()
                    : allTables.Where(t => !config.ExcludedTables.Contains(t.Name)).ToList();

                if (filteredTables.Count == 0)
                {
                    WriteWarning("\n没有找到符合条件的表！请检查配置。");
                    return;
                }

                // 计算表的依赖关系图
                var dependencyGraph = new TableDependencyGraph();
                foreach (var table in filteredTables)
                {
                    foreach (var fk in table.ForeignKeys)
                    {
                        dependencyGraph.AddDependency(table.Name, fk.ReferencedTable);
                    }
                }

                // 排序表
                var sortedTables = dependencyGraph.TopologicalSort(filteredTables);

                // 获取表的行数信息
                var tableRowCounts = new Dictionary<string, long>();
                WriteInfo("\n正在获取表的数据量信息...");

                foreach (var table in filteredTables)
                {
                    try
                    {
                        var rowCount = await sourceReader.GetTableRowCountAsync(table.Name);
                        tableRowCounts[table.Name] = rowCount;
                    }
                    catch (Exception ex)
                    {
                        WriteWarning($"无法获取表 {table.Name} 的行数: {ex.Message}");
                        tableRowCounts[table.Name] = 0;
                    }
                }

                // 显示迁移计划
                var planDisplay = new MigrationPlanDisplay(filteredTables, sortedTables, tableRowCounts);
                planDisplay.Display();
            }
            catch (DatabaseConnectionException ex)
            {
                WriteError($"\n获取迁移计划时出错: 无法连接到数据库");
                WriteError($"错误详情: {ex.Message}");
                if (ex.InnerException != null)
                {
                    WriteError($"内部错误: {ex.InnerException.Message}");
                }

                WriteInfo("\n请检查以下内容:");
                WriteInfo("1. 数据库连接字符串是否正确");
                WriteInfo("2. 数据库服务是否正在运行");
                WriteInfo("3. 数据库用户是否有足够的权限");
                WriteInfo("4. 网络连接是否正常");
            }
            catch (Exception ex)
            {
                WriteError($"\n获取迁移计划时出错: {ex.Message}");
                if (_options.Verbose)
                {
                    WriteError(ex.StackTrace ?? string.Empty);
                }
            }
        }

        /// <summary>
        /// 创建数据库提供程序
        /// </summary>
        private (IDataReader, IDataWriter) CreateDatabaseProviders(Configuration.Models.MigrationConfig config)
        {
            try
            {
                IDataReader sourceReader = config.Source.Type.ToLower() switch
                {
                    "postgresql" => new PostgresProviderFactory(config.Source.ConnectionString, _logger).CreateReader(),
                    "mysql" => new MySqlProviderFactory(config.Source.ConnectionString).CreateReader(),
                    _ => throw new ConfigurationValidationException($"不支持的源数据库类型: {config.Source.Type}")
                };

                IDataWriter targetWriter = config.Target.Type.ToLower() switch
                {
                    "postgresql" => new PostgresProviderFactory(config.Target.ConnectionString, _logger).CreateWriter(),
                    "mysql" => new MySqlProviderFactory(config.Target.ConnectionString,typeMappings:config.TypeMappings).CreateWriter(),
                    _ => throw new ConfigurationValidationException($"不支持的目标数据库类型: {config.Target.Type}")
                };

                return (sourceReader, targetWriter);
            }
            catch (Exception ex) when (!(ex is ConfigurationValidationException))
            {
                throw new DatabaseConnectionException("创建数据库提供程序失败", ex);
            }
        }

        private void WriteSuccess(string message)
        {
            AnsiConsole.MarkupLine($"[bold green]{message}[/]");
        }

        private void WriteInfo(string message)
        {
            AnsiConsole.MarkupLine($"[blue]{message}[/]");
        }

        private void WriteWarning(string message)
        {
            AnsiConsole.MarkupLine($"[bold yellow]{message}[/]");
        }

        private void WriteError(string message)
        {
            AnsiConsole.MarkupLine($"[bold red]{message}[/]");
        }

        private bool _disposed;

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    _cancellationTokenSource.Dispose();
                }

                _disposed = true;
            }
        }

        public static void CloseLogger()
        {
            try
            {
                // 确保所有日志都被写入文件
                Log.Information("正在关闭日志记录器...");
                (_staticLoggerFactory as IDisposable)?.Dispose();
                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"关闭日志记录器时发生错误: {ex.Message}");
            }
        }

        ~MigrationCommand()
        {
            Dispose(false);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}