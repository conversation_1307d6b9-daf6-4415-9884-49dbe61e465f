using System;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.CLI.Options;
using TryCode.DatabaseMigration.Configuration;
using TryCode.DatabaseMigration.Core.Exceptions;
using TryCode.DatabaseMigration.Core.Helpers;

namespace TryCode.DatabaseMigration.CLI.Commands
{
    /// <summary>
    /// 验证命令
    /// </summary>
    public class ValidateCommand
    {
        private readonly ValidateOptions _options;
        private readonly ConsoleColor _defaultColor;

        public ValidateCommand(ValidateOptions options)
        {
            _options = options;
            _defaultColor = Console.ForegroundColor;
        }

        /// <summary>
        /// 执行验证命令
        /// </summary>
        public async Task<int> ExecuteAsync()
        {
            try
            {
                // 验证配置文件
                WriteInfo("正在验证配置文件...");
                var provider = new ConfigurationProvider(_options.ConfigFile);
                var config = await provider.GetMigrationConfigAsync();
                WriteSuccess("配置文件验证通过！");

                if (_options.TestConnection)
                {
                    // 测试源数据库连接
                    WriteInfo($"正在测试源数据库连接 ({config.Source.Type})...");
                    await DatabaseConnectionTester.TestConnectionAsync(
                        config.Source.Type,
                        config.Source.ConnectionString);
                    WriteSuccess("源数据库连接测试通过！");

                    // 测试目标数据库连接
                    WriteInfo($"正在测试目标数据库连接 ({config.Target.Type})...");
                    await DatabaseConnectionTester.TestConnectionAsync(
                        config.Target.Type,
                        config.Target.ConnectionString);
                    WriteSuccess("目标数据库连接测试通过！");
                }

                WriteSuccess("所有验证项目通过！");
                return 0;
            }
            catch (ConfigurationValidationException ex)
            {
                WriteError($"配置验证失败: {ex.Message}");
                return 1;
            }
            catch (DatabaseConnectionException ex)
            {
                WriteError($"数据库连接测试失败: {ex.Message}");
                if (ex.InnerException != null)
                {
                    WriteError($"详细信息: {ex.InnerException.Message}");
                }
                return 1;
            }
            catch (Exception ex)
            {
                WriteError($"验证过程中发生错误: {ex.Message}");
                return 1;
            }
        }

        private void WriteInfo(string message)
        {
            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine(message);
            Console.ForegroundColor = _defaultColor;
        }

        private void WriteSuccess(string message)
        {
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine(message);
            Console.ForegroundColor = _defaultColor;
        }

        private void WriteError(string message)
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine(message);
            Console.ForegroundColor = _defaultColor;
        }
    }
}
