2025-06-20 15:52:52.298 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql.config.json
2025-06-20 16:02:23.751 +08:00 [INF] 获取到285个表
2025-06-20 16:02:23.754 +08:00 [INF] 正在更新断点文件中的排除表信息...
2025-06-20 16:02:23.768 +08:00 [INF] 已将表 AbpAuditLogActions 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.769 +08:00 [INF] 已将表 AbpAuditLogs 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.769 +08:00 [INF] 已将表 AbpEntityChanges 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.770 +08:00 [INF] 已将表 AbpEntityPropertyChanges 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.770 +08:00 [INF] 已将表 AbpSecurityLogs 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.770 +08:00 [INF] 已将表 AbpUserClaims 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.771 +08:00 [INF] 已将表 AppNotifications 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.771 +08:00 [INF] 已将表 AppUserNotifications 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.773 +08:00 [INF] 已将表 AppUserSubscribes 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.774 +08:00 [INF] 已将表 Ledger_LedgerDataUpdateLogs 在断点文件中标记为仅创建结构
2025-06-20 16:02:23.778 +08:00 [INF] 分区表处理完成，原始表数: 275，处理后表数: 275
2025-06-20 16:02:23.778 +08:00 [INF] 将为以下排除的表创建表结构（不迁移数据）: AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 16:02:23.778 +08:00 [INF] 过滤后剩余285个表
2025-06-20 16:02:23.778 +08:00 [INF] 使用并行度: 4
2025-06-20 16:02:23.790 +08:00 [INF] 开始处理以下表的迁移: __EFMigrationsHistory, AbpClaimTypes, AbpEditions, AbpFeatureValues, AbpLinkUsers, AbpLocalizationLanguages, AbpLocalizationResources, AbpLocalizationTexts, AbpOrganizationUnitRoles, AbpOrganizationUnits, AbpPermissionGrants, AbpRoleClaims, AbpRoles, AbpSettings, AbpTenantConnectionStrings, AbpTenants, AbpUserLogins, AbpUserOrganizationUnits, AbpUserRoles, AbpUserRoles_923bf, AbpUsers, AbpUserTokens, AbpWebhooksEvents, AbpWebhooksSendAttempts, AbpWebhooksSubscriptions, Airport_AssetLiabilities, Airport_BudgetAccounts, Airport_BudgetOrganizations, Airport_FinanceAuditBatches, Airport_FinanceAuditedDatas, Airport_FinanceProjects, Airport_NoFinanceProjects, Airport_NoFinancialBudgetProjects, Airport_OverheadCostCenters, AppChatGroups, AppGroupChatBlacks, AppGroupMessages, AppNotificationDefinitionGroups, AppNotificationDefinitions, AppPlatformDataItems, AppPlatformDatas, AppPlatformLayouts, AppPlatformMenus, AppPlatformPackageBlobs, AppPlatformPackages, AppPlatformRoleMenus, AppPlatformUserFavoriteMenus, AppPlatformUserMenus, AppPlatformVersion, AppPlatformVersionFile, AppUserChatCards, AppUserChatFriends, AppUserChatGroups, AppUserChatSettings, AppUserGroupCards, AppUserMessages, Bank_Ledger_BaseInfoStatistics, Bank_Ledger_ResettleHelpEducates, Bank_Ledger_RiskDetermines, Bank_Ledger_ServeSentenceAndCriminalInfoStatistics, BigScreenAuditDatas, BSPUser, dept_new_zhong, excelbookuser, excepbook, ExistingTables, fill_user_book, Filling_AreaOrganizationUnits, Filling_CollaborativeReports, Filling_FileInfos, Filling_FillingConfigs, Filling_PlanTaskAreaOrganizationUnits, Filling_PlanTaskReportTableTemplates, Filling_PlanTasks, Filling_PlanTaskStaffAreaOrganizationUnits, Filling_PlanTaskStaffs, Filling_ReportMessageInfos, Filling_ReportTableRows, Filling_ReportTables, Filling_ReportTableTemplates, Filling_ReportTaskAreaOrganizationUnitAudits, Filling_ReportTaskAreaOrganizationUnitFillers, Filling_ReportTaskAreaOrganizationUnits, Filling_ReportTasks, Filling_Staff, Filling_StaffPlanTaskHides, Filling_TableLedgerRuleConfigs, Filling_TableTemplateColumns, Filling_TableTemplateDataRows, Filling_YkzOrgUnits, hg_t_audit_log, IdentityServerApiResourceClaims, IdentityServerApiResourceProperties, IdentityServerApiResources, IdentityServerApiResourceScopes, IdentityServerApiResourceSecrets, IdentityServerApiScopeClaims, IdentityServerApiScopeProperties, IdentityServerApiScopes, IdentityServerClientClaims, IdentityServerClientCorsOrigins, IdentityServerClientGrantTypes, IdentityServerClientIdPRestrictions, IdentityServerClientPostLogoutRedirectUris, IdentityServerClientProperties, IdentityServerClientRedirectUris, IdentityServerClients, IdentityServerClientScopes, IdentityServerClientSecrets, IdentityServerDeviceFlowCodes, IdentityServerIdentityResourceClaims, IdentityServerIdentityResourceProperties, IdentityServerIdentityResources, IdentityServerPersistedGrants, Ledger_AjcBaseInfoStatistics, Ledger_ApiAuxiliaryFillingRules, Ledger_ApiAuxiliaryFillings, Ledger_ApiAuxiliaryFillSources, Ledger_ApiDataSetMappingFields, Ledger_ApiDataSets, Ledger_ApiRequestParameters, Ledger_AssociatedConfigs, Ledger_AssociatedSynConfigs, Ledger_AssociatedSyns, Ledger_BaseInfoStatistics, Ledger_BigViewTypicalScenarios, Ledger_BigViewTypicalScenarioUploadRecords, Ledger_CommonGoalsTasks, Ledger_DataItemsManagements, Ledger_DataItemsUpdateRecords, Ledger_DataSources, Ledger_DbTableFields, Ledger_DeductPoints, Ledger_DeductPointsPublishes, Ledger_DeductPointsRecords, Ledger_Disabilities, Ledger_HierarchicalLedgerDataPushOriginPlans, Ledger_HierarchicalLedgers, Ledger_HierarchicalLedgerTaskItems, Ledger_HistoryEvaluates, Ledger_IndicatorCockpitConfigs, Ledger_IndicatorCockpits, Ledger_LedgerAuditBatches, Ledger_LedgerAuditBatchFlowNodes, Ledger_LedgerAuditBatchFlows, Ledger_LedgerAuditBatchStatistics, Ledger_LedgerAuxiliaryFillingConfigs, Ledger_LedgerAuxiliaryFillingPreviews, Ledger_LedgerAuxiliaryFillings, Ledger_LedgerChangeRecordDetails, Ledger_LedgerChangeRecords, Ledger_LedgerDataAnalyses, Ledger_LedgerDataComparisonRecords, Ledger_LedgerDataSetAuxiliaryFillingConfigs, Ledger_LedgerDataSetAuxiliaryFillings, Ledger_LedgerDataSyncConfigs, Ledger_LedgerDataSyncFields, Ledger_LedgerDataSyncTasks, Ledger_LedgerDepartmentDataDetailStatistics, Ledger_LedgerDepartmentDataStatistics, Ledger_LedgerDepartments, Ledger_LedgerHierarchicalAuthorizations, Ledger_LedgerPermissionsAuthorizationModes, Ledger_LedgerRunwayRelations, Ledger_LedgerRunways, Ledger_Ledgers, Ledger_LedgerStatistics, Ledger_LedgerTemplates, Ledger_LedgerTypes, Ledger_LedgerUsers, Ledger_LedgerWayAndRelations, Ledger_LlmMessages, Ledger_MyLedgerExportRecords, Ledger_OneVoteRejectPublishes, Ledger_OneVoteRejectRecords, Ledger_OneVoteRejects, Ledger_OnLineAnalysisConfigs, Ledger_OnLineAnalysisEchartConfigs, Ledger_PlanProgressConfigs, Ledger_ResettleHelpEducates, Ledger_RiskDetermines, Ledger_ServeSentenceAndCriminalInfoStatistics, Ledger_SocialAssistances, Ledger_SourceDbTableInfos, Ledger_SourceLedgerFillStatistics, Ledger_SourceLedgerTypeRelations, Ledger_SourceLedgerTypes, Ledger_StarEvaluatePublishes, Ledger_StarEvaluateRecords, Ledger_StarEvaluates, Ledger_StreetBigDataEchartAnalyses, Ledger_StreetBigDataEchartAnalysisConfigs, Ledger_TableDataExportRecords, Ledger_TableDataImportRecords, Ledger_TableDataSetJoinConfigs, Ledger_TableDataSetJoinFieldConfigs, Ledger_TableDataSetMappingFields, Ledger_TableDataSets, Ledger_TableDataSetSourceDbTableInfos, Ledger_TableFieldCalculateRules, Ledger_TableFieldClientSettings, Ledger_TableFieldGroups, Ledger_TableFieldMultiples, Ledger_TableFields, Ledger_TableFiledValidateRules, Ledger_TableInfos, Ledger_TableQueryConfigs, Ledger_TaskChargePeople, Ledger_TelecomTaskItems, NewFeature_AuthorityAudits, NewFeature_AutomatedVerifications, NewFeature_DataProcesses, NewFeature_FunctionLogs, NewFeature_ProgressManagementItems, NewFeature_ProgressManagements, Platform_ChartDicDatas, Platform_ChartGroups, Platform_ChartListDatas, Platform_Charts, Platform_ChartTimeFlowDatas, Platform_DepartmentFavoriteGroups, Platform_DepartmentFavorites, Platform_DepartmentGroupDepartments, Platform_DepartmentGroups, Platform_Departments, Platform_Departments_error, Platform_QuestionFeedbacks, Platform_RegionFavoriteGroups, Platform_RegionFavorites, Platform_Regions, Platform_ReportLedgerNotices, Platform_Routes, Platform_SyncRecords, Platform_UserDepartmentFavoriteGroups, Platform_UserDepartmentRoles, Platform_UserDepartments, Platform_Users, Platform_WebWidgets, Platform_WFDataProcesses, Platform_WFPlanTasks, Platform_WFTaskItems, Platform_WorkToDoRecords, Platform_YbtUsers, Platform_YkzOrganizations, Platform_YkzOrgUnits, RPA_ApplicationDepartments, RPA_Applications, RPA_ApplicationUsers, RPA_AppTaskItems, RPA_AppTasks, RPA_DataFieldMappings, RPA_DataSources, RPA_ProcessFields, RPA_ProcessServices, SrcOrgs, TableInfosToCheck, Temp_Batch, Temp_Ledger_Department_Fill_Fix, Temp_Ledger_Department_Operation_Count, Temp_Ledger_Department_Operation_Count_NewV1, TempOrgs, TempUsers, Test_DEC, TK_BackgroundJobLogs, TK_BackgroundJobs, UserLoginRecords, WF_WorkflowProcesses, WF_WorkflowSchemeAuths, WF_WorkflowSchemeInfoPermissionGrants, WF_WorkflowSchemeInfos, WF_WorkflowSchemes, WF_WorkflowStamps, WF_WorkflowTaskLogs, WF_WorkflowTasks, WF_WorkflowUnits, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 16:02:23.791 +08:00 [INF] 表的迁移顺序: __EFMigrationsHistory, AbpClaimTypes, AbpEditions, AbpFeatureValues, AbpLinkUsers, AbpLocalizationLanguages, AbpLocalizationResources, AbpLocalizationTexts, AbpRoles, AbpOrganizationUnits, AbpOrganizationUnitRoles, AbpPermissionGrants, AbpRoleClaims, AbpSettings, AbpTenants, AbpTenantConnectionStrings, AbpUsers, AbpUserLogins, AbpUserOrganizationUnits, AbpUserRoles, AbpUserRoles_923bf, AbpUserTokens, AbpWebhooksEvents, AbpWebhooksSendAttempts, AbpWebhooksSubscriptions, Airport_AssetLiabilities, Airport_BudgetAccounts, Airport_BudgetOrganizations, Airport_FinanceAuditBatches, Airport_FinanceAuditedDatas, Airport_FinanceProjects, Airport_NoFinanceProjects, Airport_NoFinancialBudgetProjects, Airport_OverheadCostCenters, AppChatGroups, AppGroupChatBlacks, AppGroupMessages, AppNotificationDefinitionGroups, AppNotificationDefinitions, AppPlatformDatas, AppPlatformDataItems, AppPlatformLayouts, AppPlatformMenus, AppPlatformPackages, AppPlatformPackageBlobs, AppPlatformRoleMenus, AppPlatformUserFavoriteMenus, AppPlatformUserMenus, AppPlatformVersion, AppPlatformVersionFile, AppUserChatCards, AppUserChatFriends, AppUserChatGroups, AppUserChatSettings, AppUserGroupCards, AppUserMessages, Bank_Ledger_BaseInfoStatistics, Bank_Ledger_ResettleHelpEducates, Bank_Ledger_RiskDetermines, Bank_Ledger_ServeSentenceAndCriminalInfoStatistics, BigScreenAuditDatas, BSPUser, dept_new_zhong, excelbookuser, excepbook, ExistingTables, fill_user_book, Filling_AreaOrganizationUnits, Filling_CollaborativeReports, Filling_Staff, Filling_PlanTasks, Filling_ReportTasks, Filling_FileInfos, Filling_ReportTaskAreaOrganizationUnits, Filling_ReportTables, Filling_FillingConfigs, Filling_PlanTaskAreaOrganizationUnits, Filling_ReportTableTemplates, Filling_PlanTaskReportTableTemplates, Filling_PlanTaskStaffAreaOrganizationUnits, Filling_PlanTaskStaffs, Filling_ReportMessageInfos, Filling_ReportTableRows, Filling_ReportTaskAreaOrganizationUnitAudits, Filling_ReportTaskAreaOrganizationUnitFillers, Filling_StaffPlanTaskHides, Filling_TableLedgerRuleConfigs, Filling_TableTemplateColumns, Filling_TableTemplateDataRows, Filling_YkzOrgUnits, hg_t_audit_log, IdentityServerApiResources, IdentityServerApiResourceClaims, IdentityServerApiResourceProperties, IdentityServerApiResourceScopes, IdentityServerApiResourceSecrets, IdentityServerApiScopes, IdentityServerApiScopeClaims, IdentityServerApiScopeProperties, IdentityServerClients, IdentityServerClientClaims, IdentityServerClientCorsOrigins, IdentityServerClientGrantTypes, IdentityServerClientIdPRestrictions, IdentityServerClientPostLogoutRedirectUris, IdentityServerClientProperties, IdentityServerClientRedirectUris, IdentityServerClientScopes, IdentityServerClientSecrets, IdentityServerDeviceFlowCodes, IdentityServerIdentityResources, IdentityServerIdentityResourceClaims, IdentityServerIdentityResourceProperties, IdentityServerPersistedGrants, Ledger_AjcBaseInfoStatistics, Ledger_TableFieldGroups, Ledger_DataSources, Ledger_SourceDbTableInfos, Ledger_DbTableFields, Ledger_TableDataSets, Ledger_TableDataSetMappingFields, Ledger_TableFields, Ledger_TableInfos, Platform_Departments_error, Platform_Departments, Ledger_LedgerTypes, Ledger_Ledgers, Ledger_ApiAuxiliaryFillSources, Ledger_ApiAuxiliaryFillingRules, Ledger_ApiAuxiliaryFillings, Ledger_ApiDataSets, Ledger_ApiDataSetMappingFields, Ledger_ApiRequestParameters, Ledger_LedgerDataAnalyses, Ledger_AssociatedConfigs, Ledger_AssociatedSyns, Ledger_AssociatedSynConfigs, Ledger_BaseInfoStatistics, Ledger_BigViewTypicalScenarios, Platform_Users, Ledger_BigViewTypicalScenarioUploadRecords, Ledger_CommonGoalsTasks, Ledger_DataItemsManagements, Ledger_DataItemsUpdateRecords, Ledger_StarEvaluates, Ledger_DeductPoints, Ledger_StarEvaluatePublishes, Ledger_DeductPointsPublishes, Ledger_HistoryEvaluates, Ledger_StarEvaluateRecords, Ledger_DeductPointsRecords, Ledger_Disabilities, Ledger_HierarchicalLedgerDataPushOriginPlans, Ledger_HierarchicalLedgers, Ledger_HierarchicalLedgerTaskItems, Ledger_IndicatorCockpits, Ledger_IndicatorCockpitConfigs, Ledger_LedgerAuditBatches, Ledger_LedgerAuditBatchFlows, Ledger_LedgerAuditBatchFlowNodes, Ledger_LedgerAuditBatchStatistics, Ledger_LedgerAuxiliaryFillings, Ledger_LedgerAuxiliaryFillingConfigs, Ledger_LedgerDataSetAuxiliaryFillings, Ledger_LedgerAuxiliaryFillingPreviews, Ledger_LedgerChangeRecords, Ledger_LedgerChangeRecordDetails, Ledger_LedgerDataComparisonRecords, Ledger_LedgerDataSetAuxiliaryFillingConfigs, Ledger_LedgerDataSyncConfigs, Ledger_LedgerDataSyncTasks, Ledger_LedgerDataSyncFields, Ledger_LedgerDepartmentDataDetailStatistics, Ledger_LedgerDepartmentDataStatistics, Ledger_LedgerDepartments, Ledger_LedgerHierarchicalAuthorizations, Ledger_LedgerPermissionsAuthorizationModes, Ledger_LedgerRunways, Ledger_LedgerRunwayRelations, Ledger_TableQueryConfigs, Ledger_LedgerStatistics, Ledger_LedgerTemplates, Ledger_LedgerUsers, Ledger_LedgerWayAndRelations, Ledger_LlmMessages, Ledger_MyLedgerExportRecords, Ledger_OneVoteRejectPublishes, Ledger_OneVoteRejectRecords, Ledger_OneVoteRejects, Ledger_OnLineAnalysisConfigs, Ledger_OnLineAnalysisEchartConfigs, Ledger_PlanProgressConfigs, Ledger_ResettleHelpEducates, Ledger_RiskDetermines, Ledger_ServeSentenceAndCriminalInfoStatistics, Ledger_SocialAssistances, Ledger_SourceLedgerTypes, Ledger_SourceLedgerFillStatistics, Ledger_SourceLedgerTypeRelations, Ledger_StreetBigDataEchartAnalyses, Ledger_StreetBigDataEchartAnalysisConfigs, Ledger_TableDataExportRecords, Ledger_TableDataImportRecords, Ledger_TableDataSetSourceDbTableInfos, Ledger_TableDataSetJoinConfigs, Ledger_TableDataSetJoinFieldConfigs, Ledger_TableFieldCalculateRules, Ledger_TableFieldClientSettings, Ledger_TableFieldMultiples, Ledger_TableFiledValidateRules, Ledger_TelecomTaskItems, Ledger_TaskChargePeople, NewFeature_AuthorityAudits, NewFeature_AutomatedVerifications, NewFeature_ProgressManagements, NewFeature_ProgressManagementItems, NewFeature_DataProcesses, NewFeature_FunctionLogs, Platform_ChartGroups, Platform_Charts, Platform_ChartDicDatas, Platform_ChartListDatas, Platform_ChartTimeFlowDatas, Platform_DepartmentFavoriteGroups, Platform_DepartmentFavorites, Platform_DepartmentGroups, Platform_DepartmentGroupDepartments, Platform_Regions, Platform_QuestionFeedbacks, Platform_RegionFavoriteGroups, Platform_RegionFavorites, Platform_ReportLedgerNotices, Platform_Routes, Platform_SyncRecords, Platform_UserDepartmentFavoriteGroups, Platform_UserDepartmentRoles, Platform_UserDepartments, Platform_WebWidgets, Platform_WFPlanTasks, Platform_WFTaskItems, Platform_WFDataProcesses, Platform_WorkToDoRecords, Platform_YbtUsers, Platform_YkzOrganizations, Platform_YkzOrgUnits, RPA_Applications, RPA_ApplicationDepartments, RPA_ApplicationUsers, RPA_AppTasks, RPA_AppTaskItems, RPA_DataSources, RPA_DataFieldMappings, RPA_ProcessServices, RPA_ProcessFields, SrcOrgs, TableInfosToCheck, Temp_Batch, Temp_Ledger_Department_Fill_Fix, Temp_Ledger_Department_Operation_Count, Temp_Ledger_Department_Operation_Count_NewV1, TempOrgs, TempUsers, Test_DEC, TK_BackgroundJobLogs, TK_BackgroundJobs, UserLoginRecords, WF_WorkflowTasks, WF_WorkflowSchemeInfos, WF_WorkflowSchemes, WF_WorkflowProcesses, WF_WorkflowSchemeAuths, WF_WorkflowSchemeInfoPermissionGrants, WF_WorkflowStamps, WF_WorkflowTaskLogs, WF_WorkflowUnits, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 16:02:24.463 +08:00 [INF] 表 __EFMigrationsHistory 总行数: 391
2025-06-20 16:02:24.687 +08:00 [INF] 表 AbpClaimTypes 总行数: 35
2025-06-20 16:02:24.774 +08:00 [INF] 表 AbpFeatureValues 总行数: 24
2025-06-20 16:02:24.778 +08:00 [INF] 表 AbpEditions 总行数: 0
2025-06-20 16:02:24.928 +08:00 [INF] 表 AbpLinkUsers 总行数: 0
2025-06-20 16:02:25.096 +08:00 [INF] 表 AbpLocalizationLanguages 总行数: 1
2025-06-20 16:02:25.132 +08:00 [INF] 表 AbpLocalizationResources 总行数: 1
2025-06-20 16:02:25.152 +08:00 [INF] 表 AbpLocalizationTexts 总行数: 20
2025-06-20 16:02:25.362 +08:00 [INF] 表 AbpRoles 总行数: 18
2025-06-20 16:02:25.614 +08:00 [INF] 表 AbpOrganizationUnitRoles 总行数: 0
2025-06-20 16:02:25.614 +08:00 [INF] 表 AbpOrganizationUnits 总行数: 10
2025-06-20 16:02:25.676 +08:00 [INF] 表 AbpPermissionGrants 总行数: 5696
2025-06-20 16:02:25.854 +08:00 [INF] 表 AbpRoleClaims 总行数: 1
2025-06-20 16:02:26.044 +08:00 [INF] 表 AbpSettings 总行数: 13
2025-06-20 16:02:26.098 +08:00 [INF] 表 AbpTenantConnectionStrings 总行数: 0
2025-06-20 16:02:26.112 +08:00 [INF] 表 AbpTenants 总行数: 1
2025-06-20 16:02:26.383 +08:00 [INF] 表 AbpUsers 总行数: 483309
2025-06-20 16:02:26.535 +08:00 [INF] 表 AbpUserLogins 总行数: 269
2025-06-20 16:02:26.536 +08:00 [INF] 表 AbpUserOrganizationUnits 总行数: 3
2025-06-20 16:02:26.697 +08:00 [INF] 表 AbpUserRoles 总行数: 523722
2025-06-20 16:02:26.801 +08:00 [INF] 表 AbpUserRoles_923bf 总行数: 455580
2025-06-20 16:02:27.011 +08:00 [INF] 表 AbpUserTokens 总行数: 0
2025-06-20 16:02:27.045 +08:00 [INF] 表 AbpWebhooksEvents 总行数: 0
2025-06-20 16:02:27.062 +08:00 [INF] 表 AbpWebhooksSendAttempts 总行数: 0
2025-06-20 16:02:27.303 +08:00 [INF] 表 AbpWebhooksSubscriptions 总行数: 1
2025-06-20 16:02:27.498 +08:00 [INF] 表 Airport_AssetLiabilities 总行数: 0
2025-06-20 16:02:27.528 +08:00 [INF] 表 Airport_BudgetAccounts 总行数: 0
2025-06-20 16:02:27.542 +08:00 [INF] 表 Airport_BudgetOrganizations 总行数: 0
2025-06-20 16:02:27.764 +08:00 [INF] 表 Airport_FinanceAuditBatches 总行数: 0
2025-06-20 16:02:28.022 +08:00 [INF] 表 Airport_FinanceAuditedDatas 总行数: 0
2025-06-20 16:02:28.037 +08:00 [INF] 表 Airport_FinanceProjects 总行数: 0
2025-06-20 16:02:28.056 +08:00 [INF] 表 Airport_NoFinanceProjects 总行数: 0
2025-06-20 16:02:28.202 +08:00 [INF] 表 Airport_NoFinancialBudgetProjects 总行数: 0
2025-06-20 16:02:28.450 +08:00 [INF] 表 Airport_OverheadCostCenters 总行数: 0
2025-06-20 16:02:28.657 +08:00 [INF] 表 AppChatGroups 总行数: 0
2025-06-20 16:02:28.942 +08:00 [INF] 表 AppGroupChatBlacks 总行数: 0
2025-06-20 16:02:29.165 +08:00 [INF] 表 AppGroupMessages 总行数: 0
2025-06-20 16:02:29.348 +08:00 [INF] 表 AppNotificationDefinitionGroups 总行数: 0
2025-06-20 16:02:29.522 +08:00 [INF] 表 AppNotificationDefinitions 总行数: 0
2025-06-20 16:02:29.796 +08:00 [INF] 表 AppPlatformDatas 总行数: 3
2025-06-20 16:02:30.044 +08:00 [INF] 表 AppPlatformDataItems 总行数: 27
2025-06-20 16:02:30.303 +08:00 [INF] 表 AppPlatformLayouts 总行数: 2
2025-06-20 16:02:30.529 +08:00 [INF] 表 AppPlatformMenus 总行数: 263
2025-06-20 16:02:30.869 +08:00 [INF] 表 AppPlatformPackages 总行数: 0
2025-06-20 16:02:30.970 +08:00 [INF] 表 AppPlatformPackageBlobs 总行数: 0
2025-06-20 16:02:31.088 +08:00 [INF] 表 AppPlatformRoleMenus 总行数: 585
2025-06-20 16:02:31.382 +08:00 [INF] 表 AppPlatformUserFavoriteMenus 总行数: 0
2025-06-20 16:02:31.397 +08:00 [INF] 表 AppPlatformUserMenus 总行数: 5281
2025-06-20 16:02:33.726 +08:00 [INF] 表 AppPlatformVersion 总行数: 0
2025-06-20 16:02:33.770 +08:00 [INF] 表 AppPlatformVersionFile 总行数: 0
2025-06-20 16:02:33.806 +08:00 [INF] 表 AppUserChatCards 总行数: 49825
2025-06-20 16:03:02.338 +08:00 [INF] 表 AbpAuditLogActions 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:02.374 +08:00 [INF] 表 AbpAuditLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:02.639 +08:00 [INF] 表 AbpEntityChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:02.691 +08:00 [INF] 表 AbpEntityPropertyChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:02.935 +08:00 [INF] 表 AbpSecurityLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:02.970 +08:00 [INF] 表 AbpUserClaims 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:03.220 +08:00 [INF] 表 AppNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:03.233 +08:00 [INF] 表 AppUserNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:03.436 +08:00 [INF] 表 AppUserSubscribes 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:03.464 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 16:03:05.528 +08:00 [INF] 表 AppUserChatFriends 总行数: 0
2025-06-20 16:03:05.543 +08:00 [INF] 表 AppUserChatGroups 总行数: 0
2025-06-20 16:03:05.584 +08:00 [INF] 表 AppUserChatSettings 总行数: 49825
2025-06-20 16:03:25.191 +08:00 [INF] 表 AppUserGroupCards 总行数: 0
2025-06-20 16:03:25.219 +08:00 [INF] 表 AppUserMessages 总行数: 0
2025-06-20 16:03:25.256 +08:00 [INF] 表 Bank_Ledger_BaseInfoStatistics 总行数: 0
2025-06-20 16:03:25.283 +08:00 [INF] 表 Bank_Ledger_ResettleHelpEducates 总行数: 0
2025-06-20 16:03:25.308 +08:00 [INF] 表 Bank_Ledger_RiskDetermines 总行数: 0
2025-06-20 16:03:25.355 +08:00 [INF] 表 Bank_Ledger_ServeSentenceAndCriminalInfoStatistics 总行数: 0
2025-06-20 16:03:25.379 +08:00 [INF] 表 BigScreenAuditDatas 总行数: 39
2025-06-20 16:03:25.547 +08:00 [INF] 表 BSPUser 总行数: 455368
2025-06-20 16:04:18.519 +08:00 [INF] 表 dept_new_zhong 总行数: 194
2025-06-20 16:04:18.677 +08:00 [INF] 表 excelbookuser 总行数: 1858
2025-06-20 16:04:19.106 +08:00 [INF] 表 excepbook 总行数: 92
2025-06-20 16:04:19.235 +08:00 [INF] 表 ExistingTables 总行数: 8073
2025-06-20 16:04:20.565 +08:00 [INF] 表 fill_user_book 总行数: 205200
2025-06-20 16:04:42.204 +08:00 [INF] 表 Filling_AreaOrganizationUnits 总行数: 98986
2025-06-20 16:05:15.020 +08:00 [INF] 表 Filling_CollaborativeReports 总行数: 1111
2025-06-20 16:05:15.830 +08:00 [INF] 表 Filling_Staff 总行数: 483643
2025-06-20 16:05:18.744 +08:00 [INF] 表 Filling_PlanTasks 总行数: 3627
2025-06-20 16:05:24.174 +08:00 [INF] 表 Filling_ReportTasks 总行数: 4883
2025-06-20 16:05:28.363 +08:00 [INF] 表 Filling_FileInfos 总行数: 1570
2025-06-20 16:05:29.656 +08:00 [INF] 表 Filling_ReportTaskAreaOrganizationUnits 总行数: 1911
2025-06-20 16:05:32.292 +08:00 [INF] 表 Filling_ReportTables 总行数: 1997
2025-06-20 16:05:34.739 +08:00 [INF] 表 Filling_FillingConfigs 总行数: 33
2025-06-20 16:05:34.929 +08:00 [INF] 表 Filling_PlanTaskAreaOrganizationUnits 总行数: 14241
2025-06-20 16:05:37.390 +08:00 [INF] 表 Filling_ReportTableTemplates 总行数: 4819
2025-06-20 16:05:55.086 +08:00 [INF] 表 Filling_PlanTaskReportTableTemplates 总行数: 3832
2025-06-20 16:05:55.848 +08:00 [INF] 表 Filling_PlanTaskStaffAreaOrganizationUnits 总行数: 217
2025-06-20 16:05:56.003 +08:00 [INF] 表 Filling_PlanTaskStaffs 总行数: 1913
2025-06-20 16:05:56.556 +08:00 [INF] 表 Filling_ReportMessageInfos 总行数: 0
2025-06-20 16:05:56.611 +08:00 [INF] 表 Filling_ReportTableRows 总行数: 2042
2025-06-20 16:05:57.466 +08:00 [INF] 表 Filling_ReportTaskAreaOrganizationUnitAudits 总行数: 957
2025-06-20 16:05:57.688 +08:00 [INF] 表 Filling_ReportTaskAreaOrganizationUnitFillers 总行数: 1067
2025-06-20 16:05:57.801 +08:00 [INF] 表 Filling_StaffPlanTaskHides 总行数: 865
2025-06-20 16:05:57.996 +08:00 [INF] 表 Filling_TableLedgerRuleConfigs 总行数: 0
2025-06-20 16:05:58.026 +08:00 [INF] 表 Filling_TableTemplateColumns 总行数: 175494
2025-06-20 16:05:58.139 +08:00 [INF] 表 Filling_TableTemplateDataRows 总行数: 370
2025-06-20 16:05:58.328 +08:00 [INF] 表 Filling_YkzOrgUnits 总行数: 97762
2025-06-20 16:07:09.526 +08:00 [INF] 表 IdentityServerApiResources 总行数: 3
2025-06-20 16:07:09.619 +08:00 [INF] 表 IdentityServerApiResourceClaims 总行数: 35
2025-06-20 16:07:09.717 +08:00 [INF] 表 IdentityServerApiResourceProperties 总行数: 0
2025-06-20 16:07:09.728 +08:00 [INF] 表 IdentityServerApiResourceScopes 总行数: 2
2025-06-20 16:07:09.830 +08:00 [INF] 表 IdentityServerApiResourceSecrets 总行数: 0
2025-06-20 16:07:09.842 +08:00 [INF] 表 IdentityServerApiScopes 总行数: 3
2025-06-20 16:07:09.999 +08:00 [INF] 表 IdentityServerApiScopeClaims 总行数: 0
2025-06-20 16:07:10.014 +08:00 [INF] 表 IdentityServerApiScopeProperties 总行数: 0
2025-06-20 16:07:10.026 +08:00 [INF] 表 IdentityServerClients 总行数: 11
2025-06-20 16:07:10.141 +08:00 [INF] 表 IdentityServerClientClaims 总行数: 0
2025-06-20 16:07:10.154 +08:00 [INF] 表 IdentityServerClientCorsOrigins 总行数: 1
2025-06-20 16:07:10.247 +08:00 [INF] 表 IdentityServerClientGrantTypes 总行数: 19
2025-06-20 16:07:10.338 +08:00 [INF] 表 IdentityServerClientIdPRestrictions 总行数: 0
2025-06-20 16:07:10.350 +08:00 [INF] 表 IdentityServerClientPostLogoutRedirectUris 总行数: 6
2025-06-20 16:07:10.450 +08:00 [INF] 表 IdentityServerClientProperties 总行数: 0
2025-06-20 16:07:10.464 +08:00 [INF] 表 IdentityServerClientRedirectUris 总行数: 12
2025-06-20 16:07:10.568 +08:00 [INF] 表 IdentityServerClientScopes 总行数: 58
2025-06-20 16:07:10.664 +08:00 [INF] 表 IdentityServerClientSecrets 总行数: 13
2025-06-20 16:07:10.768 +08:00 [INF] 表 IdentityServerDeviceFlowCodes 总行数: 0
2025-06-20 16:07:10.787 +08:00 [INF] 表 IdentityServerIdentityResources 总行数: 8
2025-06-20 16:07:10.888 +08:00 [INF] 表 IdentityServerIdentityResourceClaims 总行数: 34
2025-06-20 16:07:10.986 +08:00 [INF] 表 IdentityServerIdentityResourceProperties 总行数: 0
2025-06-20 16:07:11.028 +08:00 [INF] 表 IdentityServerPersistedGrants 总行数: 1023
2025-06-20 16:07:12.122 +08:00 [INF] 表 Ledger_AjcBaseInfoStatistics 总行数: 0
2025-06-20 16:07:12.135 +08:00 [INF] 表 Ledger_TableFieldGroups 总行数: 7017
2025-06-20 16:07:13.893 +08:00 [INF] 表 Ledger_DataSources 总行数: 26
2025-06-20 16:07:14.025 +08:00 [INF] 表 Ledger_SourceDbTableInfos 总行数: 926
2025-06-20 16:07:14.480 +08:00 [INF] 表 Ledger_DbTableFields 总行数: 23219
2025-06-20 16:07:18.540 +08:00 [ERR] 获取表 hg_t_audit_log 行数失败
System.Exception: 获取表hg_t_audit_log的总行数在3次尝试后操作仍然失败。最后一次错误: XX000: permission denied 
 ---> Nhgdb.HighgoException (0x80004005): XX000: permission denied 
   at Nhgdb.Internal.NhgdbConnector.<ReadMessage>g__ReadMessageLong|224_0(NhgdbConnector connector, Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at Nhgdb.NhgdbDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Nhgdb.NhgdbCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
   at Nhgdb.NhgdbCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
   at Nhgdb.NhgdbCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Dapper.SqlMapper.QueryRowAsync[T](IDbConnection cnn, Row row, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 489
   at TryCode.DatabaseMigration.PostgreSQL.PostgresDataReader.<>c__DisplayClass11_0.<<GetTableRowCountAsync>b__0>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.PostgreSQL/PostgresDataReader.cs:line 255
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Core.DataReaders.DataReaderBase.ExecuteWithRetryAsync[T](Func`1 operation, String operationName, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Core/DataReaders/DataReaderBase.cs:line 41
  Exception data:
    Severity: ERROR
    SqlState: XX000
    MessageText: permission denied 
    File: hgaudit.c
    Line: 3178
    Routine: check_audit_table
   --- End of inner exception stack trace ---
   at TryCode.DatabaseMigration.Core.DataReaders.DataReaderBase.ExecuteWithRetryAsync[T](Func`1 operation, String operationName, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Core/DataReaders/DataReaderBase.cs:line 50
   at TryCode.DatabaseMigration.PostgreSQL.PostgresDataReader.GetTableRowCountAsync(String tableName, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.PostgreSQL/PostgresDataReader.cs:line 249
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 304
2025-06-20 16:07:18.633 +08:00 [INF] 表 Ledger_TableDataSets 总行数: 145
2025-06-20 16:07:18.859 +08:00 [INF] 表 Ledger_TableDataSetMappingFields 总行数: 3575
2025-06-20 16:07:20.113 +08:00 [INF] 表 Ledger_TableInfos 总行数: 9037
2025-06-20 16:07:20.175 +08:00 [INF] 表 Ledger_TableFields 总行数: 290271
2025-06-20 16:07:26.046 +08:00 [INF] 表 Platform_Departments_error 总行数: 219108
2025-06-20 16:09:49.980 +08:00 [INF] 表 Platform_Departments 总行数: 258980
2025-06-20 16:10:40.484 +08:00 [INF] 表 Ledger_LedgerTypes 总行数: 1259
2025-06-20 16:10:41.221 +08:00 [INF] 表 Ledger_Ledgers 总行数: 8404
2025-06-20 16:11:01.695 +08:00 [INF] 表 Ledger_ApiAuxiliaryFillSources 总行数: 2
2025-06-20 16:11:01.800 +08:00 [INF] 表 Ledger_ApiAuxiliaryFillingRules 总行数: 179
2025-06-20 16:11:01.991 +08:00 [INF] 表 Ledger_ApiAuxiliaryFillings 总行数: 53
2025-06-20 16:11:02.139 +08:00 [INF] 表 Ledger_ApiDataSets 总行数: 6
2025-06-20 16:11:02.300 +08:00 [INF] 表 Ledger_ApiDataSetMappingFields 总行数: 278
2025-06-20 16:11:02.545 +08:00 [INF] 表 Ledger_ApiRequestParameters 总行数: 6
2025-06-20 16:11:02.664 +08:00 [INF] 表 Ledger_LedgerDataAnalyses 总行数: 539
2025-06-20 16:11:03.099 +08:00 [INF] 表 Ledger_AssociatedConfigs 总行数: 215
2025-06-20 16:11:04.322 +08:00 [INF] 表 Ledger_AssociatedSyns 总行数: 43
2025-06-20 16:11:04.514 +08:00 [INF] 表 Ledger_AssociatedSynConfigs 总行数: 296
2025-06-20 16:11:04.784 +08:00 [INF] 表 Ledger_BaseInfoStatistics 总行数: 1072
2025-06-20 16:11:06.403 +08:00 [INF] 表 Ledger_BigViewTypicalScenarios 总行数: 44
2025-06-20 16:11:06.598 +08:00 [INF] 表 Platform_Users 总行数: 483649
2025-06-20 16:11:07.659 +08:00 [INF] 表 Ledger_BigViewTypicalScenarioUploadRecords 总行数: 0
2025-06-20 16:11:07.693 +08:00 [INF] 表 Ledger_CommonGoalsTasks 总行数: 0
2025-06-20 16:11:07.711 +08:00 [INF] 表 Ledger_DataItemsManagements 总行数: 0
2025-06-20 16:11:07.728 +08:00 [INF] 表 Ledger_DataItemsUpdateRecords 总行数: 0
2025-06-20 16:11:07.815 +08:00 [INF] 表 Ledger_StarEvaluates 总行数: 41
2025-06-20 16:11:08.052 +08:00 [INF] 表 Ledger_DeductPoints 总行数: 11
2025-06-20 16:11:08.194 +08:00 [INF] 表 Ledger_StarEvaluatePublishes 总行数: 779
2025-06-20 16:11:09.511 +08:00 [INF] 表 Ledger_DeductPointsPublishes 总行数: 61
2025-06-20 16:11:09.661 +08:00 [INF] 表 Ledger_HistoryEvaluates 总行数: 0
2025-06-20 16:11:09.682 +08:00 [INF] 表 Ledger_StarEvaluateRecords 总行数: 0
2025-06-20 16:11:09.695 +08:00 [INF] 表 Ledger_DeductPointsRecords 总行数: 0
2025-06-20 16:11:09.726 +08:00 [INF] 表 Ledger_Disabilities 总行数: 0
2025-06-20 16:11:09.744 +08:00 [INF] 表 Ledger_HierarchicalLedgerDataPushOriginPlans 总行数: 0
2025-06-20 16:11:09.777 +08:00 [INF] 表 Ledger_HierarchicalLedgers 总行数: 3
2025-06-20 16:11:09.944 +08:00 [INF] 表 Ledger_HierarchicalLedgerTaskItems 总行数: 2
2025-06-20 16:11:10.038 +08:00 [INF] 表 Ledger_IndicatorCockpitConfigs 总行数: 35
2025-06-20 16:11:10.047 +08:00 [INF] 表 Ledger_IndicatorCockpits 总行数: 45623
2025-06-20 16:11:10.253 +08:00 [INF] 表 Ledger_LedgerAuditBatches 总行数: 73785
2025-06-20 16:12:12.324 +08:00 [INF] 表 Ledger_LedgerAuditBatchFlows 总行数: 169
2025-06-20 16:12:12.585 +08:00 [INF] 表 Ledger_LedgerAuditBatchFlowNodes 总行数: 397
2025-06-20 16:12:12.870 +08:00 [INF] 表 Ledger_LedgerAuditBatchStatistics 总行数: 67421
2025-06-20 16:12:22.936 +08:00 [INF] 表 Ledger_LedgerAuxiliaryFillings 总行数: 51
2025-06-20 16:12:23.113 +08:00 [INF] 表 Ledger_LedgerAuxiliaryFillingConfigs 总行数: 172
2025-06-20 16:12:23.259 +08:00 [INF] 表 Ledger_LedgerDataSetAuxiliaryFillings 总行数: 2
2025-06-20 16:12:23.346 +08:00 [INF] 表 Ledger_LedgerAuxiliaryFillingPreviews 总行数: 159
2025-06-20 16:12:23.500 +08:00 [INF] 表 Ledger_LedgerChangeRecords 总行数: 73
2025-06-20 16:12:23.639 +08:00 [INF] 表 Ledger_LedgerChangeRecordDetails 总行数: 135
2025-06-20 16:12:23.909 +08:00 [INF] 表 Ledger_LedgerDataComparisonRecords 总行数: 38
2025-06-20 16:12:24.025 +08:00 [INF] 表 Ledger_LedgerDataSetAuxiliaryFillingConfigs 总行数: 2
2025-06-20 16:12:24.198 +08:00 [INF] 表 Ledger_LedgerDataSyncConfigs 总行数: 1
2025-06-20 16:12:24.307 +08:00 [INF] 表 Ledger_LedgerDataSyncTasks 总行数: 1
2025-06-20 16:12:24.417 +08:00 [INF] 表 Ledger_LedgerDataSyncFields 总行数: 1
2025-06-20 16:12:24.540 +08:00 [INF] 表 Ledger_LedgerDepartmentDataDetailStatistics 总行数: 86453
2025-06-20 16:12:25.813 +08:00 [INF] 表 Ledger_LedgerDepartmentDataStatistics 总行数: 1388
2025-06-20 16:12:26.301 +08:00 [INF] 表 Ledger_LedgerDepartments 总行数: 168892
2025-06-20 16:12:39.491 +08:00 [INF] 表 Ledger_LedgerHierarchicalAuthorizations 总行数: 1
2025-06-20 16:12:39.713 +08:00 [INF] 表 Ledger_LedgerPermissionsAuthorizationModes 总行数: 1263126
2025-06-20 16:13:02.214 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 55000，总行数: 168892
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11ce00-2ec2-fcfe-fe70-9f07715b96c5-3a0ef8ec-c674-5e7e-4b1b-96f1afc806e3' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 118
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 16:13:02.222 +08:00 [ERR] 检测到主键冲突，这可能是由于ORDER BY不稳定导致的重复数据读取。请检查源数据或考虑重新开始迁移
2025-06-20 16:13:02.224 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据时出错: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。 详细错误: Duplicate entry '3a11ce00-2ec2-fcfe-fe70-9f07715b96c5-3a0ef8ec-c674-5e7e-4b1b-96f1afc806e3' for key 'PRIMARY'
2025-06-20 16:43:39.558 +08:00 [INF] 正在生成迁移报告...
2025-06-20 16:43:39.565 +08:00 [INF] 迁移报告已生成: migration_report.txt
2025-06-20 16:43:39.566 +08:00 [ERR] 未知错误: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
System.InvalidOperationException: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
 ---> MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11ce00-2ec2-fcfe-fe70-9f07715b96c5-3a0ef8ec-c674-5e7e-4b1b-96f1afc806e3' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 118
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
   --- End of inner exception stack trace ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 401
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 451
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 649
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 673
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 683
   at TryCode.DatabaseMigration.CLI.Commands.MigrationCommand.ExecuteMigrationCommandAsync() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.CLI/Commands/MigrationCommand.cs:line 199
2025-06-20 16:43:39.572 +08:00 [INF] 正在关闭日志记录器...
