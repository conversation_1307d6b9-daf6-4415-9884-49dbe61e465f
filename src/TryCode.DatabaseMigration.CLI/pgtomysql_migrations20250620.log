2025-06-20 15:18:55.387 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql.config.json
2025-06-20 15:21:40.161 +08:00 [INF] 获取到285个表
2025-06-20 15:21:40.171 +08:00 [INF] 正在更新断点文件中的排除表信息...
2025-06-20 15:21:40.194 +08:00 [INF] 已将表 AbpAuditLogActions 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.195 +08:00 [INF] 已将表 AbpAuditLogs 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.195 +08:00 [INF] 已将表 AbpEntityChanges 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.196 +08:00 [INF] 已将表 AbpEntityPropertyChanges 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.196 +08:00 [INF] 已将表 AbpSecurityLogs 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.196 +08:00 [INF] 已将表 AbpUserClaims 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.197 +08:00 [INF] 已将表 AppNotifications 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.197 +08:00 [INF] 已将表 AppUserNotifications 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.198 +08:00 [INF] 已将表 AppUserSubscribes 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.199 +08:00 [INF] 已将表 Ledger_LedgerDataUpdateLogs 在断点文件中标记为仅创建结构
2025-06-20 15:21:40.203 +08:00 [INF] 分区表处理完成，原始表数: 275，处理后表数: 275
2025-06-20 15:21:40.203 +08:00 [INF] 将为以下排除的表创建表结构（不迁移数据）: AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 15:21:40.203 +08:00 [INF] 过滤后剩余285个表
2025-06-20 15:21:40.203 +08:00 [INF] 使用并行度: 4
2025-06-20 15:21:40.213 +08:00 [INF] 开始处理以下表的迁移: __EFMigrationsHistory, AbpClaimTypes, AbpEditions, AbpFeatureValues, AbpLinkUsers, AbpLocalizationLanguages, AbpLocalizationResources, AbpLocalizationTexts, AbpOrganizationUnitRoles, AbpOrganizationUnits, AbpPermissionGrants, AbpRoleClaims, AbpRoles, AbpSettings, AbpTenantConnectionStrings, AbpTenants, AbpUserLogins, AbpUserOrganizationUnits, AbpUserRoles, AbpUserRoles_923bf, AbpUsers, AbpUserTokens, AbpWebhooksEvents, AbpWebhooksSendAttempts, AbpWebhooksSubscriptions, Airport_AssetLiabilities, Airport_BudgetAccounts, Airport_BudgetOrganizations, Airport_FinanceAuditBatches, Airport_FinanceAuditedDatas, Airport_FinanceProjects, Airport_NoFinanceProjects, Airport_NoFinancialBudgetProjects, Airport_OverheadCostCenters, AppChatGroups, AppGroupChatBlacks, AppGroupMessages, AppNotificationDefinitionGroups, AppNotificationDefinitions, AppPlatformDataItems, AppPlatformDatas, AppPlatformLayouts, AppPlatformMenus, AppPlatformPackageBlobs, AppPlatformPackages, AppPlatformRoleMenus, AppPlatformUserFavoriteMenus, AppPlatformUserMenus, AppPlatformVersion, AppPlatformVersionFile, AppUserChatCards, AppUserChatFriends, AppUserChatGroups, AppUserChatSettings, AppUserGroupCards, AppUserMessages, Bank_Ledger_BaseInfoStatistics, Bank_Ledger_ResettleHelpEducates, Bank_Ledger_RiskDetermines, Bank_Ledger_ServeSentenceAndCriminalInfoStatistics, BigScreenAuditDatas, BSPUser, dept_new_zhong, excelbookuser, excepbook, ExistingTables, fill_user_book, Filling_AreaOrganizationUnits, Filling_CollaborativeReports, Filling_FileInfos, Filling_FillingConfigs, Filling_PlanTaskAreaOrganizationUnits, Filling_PlanTaskReportTableTemplates, Filling_PlanTasks, Filling_PlanTaskStaffAreaOrganizationUnits, Filling_PlanTaskStaffs, Filling_ReportMessageInfos, Filling_ReportTableRows, Filling_ReportTables, Filling_ReportTableTemplates, Filling_ReportTaskAreaOrganizationUnitAudits, Filling_ReportTaskAreaOrganizationUnitFillers, Filling_ReportTaskAreaOrganizationUnits, Filling_ReportTasks, Filling_Staff, Filling_StaffPlanTaskHides, Filling_TableLedgerRuleConfigs, Filling_TableTemplateColumns, Filling_TableTemplateDataRows, Filling_YkzOrgUnits, hg_t_audit_log, IdentityServerApiResourceClaims, IdentityServerApiResourceProperties, IdentityServerApiResources, IdentityServerApiResourceScopes, IdentityServerApiResourceSecrets, IdentityServerApiScopeClaims, IdentityServerApiScopeProperties, IdentityServerApiScopes, IdentityServerClientClaims, IdentityServerClientCorsOrigins, IdentityServerClientGrantTypes, IdentityServerClientIdPRestrictions, IdentityServerClientPostLogoutRedirectUris, IdentityServerClientProperties, IdentityServerClientRedirectUris, IdentityServerClients, IdentityServerClientScopes, IdentityServerClientSecrets, IdentityServerDeviceFlowCodes, IdentityServerIdentityResourceClaims, IdentityServerIdentityResourceProperties, IdentityServerIdentityResources, IdentityServerPersistedGrants, Ledger_AjcBaseInfoStatistics, Ledger_ApiAuxiliaryFillingRules, Ledger_ApiAuxiliaryFillings, Ledger_ApiAuxiliaryFillSources, Ledger_ApiDataSetMappingFields, Ledger_ApiDataSets, Ledger_ApiRequestParameters, Ledger_AssociatedConfigs, Ledger_AssociatedSynConfigs, Ledger_AssociatedSyns, Ledger_BaseInfoStatistics, Ledger_BigViewTypicalScenarios, Ledger_BigViewTypicalScenarioUploadRecords, Ledger_CommonGoalsTasks, Ledger_DataItemsManagements, Ledger_DataItemsUpdateRecords, Ledger_DataSources, Ledger_DbTableFields, Ledger_DeductPoints, Ledger_DeductPointsPublishes, Ledger_DeductPointsRecords, Ledger_Disabilities, Ledger_HierarchicalLedgerDataPushOriginPlans, Ledger_HierarchicalLedgers, Ledger_HierarchicalLedgerTaskItems, Ledger_HistoryEvaluates, Ledger_IndicatorCockpitConfigs, Ledger_IndicatorCockpits, Ledger_LedgerAuditBatches, Ledger_LedgerAuditBatchFlowNodes, Ledger_LedgerAuditBatchFlows, Ledger_LedgerAuditBatchStatistics, Ledger_LedgerAuxiliaryFillingConfigs, Ledger_LedgerAuxiliaryFillingPreviews, Ledger_LedgerAuxiliaryFillings, Ledger_LedgerChangeRecordDetails, Ledger_LedgerChangeRecords, Ledger_LedgerDataAnalyses, Ledger_LedgerDataComparisonRecords, Ledger_LedgerDataSetAuxiliaryFillingConfigs, Ledger_LedgerDataSetAuxiliaryFillings, Ledger_LedgerDataSyncConfigs, Ledger_LedgerDataSyncFields, Ledger_LedgerDataSyncTasks, Ledger_LedgerDepartmentDataDetailStatistics, Ledger_LedgerDepartmentDataStatistics, Ledger_LedgerDepartments, Ledger_LedgerHierarchicalAuthorizations, Ledger_LedgerPermissionsAuthorizationModes, Ledger_LedgerRunwayRelations, Ledger_LedgerRunways, Ledger_Ledgers, Ledger_LedgerStatistics, Ledger_LedgerTemplates, Ledger_LedgerTypes, Ledger_LedgerUsers, Ledger_LedgerWayAndRelations, Ledger_LlmMessages, Ledger_MyLedgerExportRecords, Ledger_OneVoteRejectPublishes, Ledger_OneVoteRejectRecords, Ledger_OneVoteRejects, Ledger_OnLineAnalysisConfigs, Ledger_OnLineAnalysisEchartConfigs, Ledger_PlanProgressConfigs, Ledger_ResettleHelpEducates, Ledger_RiskDetermines, Ledger_ServeSentenceAndCriminalInfoStatistics, Ledger_SocialAssistances, Ledger_SourceDbTableInfos, Ledger_SourceLedgerFillStatistics, Ledger_SourceLedgerTypeRelations, Ledger_SourceLedgerTypes, Ledger_StarEvaluatePublishes, Ledger_StarEvaluateRecords, Ledger_StarEvaluates, Ledger_StreetBigDataEchartAnalyses, Ledger_StreetBigDataEchartAnalysisConfigs, Ledger_TableDataExportRecords, Ledger_TableDataImportRecords, Ledger_TableDataSetJoinConfigs, Ledger_TableDataSetJoinFieldConfigs, Ledger_TableDataSetMappingFields, Ledger_TableDataSets, Ledger_TableDataSetSourceDbTableInfos, Ledger_TableFieldCalculateRules, Ledger_TableFieldClientSettings, Ledger_TableFieldGroups, Ledger_TableFieldMultiples, Ledger_TableFields, Ledger_TableFiledValidateRules, Ledger_TableInfos, Ledger_TableQueryConfigs, Ledger_TaskChargePeople, Ledger_TelecomTaskItems, NewFeature_AuthorityAudits, NewFeature_AutomatedVerifications, NewFeature_DataProcesses, NewFeature_FunctionLogs, NewFeature_ProgressManagementItems, NewFeature_ProgressManagements, Platform_ChartDicDatas, Platform_ChartGroups, Platform_ChartListDatas, Platform_Charts, Platform_ChartTimeFlowDatas, Platform_DepartmentFavoriteGroups, Platform_DepartmentFavorites, Platform_DepartmentGroupDepartments, Platform_DepartmentGroups, Platform_Departments, Platform_Departments_error, Platform_QuestionFeedbacks, Platform_RegionFavoriteGroups, Platform_RegionFavorites, Platform_Regions, Platform_ReportLedgerNotices, Platform_Routes, Platform_SyncRecords, Platform_UserDepartmentFavoriteGroups, Platform_UserDepartmentRoles, Platform_UserDepartments, Platform_Users, Platform_WebWidgets, Platform_WFDataProcesses, Platform_WFPlanTasks, Platform_WFTaskItems, Platform_WorkToDoRecords, Platform_YbtUsers, Platform_YkzOrganizations, Platform_YkzOrgUnits, RPA_ApplicationDepartments, RPA_Applications, RPA_ApplicationUsers, RPA_AppTaskItems, RPA_AppTasks, RPA_DataFieldMappings, RPA_DataSources, RPA_ProcessFields, RPA_ProcessServices, SrcOrgs, TableInfosToCheck, Temp_Batch, Temp_Ledger_Department_Fill_Fix, Temp_Ledger_Department_Operation_Count, Temp_Ledger_Department_Operation_Count_NewV1, TempOrgs, TempUsers, Test_DEC, TK_BackgroundJobLogs, TK_BackgroundJobs, UserLoginRecords, WF_WorkflowProcesses, WF_WorkflowSchemeAuths, WF_WorkflowSchemeInfoPermissionGrants, WF_WorkflowSchemeInfos, WF_WorkflowSchemes, WF_WorkflowStamps, WF_WorkflowTaskLogs, WF_WorkflowTasks, WF_WorkflowUnits, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 15:21:40.214 +08:00 [INF] 表的迁移顺序: __EFMigrationsHistory, AbpClaimTypes, AbpEditions, AbpFeatureValues, AbpLinkUsers, AbpLocalizationLanguages, AbpLocalizationResources, AbpLocalizationTexts, AbpRoles, AbpOrganizationUnits, AbpOrganizationUnitRoles, AbpPermissionGrants, AbpRoleClaims, AbpSettings, AbpTenants, AbpTenantConnectionStrings, AbpUsers, AbpUserLogins, AbpUserOrganizationUnits, AbpUserRoles, AbpUserRoles_923bf, AbpUserTokens, AbpWebhooksEvents, AbpWebhooksSendAttempts, AbpWebhooksSubscriptions, Airport_AssetLiabilities, Airport_BudgetAccounts, Airport_BudgetOrganizations, Airport_FinanceAuditBatches, Airport_FinanceAuditedDatas, Airport_FinanceProjects, Airport_NoFinanceProjects, Airport_NoFinancialBudgetProjects, Airport_OverheadCostCenters, AppChatGroups, AppGroupChatBlacks, AppGroupMessages, AppNotificationDefinitionGroups, AppNotificationDefinitions, AppPlatformDatas, AppPlatformDataItems, AppPlatformLayouts, AppPlatformMenus, AppPlatformPackages, AppPlatformPackageBlobs, AppPlatformRoleMenus, AppPlatformUserFavoriteMenus, AppPlatformUserMenus, AppPlatformVersion, AppPlatformVersionFile, AppUserChatCards, AppUserChatFriends, AppUserChatGroups, AppUserChatSettings, AppUserGroupCards, AppUserMessages, Bank_Ledger_BaseInfoStatistics, Bank_Ledger_ResettleHelpEducates, Bank_Ledger_RiskDetermines, Bank_Ledger_ServeSentenceAndCriminalInfoStatistics, BigScreenAuditDatas, BSPUser, dept_new_zhong, excelbookuser, excepbook, ExistingTables, fill_user_book, Filling_AreaOrganizationUnits, Filling_CollaborativeReports, Filling_Staff, Filling_PlanTasks, Filling_ReportTasks, Filling_FileInfos, Filling_ReportTaskAreaOrganizationUnits, Filling_ReportTables, Filling_FillingConfigs, Filling_PlanTaskAreaOrganizationUnits, Filling_ReportTableTemplates, Filling_PlanTaskReportTableTemplates, Filling_PlanTaskStaffAreaOrganizationUnits, Filling_PlanTaskStaffs, Filling_ReportMessageInfos, Filling_ReportTableRows, Filling_ReportTaskAreaOrganizationUnitAudits, Filling_ReportTaskAreaOrganizationUnitFillers, Filling_StaffPlanTaskHides, Filling_TableLedgerRuleConfigs, Filling_TableTemplateColumns, Filling_TableTemplateDataRows, Filling_YkzOrgUnits, hg_t_audit_log, IdentityServerApiResources, IdentityServerApiResourceClaims, IdentityServerApiResourceProperties, IdentityServerApiResourceScopes, IdentityServerApiResourceSecrets, IdentityServerApiScopes, IdentityServerApiScopeClaims, IdentityServerApiScopeProperties, IdentityServerClients, IdentityServerClientClaims, IdentityServerClientCorsOrigins, IdentityServerClientGrantTypes, IdentityServerClientIdPRestrictions, IdentityServerClientPostLogoutRedirectUris, IdentityServerClientProperties, IdentityServerClientRedirectUris, IdentityServerClientScopes, IdentityServerClientSecrets, IdentityServerDeviceFlowCodes, IdentityServerIdentityResources, IdentityServerIdentityResourceClaims, IdentityServerIdentityResourceProperties, IdentityServerPersistedGrants, Ledger_AjcBaseInfoStatistics, Ledger_TableFieldGroups, Ledger_DataSources, Ledger_SourceDbTableInfos, Ledger_DbTableFields, Ledger_TableDataSets, Ledger_TableDataSetMappingFields, Ledger_TableFields, Ledger_TableInfos, Platform_Departments_error, Platform_Departments, Ledger_LedgerTypes, Ledger_Ledgers, Ledger_ApiAuxiliaryFillSources, Ledger_ApiAuxiliaryFillingRules, Ledger_ApiAuxiliaryFillings, Ledger_ApiDataSets, Ledger_ApiDataSetMappingFields, Ledger_ApiRequestParameters, Ledger_LedgerDataAnalyses, Ledger_AssociatedConfigs, Ledger_AssociatedSyns, Ledger_AssociatedSynConfigs, Ledger_BaseInfoStatistics, Ledger_BigViewTypicalScenarios, Platform_Users, Ledger_BigViewTypicalScenarioUploadRecords, Ledger_CommonGoalsTasks, Ledger_DataItemsManagements, Ledger_DataItemsUpdateRecords, Ledger_StarEvaluates, Ledger_DeductPoints, Ledger_StarEvaluatePublishes, Ledger_DeductPointsPublishes, Ledger_HistoryEvaluates, Ledger_StarEvaluateRecords, Ledger_DeductPointsRecords, Ledger_Disabilities, Ledger_HierarchicalLedgerDataPushOriginPlans, Ledger_HierarchicalLedgers, Ledger_HierarchicalLedgerTaskItems, Ledger_IndicatorCockpits, Ledger_IndicatorCockpitConfigs, Ledger_LedgerAuditBatches, Ledger_LedgerAuditBatchFlows, Ledger_LedgerAuditBatchFlowNodes, Ledger_LedgerAuditBatchStatistics, Ledger_LedgerAuxiliaryFillings, Ledger_LedgerAuxiliaryFillingConfigs, Ledger_LedgerDataSetAuxiliaryFillings, Ledger_LedgerAuxiliaryFillingPreviews, Ledger_LedgerChangeRecords, Ledger_LedgerChangeRecordDetails, Ledger_LedgerDataComparisonRecords, Ledger_LedgerDataSetAuxiliaryFillingConfigs, Ledger_LedgerDataSyncConfigs, Ledger_LedgerDataSyncTasks, Ledger_LedgerDataSyncFields, Ledger_LedgerDepartmentDataDetailStatistics, Ledger_LedgerDepartmentDataStatistics, Ledger_LedgerDepartments, Ledger_LedgerHierarchicalAuthorizations, Ledger_LedgerPermissionsAuthorizationModes, Ledger_LedgerRunways, Ledger_LedgerRunwayRelations, Ledger_TableQueryConfigs, Ledger_LedgerStatistics, Ledger_LedgerTemplates, Ledger_LedgerUsers, Ledger_LedgerWayAndRelations, Ledger_LlmMessages, Ledger_MyLedgerExportRecords, Ledger_OneVoteRejectPublishes, Ledger_OneVoteRejectRecords, Ledger_OneVoteRejects, Ledger_OnLineAnalysisConfigs, Ledger_OnLineAnalysisEchartConfigs, Ledger_PlanProgressConfigs, Ledger_ResettleHelpEducates, Ledger_RiskDetermines, Ledger_ServeSentenceAndCriminalInfoStatistics, Ledger_SocialAssistances, Ledger_SourceLedgerTypes, Ledger_SourceLedgerFillStatistics, Ledger_SourceLedgerTypeRelations, Ledger_StreetBigDataEchartAnalyses, Ledger_StreetBigDataEchartAnalysisConfigs, Ledger_TableDataExportRecords, Ledger_TableDataImportRecords, Ledger_TableDataSetSourceDbTableInfos, Ledger_TableDataSetJoinConfigs, Ledger_TableDataSetJoinFieldConfigs, Ledger_TableFieldCalculateRules, Ledger_TableFieldClientSettings, Ledger_TableFieldMultiples, Ledger_TableFiledValidateRules, Ledger_TelecomTaskItems, Ledger_TaskChargePeople, NewFeature_AuthorityAudits, NewFeature_AutomatedVerifications, NewFeature_ProgressManagements, NewFeature_ProgressManagementItems, NewFeature_DataProcesses, NewFeature_FunctionLogs, Platform_ChartGroups, Platform_Charts, Platform_ChartDicDatas, Platform_ChartListDatas, Platform_ChartTimeFlowDatas, Platform_DepartmentFavoriteGroups, Platform_DepartmentFavorites, Platform_DepartmentGroups, Platform_DepartmentGroupDepartments, Platform_Regions, Platform_QuestionFeedbacks, Platform_RegionFavoriteGroups, Platform_RegionFavorites, Platform_ReportLedgerNotices, Platform_Routes, Platform_SyncRecords, Platform_UserDepartmentFavoriteGroups, Platform_UserDepartmentRoles, Platform_UserDepartments, Platform_WebWidgets, Platform_WFPlanTasks, Platform_WFTaskItems, Platform_WFDataProcesses, Platform_WorkToDoRecords, Platform_YbtUsers, Platform_YkzOrganizations, Platform_YkzOrgUnits, RPA_Applications, RPA_ApplicationDepartments, RPA_ApplicationUsers, RPA_AppTasks, RPA_AppTaskItems, RPA_DataSources, RPA_DataFieldMappings, RPA_ProcessServices, RPA_ProcessFields, SrcOrgs, TableInfosToCheck, Temp_Batch, Temp_Ledger_Department_Fill_Fix, Temp_Ledger_Department_Operation_Count, Temp_Ledger_Department_Operation_Count_NewV1, TempOrgs, TempUsers, Test_DEC, TK_BackgroundJobLogs, TK_BackgroundJobs, UserLoginRecords, WF_WorkflowTasks, WF_WorkflowSchemeInfos, WF_WorkflowSchemes, WF_WorkflowProcesses, WF_WorkflowSchemeAuths, WF_WorkflowSchemeInfoPermissionGrants, WF_WorkflowStamps, WF_WorkflowTaskLogs, WF_WorkflowUnits, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 15:21:41.040 +08:00 [INF] 表 __EFMigrationsHistory 总行数: 391
2025-06-20 15:21:41.109 +08:00 [INF] 表 AbpClaimTypes 总行数: 35
2025-06-20 15:21:41.154 +08:00 [INF] 表 AbpEditions 总行数: 0
2025-06-20 15:21:41.183 +08:00 [INF] 表 AbpFeatureValues 总行数: 24
2025-06-20 15:21:41.595 +08:00 [INF] 表 AbpLinkUsers 总行数: 0
2025-06-20 15:21:41.597 +08:00 [INF] 表 AbpLocalizationLanguages 总行数: 1
2025-06-20 15:21:41.961 +08:00 [INF] 表 AbpLocalizationTexts 总行数: 20
2025-06-20 15:21:41.961 +08:00 [INF] 表 AbpLocalizationResources 总行数: 1
2025-06-20 15:21:42.142 +08:00 [INF] 表 AbpRoles 总行数: 18
2025-06-20 15:21:42.323 +08:00 [INF] 表 AbpOrganizationUnits 总行数: 10
2025-06-20 15:21:42.459 +08:00 [INF] 表 AbpOrganizationUnitRoles 总行数: 0
2025-06-20 15:21:42.645 +08:00 [INF] 表 AbpPermissionGrants 总行数: 5696
2025-06-20 15:21:42.737 +08:00 [INF] 表 AbpRoleClaims 总行数: 1
2025-06-20 15:21:42.779 +08:00 [INF] 表 AbpSettings 总行数: 13
2025-06-20 15:21:43.020 +08:00 [INF] 表 AbpTenantConnectionStrings 总行数: 0
2025-06-20 15:21:43.021 +08:00 [INF] 表 AbpTenants 总行数: 1
2025-06-20 15:21:43.266 +08:00 [INF] 表 AbpUserLogins 总行数: 269
2025-06-20 15:21:43.340 +08:00 [INF] 表 AbpUsers 总行数: 483309
2025-06-20 15:21:43.418 +08:00 [INF] 表 AbpUserOrganizationUnits 总行数: 3
2025-06-20 15:21:43.937 +08:00 [INF] 表 AbpUserRoles 总行数: 523722
2025-06-20 15:21:43.965 +08:00 [INF] 表 AbpUserTokens 总行数: 0
2025-06-20 15:21:44.005 +08:00 [INF] 表 AbpWebhooksEvents 总行数: 0
2025-06-20 15:21:44.021 +08:00 [INF] 表 AbpWebhooksSendAttempts 总行数: 0
2025-06-20 15:21:44.089 +08:00 [INF] 表 AbpUserRoles_923bf 总行数: 455580
2025-06-20 15:21:44.177 +08:00 [INF] 表 AbpWebhooksSubscriptions 总行数: 1
2025-06-20 15:21:44.447 +08:00 [INF] 表 Airport_AssetLiabilities 总行数: 0
2025-06-20 15:21:44.465 +08:00 [INF] 表 Airport_BudgetAccounts 总行数: 0
2025-06-20 15:21:44.478 +08:00 [INF] 表 Airport_BudgetOrganizations 总行数: 0
2025-06-20 15:21:44.672 +08:00 [INF] 表 Airport_FinanceAuditBatches 总行数: 0
2025-06-20 15:21:44.686 +08:00 [INF] 表 Airport_FinanceAuditedDatas 总行数: 0
2025-06-20 15:21:44.885 +08:00 [INF] 表 Airport_FinanceProjects 总行数: 0
2025-06-20 15:21:44.952 +08:00 [INF] 表 Airport_NoFinanceProjects 总行数: 0
2025-06-20 15:21:45.119 +08:00 [INF] 表 Airport_NoFinancialBudgetProjects 总行数: 0
2025-06-20 15:21:45.214 +08:00 [INF] 表 Airport_OverheadCostCenters 总行数: 0
2025-06-20 15:21:45.404 +08:00 [INF] 表 AppChatGroups 总行数: 0
2025-06-20 15:21:45.438 +08:00 [INF] 表 AppGroupChatBlacks 总行数: 0
2025-06-20 15:21:45.932 +08:00 [INF] 表 AppGroupMessages 总行数: 0
2025-06-20 15:21:45.983 +08:00 [INF] 表 AppNotificationDefinitionGroups 总行数: 0
2025-06-20 15:21:46.098 +08:00 [INF] 表 AppNotificationDefinitions 总行数: 0
2025-06-20 15:21:46.131 +08:00 [INF] 表 AppPlatformDatas 总行数: 3
2025-06-20 15:21:46.449 +08:00 [INF] 表 AppPlatformDataItems 总行数: 27
2025-06-20 15:21:46.562 +08:00 [INF] 表 AppPlatformLayouts 总行数: 2
2025-06-20 15:21:46.721 +08:00 [INF] 表 AppPlatformMenus 总行数: 263
2025-06-20 15:21:47.046 +08:00 [INF] 表 AppPlatformPackages 总行数: 0
2025-06-20 15:21:47.066 +08:00 [INF] 表 AppPlatformPackageBlobs 总行数: 0
2025-06-20 15:21:47.082 +08:00 [INF] 表 AppPlatformRoleMenus 总行数: 585
2025-06-20 15:21:47.347 +08:00 [INF] 表 AppPlatformUserFavoriteMenus 总行数: 0
2025-06-20 15:21:47.380 +08:00 [INF] 表 AppPlatformUserMenus 总行数: 5281
2025-06-20 15:21:49.838 +08:00 [INF] 表 AppPlatformVersion 总行数: 0
2025-06-20 15:21:49.852 +08:00 [INF] 表 AppPlatformVersionFile 总行数: 0
2025-06-20 15:21:50.066 +08:00 [INF] 表 AppUserChatCards 总行数: 49825
2025-06-20 15:22:17.517 +08:00 [INF] 表 AbpAuditLogActions 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:17.790 +08:00 [INF] 表 AbpAuditLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:17.802 +08:00 [INF] 表 AbpEntityChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.062 +08:00 [INF] 表 AbpEntityPropertyChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.066 +08:00 [INF] 表 AbpSecurityLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.307 +08:00 [INF] 表 AbpUserClaims 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.362 +08:00 [INF] 表 AppNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.594 +08:00 [INF] 表 AppUserNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.608 +08:00 [INF] 表 AppUserSubscribes 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:18.796 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 15:22:19.226 +08:00 [INF] 表 AppUserChatFriends 总行数: 0
2025-06-20 15:22:19.241 +08:00 [INF] 表 AppUserChatGroups 总行数: 0
2025-06-20 15:22:19.348 +08:00 [INF] 表 AppUserChatSettings 总行数: 49825
2025-06-20 15:22:30.567 +08:00 [INF] 表 AppUserGroupCards 总行数: 0
2025-06-20 15:22:30.590 +08:00 [INF] 表 AppUserMessages 总行数: 0
2025-06-20 15:22:30.604 +08:00 [INF] 表 Bank_Ledger_BaseInfoStatistics 总行数: 0
2025-06-20 15:22:30.623 +08:00 [INF] 表 Bank_Ledger_ResettleHelpEducates 总行数: 0
2025-06-20 15:22:30.639 +08:00 [INF] 表 Bank_Ledger_RiskDetermines 总行数: 0
2025-06-20 15:22:30.655 +08:00 [INF] 表 Bank_Ledger_ServeSentenceAndCriminalInfoStatistics 总行数: 0
2025-06-20 15:22:30.672 +08:00 [INF] 表 BigScreenAuditDatas 总行数: 39
2025-06-20 15:22:31.276 +08:00 [INF] 表 BSPUser 总行数: 455368
2025-06-20 15:23:13.638 +08:00 [INF] 表 dept_new_zhong 总行数: 194
2025-06-20 15:23:13.788 +08:00 [INF] 表 excelbookuser 总行数: 1858
2025-06-20 15:23:14.034 +08:00 [INF] 表 excepbook 总行数: 92
2025-06-20 15:23:14.157 +08:00 [INF] 表 ExistingTables 总行数: 8073
2025-06-20 15:23:15.327 +08:00 [INF] 表 fill_user_book 总行数: 205200
2025-06-20 15:23:42.984 +08:00 [INF] 表 Filling_AreaOrganizationUnits 总行数: 98986
2025-06-20 15:24:05.521 +08:00 [INF] 表 Filling_CollaborativeReports 总行数: 1111
2025-06-20 15:24:07.337 +08:00 [INF] 表 Filling_Staff 总行数: 483643
2025-06-20 15:24:07.450 +08:00 [INF] 表 Filling_PlanTasks 总行数: 3627
2025-06-20 15:24:13.966 +08:00 [INF] 表 Filling_ReportTasks 总行数: 4883
2025-06-20 15:24:19.179 +08:00 [INF] 表 Filling_FileInfos 总行数: 1570
2025-06-20 15:24:20.623 +08:00 [INF] 表 Filling_ReportTaskAreaOrganizationUnits 总行数: 1911
2025-06-20 15:24:23.587 +08:00 [INF] 表 Filling_ReportTables 总行数: 1997
2025-06-20 15:24:26.859 +08:00 [INF] 表 Filling_FillingConfigs 总行数: 33
2025-06-20 15:24:27.064 +08:00 [INF] 表 Filling_PlanTaskAreaOrganizationUnits 总行数: 14241
2025-06-20 15:24:29.692 +08:00 [INF] 表 Filling_ReportTableTemplates 总行数: 4819
2025-06-20 15:24:50.159 +08:00 [INF] 表 Filling_PlanTaskReportTableTemplates 总行数: 3832
2025-06-20 15:24:51.077 +08:00 [INF] 表 Filling_PlanTaskStaffAreaOrganizationUnits 总行数: 217
2025-06-20 15:24:51.214 +08:00 [INF] 表 Filling_PlanTaskStaffs 总行数: 1913
2025-06-20 15:24:51.789 +08:00 [INF] 表 Filling_ReportMessageInfos 总行数: 0
2025-06-20 15:24:51.940 +08:00 [INF] 表 Filling_ReportTableRows 总行数: 2042
2025-06-20 15:24:53.702 +08:00 [INF] 表 Filling_ReportTaskAreaOrganizationUnitAudits 总行数: 957
2025-06-20 15:24:54.145 +08:00 [INF] 表 Filling_ReportTaskAreaOrganizationUnitFillers 总行数: 1067
2025-06-20 15:24:55.022 +08:00 [INF] 表 Filling_StaffPlanTaskHides 总行数: 865
2025-06-20 15:24:55.198 +08:00 [INF] 表 Filling_TableLedgerRuleConfigs 总行数: 0
2025-06-20 15:24:55.515 +08:00 [INF] 表 Filling_TableTemplateColumns 总行数: 175494
2025-06-20 15:25:05.514 +08:00 [INF] 表 Filling_TableTemplateDataRows 总行数: 370
2025-06-20 15:25:06.136 +08:00 [INF] 表 Filling_YkzOrgUnits 总行数: 97762
2025-06-20 15:26:07.859 +08:00 [INF] 表 IdentityServerApiResources 总行数: 3
2025-06-20 15:26:07.939 +08:00 [INF] 表 IdentityServerApiResourceClaims 总行数: 35
2025-06-20 15:26:08.019 +08:00 [INF] 表 IdentityServerApiResourceProperties 总行数: 0
2025-06-20 15:26:08.034 +08:00 [INF] 表 IdentityServerApiResourceScopes 总行数: 2
2025-06-20 15:26:08.092 +08:00 [INF] 表 IdentityServerApiResourceSecrets 总行数: 0
2025-06-20 15:26:08.104 +08:00 [INF] 表 IdentityServerApiScopes 总行数: 3
2025-06-20 15:26:08.193 +08:00 [INF] 表 IdentityServerApiScopeClaims 总行数: 0
2025-06-20 15:26:08.204 +08:00 [INF] 表 IdentityServerApiScopeProperties 总行数: 0
2025-06-20 15:26:08.221 +08:00 [INF] 表 IdentityServerClients 总行数: 11
2025-06-20 15:26:08.314 +08:00 [INF] 表 IdentityServerClientClaims 总行数: 0
2025-06-20 15:26:08.325 +08:00 [INF] 表 IdentityServerClientCorsOrigins 总行数: 1
2025-06-20 15:26:08.400 +08:00 [INF] 表 IdentityServerClientGrantTypes 总行数: 19
2025-06-20 15:26:08.481 +08:00 [INF] 表 IdentityServerClientIdPRestrictions 总行数: 0
2025-06-20 15:26:08.495 +08:00 [INF] 表 IdentityServerClientPostLogoutRedirectUris 总行数: 6
2025-06-20 15:26:08.563 +08:00 [INF] 表 IdentityServerClientProperties 总行数: 0
2025-06-20 15:26:08.575 +08:00 [INF] 表 IdentityServerClientRedirectUris 总行数: 12
2025-06-20 15:26:08.641 +08:00 [INF] 表 IdentityServerClientScopes 总行数: 58
2025-06-20 15:26:08.707 +08:00 [INF] 表 IdentityServerClientSecrets 总行数: 13
2025-06-20 15:26:08.780 +08:00 [INF] 表 IdentityServerDeviceFlowCodes 总行数: 0
2025-06-20 15:26:08.792 +08:00 [INF] 表 IdentityServerIdentityResources 总行数: 8
2025-06-20 15:26:08.869 +08:00 [INF] 表 IdentityServerIdentityResourceClaims 总行数: 34
2025-06-20 15:26:08.967 +08:00 [INF] 表 IdentityServerIdentityResourceProperties 总行数: 0
2025-06-20 15:26:09.094 +08:00 [INF] 表 IdentityServerPersistedGrants 总行数: 1012
2025-06-20 15:26:10.257 +08:00 [INF] 表 Ledger_AjcBaseInfoStatistics 总行数: 0
2025-06-20 15:26:10.312 +08:00 [INF] 表 Ledger_TableFieldGroups 总行数: 7016
2025-06-20 15:26:11.808 +08:00 [INF] 表 Ledger_DataSources 总行数: 26
2025-06-20 15:26:11.905 +08:00 [INF] 表 Ledger_SourceDbTableInfos 总行数: 926
2025-06-20 15:26:12.549 +08:00 [INF] 表 Ledger_DbTableFields 总行数: 23219
2025-06-20 15:26:18.104 +08:00 [INF] 表 Ledger_TableDataSets 总行数: 145
2025-06-20 15:26:18.271 +08:00 [INF] 表 Ledger_TableDataSetMappingFields 总行数: 3575
2025-06-20 15:26:19.968 +08:00 [INF] 表 Ledger_TableFields 总行数: 290241
2025-06-20 15:26:27.301 +08:00 [ERR] 获取表 hg_t_audit_log 行数失败
System.Exception: 获取表hg_t_audit_log的总行数在3次尝试后操作仍然失败。最后一次错误: XX000: permission denied 
 ---> Nhgdb.HighgoException (0x80004005): XX000: permission denied 
   at Nhgdb.Internal.NhgdbConnector.<ReadMessage>g__ReadMessageLong|224_0(NhgdbConnector connector, Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at Nhgdb.NhgdbDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Nhgdb.NhgdbCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
   at Nhgdb.NhgdbCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
   at Nhgdb.NhgdbCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Dapper.SqlMapper.QueryRowAsync[T](IDbConnection cnn, Row row, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 489
   at TryCode.DatabaseMigration.PostgreSQL.PostgresDataReader.<>c__DisplayClass11_0.<<GetTableRowCountAsync>b__0>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.PostgreSQL/PostgresDataReader.cs:line 253
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Core.DataReaders.DataReaderBase.ExecuteWithRetryAsync[T](Func`1 operation, String operationName, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Core/DataReaders/DataReaderBase.cs:line 41
  Exception data:
    Severity: ERROR
    SqlState: XX000
    MessageText: permission denied 
    File: hgaudit.c
    Line: 3178
    Routine: check_audit_table
   --- End of inner exception stack trace ---
   at TryCode.DatabaseMigration.Core.DataReaders.DataReaderBase.ExecuteWithRetryAsync[T](Func`1 operation, String operationName, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Core/DataReaders/DataReaderBase.cs:line 50
   at TryCode.DatabaseMigration.PostgreSQL.PostgresDataReader.GetTableRowCountAsync(String tableName, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.PostgreSQL/PostgresDataReader.cs:line 247
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 304
2025-06-20 15:26:27.623 +08:00 [INF] 表 Ledger_TableInfos 总行数: 9036
2025-06-20 15:26:34.019 +08:00 [INF] 表 Platform_Departments_error 总行数: 219108
2025-06-20 15:28:50.864 +08:00 [INF] 表 Platform_Departments 总行数: 258980
2025-06-20 15:29:24.815 +08:00 [INF] 表 Ledger_LedgerTypes 总行数: 1259
2025-06-20 15:29:25.484 +08:00 [INF] 表 Ledger_Ledgers 总行数: 8403
2025-06-20 15:29:45.607 +08:00 [INF] 表 Ledger_ApiAuxiliaryFillSources 总行数: 2
2025-06-20 15:29:45.681 +08:00 [INF] 表 Ledger_ApiAuxiliaryFillingRules 总行数: 179
2025-06-20 15:29:45.848 +08:00 [INF] 表 Ledger_ApiAuxiliaryFillings 总行数: 53
2025-06-20 15:29:45.962 +08:00 [INF] 表 Ledger_ApiDataSets 总行数: 6
2025-06-20 15:29:46.042 +08:00 [INF] 表 Ledger_ApiDataSetMappingFields 总行数: 278
2025-06-20 15:29:46.352 +08:00 [INF] 表 Ledger_ApiRequestParameters 总行数: 6
2025-06-20 15:29:46.428 +08:00 [INF] 表 Ledger_LedgerDataAnalyses 总行数: 539
2025-06-20 15:29:46.804 +08:00 [INF] 表 Ledger_AssociatedConfigs 总行数: 215
2025-06-20 15:29:48.052 +08:00 [INF] 表 Ledger_AssociatedSyns 总行数: 43
2025-06-20 15:29:48.152 +08:00 [INF] 表 Ledger_AssociatedSynConfigs 总行数: 296
2025-06-20 15:29:48.360 +08:00 [INF] 表 Ledger_BaseInfoStatistics 总行数: 1072
2025-06-20 15:29:49.760 +08:00 [INF] 表 Ledger_BigViewTypicalScenarios 总行数: 44
2025-06-20 15:29:49.917 +08:00 [INF] 表 Platform_Users 总行数: 483649
2025-06-20 15:29:56.371 +08:00 [INF] 表 Ledger_BigViewTypicalScenarioUploadRecords 总行数: 0
2025-06-20 15:29:56.421 +08:00 [INF] 表 Ledger_CommonGoalsTasks 总行数: 0
2025-06-20 15:29:56.448 +08:00 [INF] 表 Ledger_DataItemsManagements 总行数: 0
2025-06-20 15:29:56.472 +08:00 [INF] 表 Ledger_DataItemsUpdateRecords 总行数: 0
2025-06-20 15:29:56.521 +08:00 [INF] 表 Ledger_StarEvaluates 总行数: 41
2025-06-20 15:29:56.776 +08:00 [INF] 表 Ledger_DeductPoints 总行数: 11
2025-06-20 15:29:56.821 +08:00 [INF] 表 Ledger_StarEvaluatePublishes 总行数: 779
2025-06-20 15:29:56.870 +08:00 [INF] 表 Ledger_DeductPointsPublishes 总行数: 61
2025-06-20 15:29:56.985 +08:00 [INF] 表 Ledger_HistoryEvaluates 总行数: 0
2025-06-20 15:29:56.999 +08:00 [INF] 表 Ledger_StarEvaluateRecords 总行数: 0
2025-06-20 15:29:57.014 +08:00 [INF] 表 Ledger_DeductPointsRecords 总行数: 0
2025-06-20 15:29:57.030 +08:00 [INF] 表 Ledger_Disabilities 总行数: 0
2025-06-20 15:29:57.063 +08:00 [INF] 表 Ledger_HierarchicalLedgerDataPushOriginPlans 总行数: 0
2025-06-20 15:29:57.084 +08:00 [INF] 表 Ledger_HierarchicalLedgers 总行数: 3
2025-06-20 15:29:57.204 +08:00 [INF] 表 Ledger_HierarchicalLedgerTaskItems 总行数: 2
2025-06-20 15:29:57.517 +08:00 [INF] 表 Ledger_IndicatorCockpits 总行数: 45623
2025-06-20 15:29:58.311 +08:00 [INF] 表 Ledger_IndicatorCockpitConfigs 总行数: 35
2025-06-20 15:29:58.853 +08:00 [INF] 表 Ledger_LedgerAuditBatches 总行数: 73785
2025-06-20 15:30:58.795 +08:00 [INF] 表 Ledger_LedgerAuditBatchFlows 总行数: 169
2025-06-20 15:30:58.978 +08:00 [INF] 表 Ledger_LedgerAuditBatchFlowNodes 总行数: 397
2025-06-20 15:30:59.425 +08:00 [INF] 表 Ledger_LedgerAuditBatchStatistics 总行数: 67421
2025-06-20 15:31:08.648 +08:00 [INF] 表 Ledger_LedgerAuxiliaryFillings 总行数: 51
2025-06-20 15:31:08.747 +08:00 [INF] 表 Ledger_LedgerAuxiliaryFillingConfigs 总行数: 172
2025-06-20 15:31:08.821 +08:00 [INF] 表 Ledger_LedgerDataSetAuxiliaryFillings 总行数: 2
2025-06-20 15:31:08.885 +08:00 [INF] 表 Ledger_LedgerAuxiliaryFillingPreviews 总行数: 159
2025-06-20 15:31:09.017 +08:00 [INF] 表 Ledger_LedgerChangeRecords 总行数: 73
2025-06-20 15:31:09.126 +08:00 [INF] 表 Ledger_LedgerChangeRecordDetails 总行数: 135
2025-06-20 15:31:09.311 +08:00 [INF] 表 Ledger_LedgerDataComparisonRecords 总行数: 38
2025-06-20 15:31:09.414 +08:00 [INF] 表 Ledger_LedgerDataSetAuxiliaryFillingConfigs 总行数: 2
2025-06-20 15:31:09.489 +08:00 [INF] 表 Ledger_LedgerDataSyncConfigs 总行数: 1
2025-06-20 15:31:09.569 +08:00 [INF] 表 Ledger_LedgerDataSyncTasks 总行数: 1
2025-06-20 15:31:09.645 +08:00 [INF] 表 Ledger_LedgerDataSyncFields 总行数: 1
2025-06-20 15:31:10.900 +08:00 [INF] 表 Ledger_LedgerDepartmentDataStatistics 总行数: 1388
2025-06-20 15:31:12.477 +08:00 [INF] 表 Ledger_LedgerDepartmentDataDetailStatistics 总行数: 86453
2025-06-20 15:31:16.788 +08:00 [INF] 表 Ledger_LedgerDepartments 总行数: 168891
2025-06-20 15:31:34.159 +08:00 [INF] 表 Ledger_LedgerHierarchicalAuthorizations 总行数: 1
2025-06-20 15:31:34.339 +08:00 [INF] 表 Ledger_LedgerPermissionsAuthorizationModes 总行数: 1263125
2025-06-20 15:31:56.202 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 55000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11ce00-2ec2-fcfe-fe70-9f07715b96c5-3a0ef8dc-68cc-6947-523b-b7f1e17f0e2f' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:31:56.204 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:31:57.574 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 57000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11ce9e-4105-9dc9-ef3f-00694f42a922-3a0ef848-984a-8a48-50b9-a2d6e83b9142' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:31:57.576 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:31:58.849 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 59000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11d412-39a6-96e6-3922-89407d0245b9-3a0ef90f-5331-51d4-a613-2b39aca0352b' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:31:58.850 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:00.505 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 61000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11d42b-f55a-fec8-8cfb-98fd5cc5015e-3a0fa199-7d82-84d7-fb09-3ff5ce976a8d' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:00.512 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:02.051 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 63000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11dd47-8763-2207-63dc-91cb7d6d32e0-3a0ef848-1f04-5ba6-30a9-79676d605767' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:02.056 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:03.420 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 65000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11de4b-9afe-4f84-8f32-403fceb698b6-3a0fc5a5-122d-126a-7b89-97887cd20cdf' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:03.422 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:04.663 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 67000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11de58-9af0-53af-0fe5-4b77ef722ebb-3a0fc5ba-ba49-36fd-9e71-355be6143a52' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:04.665 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:06.059 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 69000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11de8c-ae76-55d6-d881-b0c7f9fb64cc-3a0fbc0b-2845-2031-ecb0-efecee956036' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:06.063 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:07.939 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 71000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11e31d-125c-135e-3f6b-b72df8868843-3a0fe606-7ccd-fe04-dd96-0065be387378' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:07.964 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:08.657 +08:00 [INF] 表 Ledger_LedgerRunways 总行数: 2642
2025-06-20 15:32:09.705 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 73000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11f1bc-8aad-a6f6-228b-9d729da97204-3a0fc1c1-9b4d-766f-2c72-354ed4975efa' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:09.707 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:11.316 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 75000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11f2c1-648a-cbec-3dc8-58a52effe3f6-3a0fbb24-9215-25b3-e66f-12c191b080c5' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:11.319 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:11.371 +08:00 [INF] 表 Ledger_LedgerRunwayRelations 总行数: 44
2025-06-20 15:32:11.487 +08:00 [INF] 表 Ledger_TableQueryConfigs 总行数: 256
2025-06-20 15:32:11.749 +08:00 [INF] 表 Ledger_LedgerStatistics 总行数: 183
2025-06-20 15:32:11.915 +08:00 [INF] 表 Ledger_LedgerTemplates 总行数: 139
2025-06-20 15:32:11.997 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 76000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11f2c1-648a-cbec-3dc8-58a52effe3f6-3a0fc5a4-68df-1493-46d2-0e03d6ffb4c4' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:12.000 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:13.424 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 78000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11fcff-4806-0d3f-78a6-c62e35397d89-3a0ef8d6-1594-539f-64e8-6b442c1514a2' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:13.426 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:15.488 +08:00 [INF] 表 Ledger_LedgerUsers 总行数: 1096540
2025-06-20 15:32:15.498 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 80000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a11fde0-e2af-c7de-5c96-711aab11ed95-3a0efdf6-8834-91c7-030f-d01948a2326c' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:15.500 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:17.103 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 82000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a120173-0fc7-0151-de7d-d441e1aa42ce-3a0fa199-993f-3518-ecfa-08e039c43e6a' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:17.106 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:18.537 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 84000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a120738-99e9-25cc-d4c6-8c7604a8ac17-3a0ef8d3-61f8-64ff-b351-051442bdb647' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:18.541 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:20.119 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 86000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a12171e-0785-ee99-9503-a2e905681f15-3a0ef8db-40b4-6f6a-a452-9d6b176a3335' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:20.121 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:21.468 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 88000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a121bc5-dc15-d97f-3585-476e31668e4f-3a0fbb3d-5d51-4dd6-6429-794b3b1c5671' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:21.470 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:22.917 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 90000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a121c77-02ae-851c-2988-a6f0ff80f150-3a0ef843-be94-8ec4-c514-ccf7ba04afa1' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:22.918 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:24.340 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 92000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122016-5516-f185-b5fe-f11c0ccb1b47-3a0fc673-3f51-6b39-e1a0-9c24f9317634' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:24.341 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:25.695 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 94000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122135-28c8-4e5b-3fa5-35669ebc5572-3a0fc03b-56d2-a341-ab79-34f8c7d7809f' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:25.697 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:27.094 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 96000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122503-0578-42c2-e0b3-896cd4d4784b-3a0fc1bd-2d82-2447-bf6f-aa1b9dfdc7b2' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:27.096 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:28.525 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 98000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122674-8319-72a1-bf41-169392de8267-3a0fc0b2-a4e8-7f09-ccea-ab4fa875df29' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:28.527 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:29.916 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 100000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122a84-35f2-cccf-5498-b3b223e6e62d-3a0fc6de-1f1e-f4e3-1323-373ae2806cb2' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:29.917 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:31.288 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 102000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122af6-9e6d-c016-4b80-dd21617ed772-3a0f5ae4-c166-3cb1-11a1-5bfd664fdd59' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:31.290 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:33.805 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 105000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a122f93-074a-2d2d-4e28-0977a12e8517-3a0fbb25-fc81-7bf8-7248-9f3fea94493a' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:33.807 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:36.100 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 108000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a1234aa-b333-37c2-c728-a5cf193fff40-3a0ef8da-1651-c6cb-7f11-98a7b0ecbd5b' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:36.102 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:37.739 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 110000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a123584-9ae8-66da-9e80-55e4f3a3a66d-3a0fc5bb-baf7-7cf2-85fd-1f3e1e21af6d' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:37.742 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:39.351 +08:00 [ERR] 迁移表 Ledger_LedgerDepartments 数据批次失败，批次大小: 1000，当前行: 112000，总行数: 168891
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a1235f7-38b6-0235-538e-c2d40419355a-3a0fbb5b-a7e0-bcca-d057-89efc941706c' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 670
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 117
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 15:32:39.353 +08:00 [ERR] 可能存在唯一键冲突，请检查源数据。如果是继续之前的迁移，可能部分数据已存在
2025-06-20 15:32:40.174 +08:00 [WRN] 迁移操作被用户取消
2025-06-20 15:32:40.175 +08:00 [INF] 正在关闭日志记录器...
