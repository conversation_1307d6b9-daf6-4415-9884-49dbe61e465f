2025-06-20 17:54:05.354 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql.config.json
2025-06-20 17:56:37.528 +08:00 [INF] 获取到285个表
2025-06-20 17:56:37.536 +08:00 [INF] 正在更新断点文件中的排除表信息...
2025-06-20 17:56:37.590 +08:00 [INF] 已将表 AbpAuditLogActions 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.616 +08:00 [INF] 已将表 AbpAuditLogs 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.642 +08:00 [INF] 已将表 AbpEntityChanges 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.643 +08:00 [INF] 已将表 AbpEntityPropertyChanges 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.645 +08:00 [INF] 已将表 AbpSecurityLogs 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.646 +08:00 [INF] 已将表 AbpUserClaims 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.648 +08:00 [INF] 已将表 AppNotifications 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.649 +08:00 [INF] 已将表 AppUserNotifications 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.676 +08:00 [INF] 已将表 AppUserSubscribes 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.703 +08:00 [INF] 已将表 Ledger_LedgerDataUpdateLogs 在断点文件中标记为仅创建结构
2025-06-20 17:56:37.705 +08:00 [INF] 分区表处理完成，原始表数: 1，处理后表数: 1
2025-06-20 17:56:37.705 +08:00 [INF] 将为以下排除的表创建表结构（不迁移数据）: AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 17:56:37.706 +08:00 [INF] 过滤后剩余11个表
2025-06-20 17:56:37.706 +08:00 [INF] 使用并行度: 4
2025-06-20 17:56:37.718 +08:00 [INF] 开始处理以下表的迁移: Ledger_LedgerDepartments, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 17:56:37.718 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 17:56:37.718 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 17:56:37.718 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 17:56:37.718 +08:00 [WRN] 表 AbpUserClaims 的外键引用了不在迁移范围内的表 AbpUsers，此外键将被忽略
2025-06-20 17:56:37.718 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Platform_Users，此外键将被忽略
2025-06-20 17:56:37.718 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 17:56:37.718 +08:00 [INF] 表的迁移顺序: Ledger_LedgerDepartments, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 17:56:37.726 +08:00 [INF] 表 AbpEntityChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:37.726 +08:00 [INF] 表 AbpAuditLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:37.726 +08:00 [INF] 表 AbpAuditLogActions 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:38.773 +08:00 [INF] 表 AbpEntityPropertyChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:38.952 +08:00 [INF] 表 AbpSecurityLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.187 +08:00 [INF] 表 AbpUserClaims 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.417 +08:00 [INF] 表 AppNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.516 +08:00 [INF] 表 AbpAuditLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.666 +08:00 [INF] 表 AbpAuditLogActions 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.666 +08:00 [INF] 表 AbpEntityChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.866 +08:00 [INF] 表 AppUserNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.866 +08:00 [INF] 表 AbpEntityPropertyChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.965 +08:00 [INF] 表 Ledger_LedgerDepartments 总行数: 168892
2025-06-20 17:56:39.989 +08:00 [INF] 表 AppUserSubscribes 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:39.989 +08:00 [INF] 表 AbpSecurityLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:47.714 +08:00 [INF] 表 AppNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:47.714 +08:00 [INF] 表 AbpUserClaims 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:47.714 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 17:56:47.714 +08:00 [INF] 表 AppUserNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 17:56:47.714 +08:00 [INF] 表 AppUserSubscribes 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:04:56.594 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql.config.json
2025-06-20 18:05:26.561 +08:00 [INF] 获取到285个表
2025-06-20 18:05:26.567 +08:00 [INF] 正在更新断点文件中的排除表信息...
2025-06-20 18:05:26.618 +08:00 [INF] 已将表 AbpAuditLogActions 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.619 +08:00 [INF] 已将表 AbpAuditLogs 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.620 +08:00 [INF] 已将表 AbpEntityChanges 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.620 +08:00 [INF] 已将表 AbpEntityPropertyChanges 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.621 +08:00 [INF] 已将表 AbpSecurityLogs 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.622 +08:00 [INF] 已将表 AbpUserClaims 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.647 +08:00 [INF] 已将表 AppNotifications 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.669 +08:00 [INF] 已将表 AppUserNotifications 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.671 +08:00 [INF] 已将表 AppUserSubscribes 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.673 +08:00 [INF] 已将表 Ledger_LedgerDataUpdateLogs 在断点文件中标记为仅创建结构
2025-06-20 18:05:26.675 +08:00 [INF] 分区表处理完成，原始表数: 1，处理后表数: 1
2025-06-20 18:05:26.675 +08:00 [INF] 将为以下排除的表创建表结构（不迁移数据）: AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:05:26.675 +08:00 [INF] 过滤后剩余11个表
2025-06-20 18:05:26.675 +08:00 [INF] 使用并行度: 4
2025-06-20 18:05:26.686 +08:00 [INF] 开始处理以下表的迁移: Ledger_LedgerDepartments, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:05:26.687 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:05:26.687 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 18:05:26.687 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 18:05:26.687 +08:00 [WRN] 表 AbpUserClaims 的外键引用了不在迁移范围内的表 AbpUsers，此外键将被忽略
2025-06-20 18:05:26.687 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Platform_Users，此外键将被忽略
2025-06-20 18:05:26.687 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:05:26.687 +08:00 [INF] 表的迁移顺序: Ledger_LedgerDepartments, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:05:26.697 +08:00 [INF] 表 AbpAuditLogActions 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.698 +08:00 [INF] 表 AbpEntityChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.723 +08:00 [INF] 表 AbpAuditLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.775 +08:00 [INF] 表 AbpEntityPropertyChanges 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.776 +08:00 [INF] 表 AbpSecurityLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.776 +08:00 [INF] 表 AbpUserClaims 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.925 +08:00 [INF] 表 AppUserNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.925 +08:00 [INF] 表 AppNotifications 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.949 +08:00 [INF] 表 AbpAuditLogActions 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.949 +08:00 [INF] 表 AbpAuditLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.950 +08:00 [INF] 表 AppUserSubscribes 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.951 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 被标记为仅创建结构（在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.950 +08:00 [INF] 表 AbpEntityChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:26.981 +08:00 [INF] 表 AbpEntityPropertyChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.005 +08:00 [INF] 表 AbpSecurityLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.034 +08:00 [INF] 表 AppUserNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.034 +08:00 [INF] 表 AbpUserClaims 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.034 +08:00 [INF] 表 AppNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.061 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.061 +08:00 [INF] 表 AppUserSubscribes 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:05:27.121 +08:00 [INF] 表 Ledger_LedgerDepartments 总行数: 168892
2025-06-20 18:08:30.270 +08:00 [INF] 所有表结构已创建完成，等待数据迁移完成...
2025-06-20 18:08:30.271 +08:00 [INF] 数据迁移和外键创建全部完成
2025-06-20 18:08:30.311 +08:00 [INF] 正在生成迁移报告...
2025-06-20 18:08:30.346 +08:00 [INF] 迁移报告已生成: migration_report.txt
2025-06-20 18:08:30.346 +08:00 [INF] 数据库迁移成功完成
2025-06-20 18:08:30.347 +08:00 [INF] 正在关闭日志记录器...
2025-06-20 18:16:53.887 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql.config.json
2025-06-20 18:20:34.423 +08:00 [INF] 获取到285个表
2025-06-20 18:20:34.429 +08:00 [INF] 正在更新断点文件中的排除表信息...
2025-06-20 18:20:34.476 +08:00 [INF] 已将表 AbpAuditLogActions 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.478 +08:00 [INF] 已将表 AbpAuditLogs 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.478 +08:00 [INF] 已将表 AbpEntityChanges 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.479 +08:00 [INF] 已将表 AbpEntityPropertyChanges 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.479 +08:00 [INF] 已将表 AbpSecurityLogs 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.479 +08:00 [INF] 已将表 AbpUserClaims 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.479 +08:00 [INF] 已将表 AppNotifications 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.480 +08:00 [INF] 已将表 AppUserNotifications 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.480 +08:00 [INF] 已将表 AppUserSubscribes 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.480 +08:00 [INF] 已将表 Ledger_LedgerDataUpdateLogs 在断点文件中标记为仅创建结构
2025-06-20 18:20:34.482 +08:00 [INF] 分区表处理完成，原始表数: 2，处理后表数: 2
2025-06-20 18:20:34.482 +08:00 [INF] 将为以下排除的表创建表结构（不迁移数据）: AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:20:34.482 +08:00 [INF] 过滤后剩余12个表
2025-06-20 18:20:34.482 +08:00 [INF] 使用并行度: 4
2025-06-20 18:20:34.493 +08:00 [INF] 开始处理以下表的迁移: Ledger_LedgerDepartments, Ledger_LedgerUsers, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerUsers 的外键引用了不在迁移范围内的表 Platform_Users，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerUsers 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 AbpUserClaims 的外键引用了不在迁移范围内的表 AbpUsers，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Platform_Users，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:20:34.493 +08:00 [INF] 表的迁移顺序: Ledger_LedgerDepartments, Ledger_LedgerUsers, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:20:34.501 +08:00 [INF] 表 Ledger_LedgerDepartments 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.501 +08:00 [INF] 表 AbpAuditLogActions 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.501 +08:00 [INF] 表 AbpAuditLogs 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.502 +08:00 [INF] 表 AbpEntityChanges 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.504 +08:00 [INF] 表 AbpEntityPropertyChanges 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.504 +08:00 [INF] 表 AbpSecurityLogs 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.504 +08:00 [INF] 表 AbpUserClaims 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.504 +08:00 [INF] 表 AppUserNotifications 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.504 +08:00 [INF] 表 AppNotifications 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.505 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.505 +08:00 [INF] 表 AppUserSubscribes 已经完成迁移，跳过所有操作
2025-06-20 18:20:34.506 +08:00 [INF] 表 Ledger_LedgerDepartments 已经完成迁移，跳过数据迁移
2025-06-20 18:20:34.508 +08:00 [INF] 表 Ledger_LedgerDepartments 已经完成迁移，跳过外键创建
2025-06-20 18:20:35.183 +08:00 [INF] 表 AbpAuditLogActions 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AbpEntityChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AbpAuditLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AbpEntityPropertyChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AbpSecurityLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AbpUserClaims 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AppUserNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AppUserSubscribes 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 AppNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.183 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:20:35.310 +08:00 [INF] 表 Ledger_LedgerUsers 总行数: 1096541
2025-06-20 18:20:41.586 +08:00 [WRN] 迁移操作被用户取消
2025-06-20 18:20:41.587 +08:00 [INF] 正在关闭日志记录器...
2025-06-20 18:20:48.450 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql.config.json
2025-06-20 18:21:21.330 +08:00 [INF] 获取到285个表
2025-06-20 18:21:21.335 +08:00 [INF] 正在更新断点文件中的排除表信息...
2025-06-20 18:21:21.377 +08:00 [INF] 已将表 AbpAuditLogActions 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.379 +08:00 [INF] 已将表 AbpAuditLogs 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.379 +08:00 [INF] 已将表 AbpEntityChanges 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.379 +08:00 [INF] 已将表 AbpEntityPropertyChanges 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.380 +08:00 [INF] 已将表 AbpSecurityLogs 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.380 +08:00 [INF] 已将表 AbpUserClaims 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.380 +08:00 [INF] 已将表 AppNotifications 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.380 +08:00 [INF] 已将表 AppUserNotifications 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.381 +08:00 [INF] 已将表 AppUserSubscribes 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.381 +08:00 [INF] 已将表 Ledger_LedgerDataUpdateLogs 在断点文件中标记为仅创建结构
2025-06-20 18:21:21.382 +08:00 [INF] 分区表处理完成，原始表数: 2，处理后表数: 2
2025-06-20 18:21:21.382 +08:00 [INF] 将为以下排除的表创建表结构（不迁移数据）: AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:21:21.383 +08:00 [INF] 过滤后剩余12个表
2025-06-20 18:21:21.383 +08:00 [INF] 使用并行度: 4
2025-06-20 18:21:21.391 +08:00 [INF] 开始处理以下表的迁移: Ledger_LedgerDepartments, Ledger_LedgerUsers, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerDepartments 的外键引用了不在迁移范围内的表 Platform_Departments，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerUsers 的外键引用了不在迁移范围内的表 Platform_Users，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerUsers 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 AbpUserClaims 的外键引用了不在迁移范围内的表 AbpUsers，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Platform_Users，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [WRN] 表 Ledger_LedgerDataUpdateLogs 的外键引用了不在迁移范围内的表 Ledger_Ledgers，此外键将被忽略
2025-06-20 18:21:21.391 +08:00 [INF] 表的迁移顺序: Ledger_LedgerDepartments, Ledger_LedgerUsers, AbpAuditLogActions, AbpAuditLogs, AbpEntityChanges, AbpEntityPropertyChanges, AbpSecurityLogs, AbpUserClaims, AppNotifications, AppUserNotifications, AppUserSubscribes, Ledger_LedgerDataUpdateLogs
2025-06-20 18:21:21.397 +08:00 [INF] 表 AbpAuditLogs 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.397 +08:00 [INF] 表 Ledger_LedgerDepartments 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.397 +08:00 [INF] 表 AbpAuditLogActions 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.399 +08:00 [INF] 表 AbpEntityChanges 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.399 +08:00 [INF] 表 AbpEntityPropertyChanges 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.400 +08:00 [INF] 表 AbpSecurityLogs 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.400 +08:00 [INF] 表 AbpUserClaims 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.400 +08:00 [INF] 表 AppNotifications 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.401 +08:00 [INF] 表 AppUserNotifications 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.402 +08:00 [INF] 表 AppUserSubscribes 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.402 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 已经完成迁移，跳过所有操作
2025-06-20 18:21:21.402 +08:00 [INF] 表 Ledger_LedgerDepartments 已经完成迁移，跳过数据迁移
2025-06-20 18:21:21.402 +08:00 [INF] 表 AbpAuditLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.402 +08:00 [INF] 表 Ledger_LedgerUsers 已迁移 12000 行数据，从断点处继续迁移
2025-06-20 18:21:21.402 +08:00 [INF] 表 AbpAuditLogActions 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.403 +08:00 [INF] 表 AbpEntityPropertyChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.403 +08:00 [INF] 表 AbpEntityChanges 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.403 +08:00 [INF] 表 AbpUserClaims 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.403 +08:00 [INF] 表 AbpSecurityLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.403 +08:00 [INF] 表 AppNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.403 +08:00 [INF] 表 AppUserNotifications 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.404 +08:00 [INF] 表 AppUserSubscribes 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.404 +08:00 [INF] 表 Ledger_LedgerDataUpdateLogs 跳过数据迁移（表在排除列表中但配置了创建表结构）
2025-06-20 18:21:21.406 +08:00 [INF] 表 Ledger_LedgerDepartments 已经完成迁移，跳过外键创建
2025-06-20 18:21:21.485 +08:00 [INF] 表 Ledger_LedgerUsers 总行数: 1096541
2025-06-20 18:21:22.258 +08:00 [ERR] 迁移表 Ledger_LedgerUsers 数据批次失败，批次大小: 1000，当前行: 12000，总行数: 1096541
MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a0e63b2-3c7d-ede5-3d67-2e48ec3ae72b-3a0b5083-7407-3323-656d-62c4e9eae7c8-3a0c407d-2517-d5fd-c2c7-e3b6552cf10c' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 662
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 118
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
2025-06-20 18:21:22.267 +08:00 [ERR] 检测到主键冲突，这可能是由于ORDER BY不稳定导致的重复数据读取。请检查源数据或考虑重新开始迁移
2025-06-20 18:21:22.271 +08:00 [ERR] 迁移表 Ledger_LedgerUsers 数据时出错: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。 详细错误: Duplicate entry '3a0e63b2-3c7d-ede5-3d67-2e48ec3ae72b-3a0b5083-7407-3323-656d-62c4e9eae7c8-3a0c407d-2517-d5fd-c2c7-e3b6552cf10c' for key 'PRIMARY'
2025-06-20 18:21:22.272 +08:00 [INF] 表 AbpAuditLogActions 已经完成迁移，跳过外键创建
2025-06-20 18:21:22.279 +08:00 [INF] 正在生成迁移报告...
2025-06-20 18:21:22.284 +08:00 [INF] 迁移报告已生成: migration_report.txt
2025-06-20 18:21:22.284 +08:00 [ERR] 未知错误: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
System.InvalidOperationException: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。
 ---> MySql.Data.MySqlClient.MySqlException (0x80004005): Duplicate entry '3a0e63b2-3c7d-ede5-3d67-2e48ec3ae72b-3a0b5083-7407-3323-656d-62c4e9eae7c8-3a0c407d-2517-d5fd-c2c7-e3b6552cf10c' for key 'PRIMARY'
   at MySql.Data.MySqlClient.MySqlStream.ReadPacketAsync(Boolean execAsync)
   at MySql.Data.MySqlClient.NativeDriver.GetResultAsync(Int32 affectedRow, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.GetResultAsync(Int32 statementId, Int32 affectedRows, Int64 insertedId, Boolean execAsync)
   at MySql.Data.MySqlClient.Driver.NextResultAsync(Int32 statementId, Boolean force, Boolean execAsync)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlDataReader.NextResultAsync(Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, Boolean execAsync, CancellationToken cancellationToken)
   at MySql.Data.MySqlClient.MySqlCommand.ExecuteNonQueryAsync(Boolean execAsync, CancellationToken cancellationToken)
   at Dapper.SqlMapper.ExecuteImplAsync(IDbConnection cnn, CommandDefinition command, Object param) in /_/Dapper/SqlMapper.Async.cs:line 662
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.WriteDataBatchAsync(String tableName, List`1 batch, List`1 columns, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 118
   at TryCode.DatabaseMigration.MySQL.Writers.MySqlBulkWriter.BulkWriteAsync(String tableName, IEnumerable`1 data, CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.MySQL/Writers/MySqlBulkWriter.cs:line 86
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 380
   --- End of inner exception stack trace ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 401
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.<>c__DisplayClass8_0.<<ExecuteAsync>b__3>d.MoveNext() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 451
--- End of stack trace from previous location ---
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 649
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 673
   at TryCode.DatabaseMigration.Parallel.ParallelMigrationExecutor.ExecuteAsync(CancellationToken cancellationToken) in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs:line 683
   at TryCode.DatabaseMigration.CLI.Commands.MigrationCommand.ExecuteMigrationCommandAsync() in /Users/<USER>/RiderProjects/database-migration-tool/src/TryCode.DatabaseMigration.CLI/Commands/MigrationCommand.cs:line 199
2025-06-20 18:21:22.287 +08:00 [INF] 正在关闭日志记录器...
