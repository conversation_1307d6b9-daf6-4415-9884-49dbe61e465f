using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Configuration.Models;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Parallel;

namespace TryCode.DatabaseMigration.CLI
{
    /// <summary>
    /// 分区表迁移演示程序
    /// </summary>
    public class PartitionTableMigrationDemo
    {
        private readonly ILogger<PartitionTableMigrationDemo> _logger;

        public PartitionTableMigrationDemo(ILogger<PartitionTableMigrationDemo> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 演示分区表处理逻辑
        /// </summary>
        public void DemonstratePartitionTableProcessing()
        {
            _logger.LogInformation("=== 分区表迁移逻辑演示 ===");

            // 模拟从PostgreSQL获取的表列表（包括主分区表和各个分区）
            var allTables = CreateMockTableList();

            _logger.LogInformation("原始表列表（共 {Count} 个表）:", allTables.Count);
            foreach (var table in allTables)
            {
                _logger.LogInformation("- {TableName} (IsPartitioned: {IsPartitioned}, Partitions: {PartitionCount})",
                    table.Name, table.IsPartitioned, table.Partitions.Count);
            }

            // 模拟配置（包含所有表名，这是推荐的配置方式）
            var config = new MigrationConfig
            {
                Tables = allTables.Select(t => t.Name).ToList()
            };

            // 创建模拟的并行迁移执行器来测试ProcessPartitionTables方法
            var executor = new MockParallelMigrationExecutor(config, _logger);
            var processedTables = executor.TestProcessPartitionTables(allTables);

            _logger.LogInformation("\n处理后的表列表（共 {Count} 个表）:", processedTables.Count);
            foreach (var table in processedTables)
            {
                _logger.LogInformation("- {TableName} (IsPartitioned: {IsPartitioned}, EstimatedRows: {RowCount})",
                    table.Name, table.IsPartitioned, table.EstimatedRowCount);

                if (table.AdditionalInfo.TryGetValue("PartitionDataSources", out var sources))
                {
                    _logger.LogInformation("  分区数据源: {Sources}",
                        string.Join(", ", ((List<Dictionary<string, object>>)sources)
                            .Select(s => $"{s["Name"]}({s["RowCount"]})")));
                }
            }

            _logger.LogInformation("\n=== 演示完成 ===");
            _logger.LogInformation("总结：原始8个表（1主表+5分区+2普通表）被正确处理为3个表（1分区表+2普通表）");
            _logger.LogInformation("这避免了在目标数据库中创建多个独立的分区表，而是创建一个正确的分区表结构。");

            // 测试MySQL分区表创建SQL生成
            _logger.LogInformation("\n=== 测试MySQL分区表SQL生成 ===");
            var testHelper = new MySqlPartitionTableTestHelper(_logger);
            testHelper.TestMySqlPartitionTableCreation(processedTables);
        }

        /// <summary>
        /// 创建模拟的表列表，模拟PostgreSQL中的分区表结构
        /// </summary>
        private List<TableSchema> CreateMockTableList()
        {
            var tables = new List<TableSchema>();

            // 1. 主分区表 LedgerData_LedgerAuditedData（模拟实际的UUID分区键场景）
            var mainTable = new TableSchema
            {
                Name = "LedgerData_LedgerAuditedData",
                IsPartitioned = true,
                PartitionType = "HASH",
                PartitionKey = "LedgerId", // 使用UUID类型的分区键来测试回退逻辑
                EstimatedRowCount = 0, // 主表本身没有数据
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema { Name = "Id", DataType = "char(36)", IsPrimaryKey = true },
                    new ColumnSchema { Name = "LedgerId", DataType = "char(36)" }, // UUID类型，不支持HASH分区
                    new ColumnSchema { Name = "OperationType", DataType = "int" }, // 整数类型，支持HASH分区
                    new ColumnSchema { Name = "Status", DataType = "int" },
                    new ColumnSchema { Name = "Data", DataType = "longtext" },
                    new ColumnSchema { Name = "CreationTime", DataType = "datetime(6)" }
                },
                PrimaryKeys = new List<string> { "Id", "LedgerId" },
                Partitions = new List<PartitionDefinition>()
            };

            // 2. 添加分区定义到主表
            for (int i = 0; i < 5; i++) // 只创建5个分区用于演示
            {
                mainTable.Partitions.Add(new PartitionDefinition
                {
                    Name = $"LedgerData_LedgerAuditedData_{i}",
                    Condition = $"FOR VALUES WITH (MODULUS 100, REMAINDER {i})"
                });
            }

            tables.Add(mainTable);

            // 3. 添加各个分区表（在PostgreSQL中这些会被当作独立的表查询出来）
            for (int i = 0; i < 5; i++)
            {
                var partitionTable = new TableSchema
                {
                    Name = $"LedgerData_LedgerAuditedData_{i}",
                    IsPartitioned = false, // 分区本身不是分区表
                    EstimatedRowCount = 1000 + i * 500, // 模拟不同的行数
                    Columns = mainTable.Columns, // 与主表相同的列结构
                    PrimaryKeys = mainTable.PrimaryKeys
                };
                tables.Add(partitionTable);
            }

            // 4. 添加一些普通表
            tables.Add(new TableSchema
            {
                Name = "RegularTable1",
                IsPartitioned = false,
                EstimatedRowCount = 2000,
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema { Name = "id", DataType = "int", IsPrimaryKey = true },
                    new ColumnSchema { Name = "name", DataType = "varchar" }
                },
                PrimaryKeys = new List<string> { "id" }
            });

            tables.Add(new TableSchema
            {
                Name = "RegularTable2",
                IsPartitioned = false,
                EstimatedRowCount = 1500,
                Columns = new List<ColumnSchema>
                {
                    new ColumnSchema { Name = "id", DataType = "int", IsPrimaryKey = true },
                    new ColumnSchema { Name = "description", DataType = "text" }
                },
                PrimaryKeys = new List<string> { "id" }
            });

            return tables;
        }
    }

    /// <summary>
    /// 模拟的并行迁移执行器，用于测试分区表处理逻辑
    /// </summary>
    public class MockParallelMigrationExecutor
    {
        private readonly MigrationConfig _config;
        private readonly ILogger _logger;

        public MockParallelMigrationExecutor(MigrationConfig config, ILogger logger)
        {
            _config = config;
            _logger = logger;
        }



        /// <summary>
        /// 测试分区表处理方法（从ParallelMigrationExecutor复制的逻辑）
        /// </summary>
        public List<TableSchema> TestProcessPartitionTables(List<TableSchema> tables)
        {
            var result = new List<TableSchema>();
            var processedTables = new HashSet<string>();

            // 按表名排序，确保主表在分区之前处理
            var sortedTables = tables.OrderBy(t => t.Name).ToList();

            foreach (var table in sortedTables)
            {
                if (processedTables.Contains(table.Name))
                    continue;

                // 检查是否是分区表的主表
                if (table.IsPartitioned && table.Partitions.Count > 0)
                {
                    _logger.LogInformation("发现分区表 {TableName}，包含 {PartitionCount} 个分区", 
                        table.Name, table.Partitions.Count);

                    // 收集所有相关的分区表数据
                    var allPartitionData = new List<TableSchema>();
                    allPartitionData.Add(table); // 主表

                    // 查找所有分区表
                    foreach (var partition in table.Partitions)
                    {
                        var partitionTable = tables.FirstOrDefault(t => t.Name == partition.Name);
                        if (partitionTable != null)
                        {
                            allPartitionData.Add(partitionTable);
                            processedTables.Add(partitionTable.Name);
                            _logger.LogDebug("找到分区表 {PartitionName}，将合并到主表 {MainTable}", 
                                partition.Name, table.Name);
                        }
                    }

                    // 创建合并后的分区表结构
                    var mergedTable = CreateMergedPartitionTable(table, allPartitionData);
                    result.Add(mergedTable);
                    processedTables.Add(table.Name);

                    _logger.LogInformation("分区表 {TableName} 处理完成，将作为单个分区表迁移", table.Name);
                }
                else
                {
                    // 检查是否是某个分区表的分区
                    bool isPartitionOfAnotherTable = sortedTables.Any(t => 
                        t.IsPartitioned && 
                        t.Partitions.Any(p => p.Name == table.Name));

                    if (!isPartitionOfAnotherTable)
                    {
                        // 普通表，直接添加
                        result.Add(table);
                        processedTables.Add(table.Name);
                    }
                    else
                    {
                        _logger.LogDebug("表 {TableName} 是分区表的分区，将被合并到主表中", table.Name);
                        processedTables.Add(table.Name);
                    }
                }
            }

            _logger.LogInformation("分区表处理完成，原始表数: {OriginalCount}，处理后表数: {ProcessedCount}", 
                tables.Count, result.Count);

            return result;
        }

        private TableSchema CreateMergedPartitionTable(TableSchema mainTable, List<TableSchema> allPartitionData)
        {
            var mergedTable = new TableSchema
            {
                Name = mainTable.Name,
                Columns = mainTable.Columns,
                ForeignKeys = mainTable.ForeignKeys,
                PrimaryKeys = mainTable.PrimaryKeys,
                IsPartitioned = true,
                PartitionKey = mainTable.PartitionKey,
                PartitionKeys = mainTable.PartitionKeys,
                PartitionType = mainTable.PartitionType,
                PartitionCount = mainTable.PartitionCount,
                Partitions = mainTable.Partitions,
                AdditionalInfo = mainTable.AdditionalInfo
            };

            // 计算总行数（所有分区的行数之和）
            mergedTable.EstimatedRowCount = allPartitionData.Sum(t => t.EstimatedRowCount);

            // 添加分区数据信息到AdditionalInfo中，供后续迁移使用
            var partitionDataInfo = allPartitionData.Where(t => t.Name != mainTable.Name)
                .Select(t => new Dictionary<string, object>
                {
                    ["Name"] = t.Name,
                    ["RowCount"] = t.EstimatedRowCount
                })
                .ToList();

            mergedTable.AdditionalInfo["PartitionDataSources"] = partitionDataInfo;

            // 根据实际的分区数据源数量设置分区数量
            mergedTable.PartitionCount = partitionDataInfo.Count;

            _logger.LogInformation("合并分区表 {TableName}，总行数: {TotalRows}，分区数量: {PartitionCount}，分区数据源: {PartitionSources}",
                mergedTable.Name, mergedTable.EstimatedRowCount, mergedTable.PartitionCount,
                string.Join(", ", partitionDataInfo.Select(p => $"{p["Name"]}({p["RowCount"]})")));

            return mergedTable;
        }


    }

    // 模拟类用于测试
    public class MockMySqlTypeConverter
    {
        public string ConvertDataType(ColumnSchema column)
        {
            return column.DataType switch
            {
                "uuid" => "char(36) CHARACTER SET ascii",
                "varchar" => $"varchar({column.MaxLength ?? 255}) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
                "text" => "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
                "int" => "int",
                "datetime" => "datetime(6)",
                "boolean" => "tinyint(1)",
                _ => column.DataType
            };
        }

        public bool ValidateKeyLength(List<string> keys, TableSchema schema)
        {
            return true; // 简化测试
        }
    }

    public class MockMySqlPartitionManager
    {
        private readonly ILogger _logger;

        public MockMySqlPartitionManager(ILogger logger)
        {
            _logger = logger;
        }

        public void AppendPartitionDefinition(StringBuilder sql, TableSchema schema)
        {
            _logger.LogInformation("开始为表 {TableName} 生成分区定义，分区类型: {PartitionType}，分区数量: {PartitionCount}，分区键: {PartitionKey}",
                schema.Name, schema.PartitionType, schema.PartitionCount, schema.PartitionKey);

            if (!schema.IsPartitioned)
            {
                _logger.LogWarning("表 {TableName} 的分区配置无效", schema.Name);
                return;
            }

            // 使用实际的分区数量，如果为0则默认为4
            var partitionCount = schema.PartitionCount > 0 ? schema.PartitionCount : 4;

            // 处理HASH分区类型
            if (schema.PartitionType?.Equals("HASH", StringComparison.OrdinalIgnoreCase) == true)
            {
                // 尝试HASH分区，如果失败则回退到KEY分区
                if (!TryAppendHashPartition(sql, schema, partitionCount))
                {
                    _logger.LogInformation("HASH分区不可用，回退到KEY分区");
                    AppendKeyPartition(sql, schema, partitionCount);
                }
            }
            else
            {
                // 其他分区类型暂时使用简单逻辑
                var partitionKey = schema.PartitionKey;
                if (string.IsNullOrEmpty(partitionKey))
                {
                    _logger.LogWarning("表 {TableName} 的分区键未指定", schema.Name);
                    return;
                }

                sql.AppendLine($"PARTITION BY {schema.PartitionType} (`{partitionKey}`)");
                sql.AppendLine($"PARTITIONS {partitionCount}");

                _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个{PartitionType}分区，分区键: {PartitionKey}",
                    schema.Name, partitionCount, schema.PartitionType, partitionKey);
            }
        }

        private bool TryAppendHashPartition(StringBuilder sql, TableSchema schema, int partitionCount)
        {
            // 智能选择合适的HASH分区键
            var partitionKey = SelectValidHashPartitionKey(schema);

            if (string.IsNullOrEmpty(partitionKey))
            {
                _logger.LogWarning("表 {TableName} 没有找到合适的HASH分区键", schema.Name);
                return false;
            }

            sql.AppendLine($"PARTITION BY HASH (`{partitionKey}`)");
            sql.AppendLine($"PARTITIONS {partitionCount}");

            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个HASH分区，分区键: {PartitionKey}",
                schema.Name, partitionCount, partitionKey);
            return true;
        }

        private void AppendKeyPartition(StringBuilder sql, TableSchema schema, int partitionCount)
        {
            // 智能选择KEY分区键
            var partitionKey = SelectValidKeyPartitionKey(schema);

            if (string.IsNullOrEmpty(partitionKey))
            {
                _logger.LogWarning("表 {TableName} 的KEY分区未找到合适的分区键", schema.Name);
                return;
            }

            sql.AppendLine($"PARTITION BY KEY (`{partitionKey}`)");
            sql.AppendLine($"PARTITIONS {partitionCount}");

            _logger.LogInformation("为表 {TableName} 创建了 {PartitionCount} 个KEY分区，分区键: {PartitionKey}",
                schema.Name, partitionCount, partitionKey);
        }

        private string SelectValidHashPartitionKey(TableSchema schema)
        {
            // 1. 首先检查原始分区键是否有效
            if (!string.IsNullOrEmpty(schema.PartitionKey))
            {
                var originalColumn = schema.Columns.FirstOrDefault(c => c.Name.Equals(schema.PartitionKey, StringComparison.OrdinalIgnoreCase));
                if (originalColumn != null && IsValidHashPartitionType(originalColumn.DataType))
                {
                    _logger.LogInformation("使用原始分区键 {PartitionKey} 作为HASH分区键", schema.PartitionKey);
                    return schema.PartitionKey;
                }
                else
                {
                    _logger.LogWarning("原始分区键 {PartitionKey} 的类型 {DataType} 不支持HASH分区，将寻找替代方案",
                        schema.PartitionKey, originalColumn?.DataType ?? "未知");
                }
            }

            // 2. 检查主键中是否有有效的键
            foreach (var primaryKey in schema.PrimaryKeys)
            {
                var column = schema.Columns.FirstOrDefault(c => c.Name.Equals(primaryKey, StringComparison.OrdinalIgnoreCase));
                if (column != null && IsValidHashPartitionType(column.DataType))
                {
                    _logger.LogInformation("使用主键 {PrimaryKey} 作为HASH分区键", primaryKey);
                    return primaryKey;
                }
            }

            // 3. 寻找任何整数类型的列作为分区键
            var integerColumn = schema.Columns.FirstOrDefault(c => IsValidHashPartitionType(c.DataType));
            if (integerColumn != null)
            {
                _logger.LogInformation("使用整数列 {ColumnName} 作为HASH分区键", integerColumn.Name);
                return integerColumn.Name;
            }

            return string.Empty;
        }

        private string SelectValidKeyPartitionKey(TableSchema schema)
        {
            // 1. 首先检查原始分区键
            if (!string.IsNullOrEmpty(schema.PartitionKey))
            {
                var originalColumn = schema.Columns.FirstOrDefault(c => c.Name.Equals(schema.PartitionKey, StringComparison.OrdinalIgnoreCase));
                if (originalColumn != null)
                {
                    _logger.LogInformation("使用原始分区键 {PartitionKey} 作为KEY分区键", schema.PartitionKey);
                    return schema.PartitionKey;
                }
            }

            // 2. 使用主键
            if (schema.PrimaryKeys.Count > 0)
            {
                var primaryKey = schema.PrimaryKeys[0];
                _logger.LogInformation("使用主键 {PrimaryKey} 作为KEY分区键", primaryKey);
                return primaryKey;
            }

            // 3. 使用第一个列
            if (schema.Columns.Count > 0)
            {
                var firstColumn = schema.Columns[0];
                _logger.LogInformation("使用第一个列 {ColumnName} 作为KEY分区键", firstColumn.Name);
                return firstColumn.Name;
            }

            return string.Empty;
        }

        private bool IsValidHashPartitionType(string dataType)
        {
            if (string.IsNullOrEmpty(dataType))
                return false;

            var lowerType = dataType.ToLower();

            // MySQL HASH分区支持的类型
            return lowerType.Contains("int") ||          // int, bigint, smallint, tinyint
                   lowerType.Contains("serial") ||       // serial, bigserial
                   lowerType.Contains("year") ||         // year
                   lowerType == "bit";                   // bit
        }
    }

    public class MockMySqlTableBuilder
    {
        private readonly MockMySqlTypeConverter _typeConverter;
        private readonly MockMySqlPartitionManager _partitionManager;
        private readonly ILogger _logger;

        public MockMySqlTableBuilder(MockMySqlTypeConverter typeConverter, MockMySqlPartitionManager partitionManager, ILogger logger)
        {
            _typeConverter = typeConverter;
            _partitionManager = partitionManager;
            _logger = logger;
        }

        public string TestBuildCreateTableSql(TableSchema schema)
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE `{schema.Name}` (");

            // 添加列定义
            var columnDefinitions = new List<string>();
            foreach (var column in schema.Columns)
            {
                var mysqlType = _typeConverter.ConvertDataType(column);
                var definition = $"`{column.Name}` {mysqlType}";

                if (column.IsNullable)
                {
                    definition += " DEFAULT NULL";
                }
                else
                {
                    definition += " NOT NULL";
                }

                columnDefinitions.Add(definition);
            }

            // 添加主键
            if (schema.PrimaryKeys.Any())
            {
                var primaryKeyColumns = schema.PrimaryKeys.Select(pk => $"`{pk}`");
                columnDefinitions.Add($"PRIMARY KEY ({string.Join(", ", primaryKeyColumns)})");
            }

            sql.AppendLine(string.Join(",\n    ", columnDefinitions));
            sql.AppendLine(")");

            // 添加表选项
            sql.AppendLine("ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");

            // 添加分区定义
            if (schema.IsPartitioned)
            {
                _partitionManager.AppendPartitionDefinition(sql, schema);
            }

            sql.AppendLine(";");
            return sql.ToString();
        }
    }

    /// <summary>
    /// MySQL分区表测试助手类
    /// </summary>
    public class MySqlPartitionTableTestHelper
    {
        private readonly ILogger _logger;

        public MySqlPartitionTableTestHelper(ILogger logger)
        {
            _logger = logger;
        }

        public void TestMySqlPartitionTableCreation(List<TableSchema> processedTables)
        {
            var partitionTable = processedTables.FirstOrDefault(t => t.IsPartitioned);
            if (partitionTable == null)
            {
                _logger.LogWarning("没有找到分区表进行测试");
                return;
            }

            _logger.LogInformation("测试分区表 {TableName} 的MySQL建表SQL生成", partitionTable.Name);
            _logger.LogInformation("分区信息：类型={PartitionType}, 数量={PartitionCount}, 键={PartitionKey}",
                partitionTable.PartitionType, partitionTable.PartitionCount, partitionTable.PartitionKey);

            try
            {
                // 创建MySQL表构建器来测试SQL生成
                var mockTypeConverter = new MockMySqlTypeConverter();
                var mockPartitionManager = new MockMySqlPartitionManager(_logger);
                var tableBuilder = new MockMySqlTableBuilder(mockTypeConverter, mockPartitionManager, _logger);

                var sql = tableBuilder.TestBuildCreateTableSql(partitionTable);
                _logger.LogInformation("生成的MySQL建表SQL：\n{SQL}", sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成MySQL建表SQL时出错: {ErrorMessage}", ex.Message);
            }
        }
    }
}
