using CommandLine;

namespace TryCode.DatabaseMigration.CLI.Options
{
    /// <summary>
    /// 演示命令选项
    /// </summary>
    [Verb("demo", HelpText = "运行分区表迁移逻辑演示")]
    public class DemoOptions
    {
        [Option('t', "type", Required = false, Default = "partition", HelpText = "演示类型 (partition: 分区表处理逻辑)")]
        public string DemoType { get; set; } = "partition";

        [Option('v', "verbose", Required = false, Default = false, HelpText = "详细输出")]
        public bool Verbose { get; set; }
    }
}
