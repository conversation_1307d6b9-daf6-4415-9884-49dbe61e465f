using System.Collections.Generic;
using CommandLine;

namespace TryCode.DatabaseMigration.CLI.Options
{
    /// <summary>
    /// 迁移命令选项
    /// </summary>
    [Verb("migrate", HelpText = "执行数据库迁移")]
    public class MigrationOptions
    {
        [Option('c', "config", Required = true, HelpText = "配置文件路径")]
        public string ConfigFile { get; set; } = string.Empty;

        [Option('t', "tables", Separator = ',', HelpText = "要迁移的表（逗号分隔），与排除表参数互斥")]
        public IEnumerable<string>? Tables { get; set; }

        [Option('x', "exclude-tables", Separator = ',', HelpText = "要排除的表（逗号分隔），与迁移表参数互斥")]
        public IEnumerable<string>? ExcludedTables { get; set; }

        [Option("create-schema-for-excluded", HelpText = "为排除的表创建表结构（不迁移数据）")]
        public bool CreateSchemaForExcludedTables { get; set; }

        [Option('p', "parallel", Default = 4, HelpText = "最大并行度")]
        public int MaxDegreeOfParallelism { get; set; }

        [Option("skip-schema", HelpText = "跳过创建表结构")]
        public bool SkipSchemaCreation { get; set; }

        [Option("schema-only", HelpText = "只迁移表结构，不迁移数据")]
        public bool SchemaOnly { get; set; }

        [Option("skip-fk", HelpText = "跳过创建外键")]
        public bool SkipForeignKeys { get; set; }

        [Option("checkpoint", HelpText = "断点文件路径")]
        public string? CheckpointFile { get; set; }

        [Option("batch-size", Default = 1000, HelpText = "批处理大小")]
        public int BatchSize { get; set; }

        [Option("timeout", Default = 30, HelpText = "命令超时时间（秒）")]
        public int CommandTimeout { get; set; }

        [Option("retry", Default = 3, HelpText = "失败重试次数")]
        public int RetryCount { get; set; }

        [Option("retry-delay", Default = 5, HelpText = "重试延迟时间（秒）")]
        public int RetryDelay { get; set; }

        [Option("dry-run", HelpText = "仅显示将要执行的操作，不实际执行")]
        public bool DryRun { get; set; }

        [Option("plan", HelpText = "仅显示迁移计划而不执行实际迁移")]
        public bool ShowPlanOnly { get; set; }

        [Option("no-confirm", HelpText = "不询问确认直接执行迁移")]
        public bool NoConfirm { get; set; }

        [Option("no-progress", HelpText = "不显示进度条")]
        public bool NoProgress { get; set; }

        [Option("no-spinner", HelpText = "不显示加载动画")]
        public bool NoSpinner { get; set; }

        [Option("verbose", HelpText = "显示详细日志")]
        public bool Verbose { get; set; } = true;

        [Option("ignore-errors", HelpText = "忽略错误继续迁移其他表")]
        public bool IgnoreErrors { get; set; }

        [Option("report-file", HelpText = "迁移报告输出文件路径")]
        public string? ReportFile { get; set; }
    }

    /// <summary>
    /// 验证命令选项
    /// </summary>
    [Verb("validate", HelpText = "验证数据库迁移配置")]
    public class ValidateOptions
    {
        [Option('c', "config", Required = true, HelpText = "配置文件路径")]
        public string ConfigFile { get; set; } = string.Empty;

        [Option("test-connection", HelpText = "测试数据库连接")]
        public bool TestConnection { get; set; }

        [Option("test-permissions", HelpText = "测试数据库权限")]
        public bool TestPermissions { get; set; }

        [Option("estimate-size", HelpText = "估算迁移数据大小")]
        public bool EstimateSize { get; set; }
    }
}
