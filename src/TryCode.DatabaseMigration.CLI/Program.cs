// See https://aka.ms/new-console-template for more information

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using CommandLine;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.PostgreSQL;
using TryCode.DatabaseMigration.CLI.Commands;
using TryCode.DatabaseMigration.CLI.Options;
using TryCode.DatabaseMigration.Core.Exceptions;
using TryCode.DatabaseMigration.MySQL;

namespace TryCode.DatabaseMigration.CLI
{
    class Program
    {
        static async Task<int> Main(string[] args)
        {
            try
            {
                return await RunAsync(args);
            }
            finally
            {
                // 确保在程序退出时清理日志资源
                MigrationCommand.CloseLogger();
            }
        }

        private static async Task<int> RunAsync(string[] args)
        {
            try
            {
                // 配置服务
                var services = new ServiceCollection();

                // 添加日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // 注册演示服务
                services.AddScoped<PartitionTableMigrationDemo>();

                // 注册服务
                services.AddScoped<IDatabaseProviderFactory>(sp =>
                {
                    var options = sp.GetRequiredService<MigrationOptions>();
                    var logger = sp.GetRequiredService<ILogger<MySqlDataWriter>>();
                    var provider = new Configuration.ConfigurationProvider(options.ConfigFile);
                    var config = provider.GetMigrationConfigAsync().GetAwaiter().GetResult();
                    return config.Target.Type.ToLower() switch
                    {
                        "postgresql" => new PostgresProviderFactory(config.Target.ConnectionString, logger),
                        "mysql" => new MySqlProviderFactory(config.Target.ConnectionString, logger,
                            config.TypeMappings),
                        _ => throw new ConfigurationValidationException($"不支持的目标数据库类型: {config.Target.Type}")
                    };
                });

                services.AddScoped<IDataReader>(sp => sp.GetRequiredService<IDatabaseProviderFactory>().CreateReader());
                services.AddScoped<IDataWriter>(sp => sp.GetRequiredService<IDatabaseProviderFactory>().CreateWriter());
                services.AddScoped<ITypeConverter>(sp =>
                    sp.GetRequiredService<IDatabaseProviderFactory>().CreateTypeConverter());

                // 构建服务提供程序
                var serviceProvider = services.BuildServiceProvider();

                return await Parser.Default.ParseArguments<MigrationOptions, ValidateOptions, DemoOptions>(args)
                    .MapResult(
                        async (MigrationOptions opts) =>
                        {
                            using var command = new MigrationCommand(opts);
                            return await command.ExecuteMigrationCommandAsync();
                        },
                        (ValidateOptions opts) => ValidateConfigAsync(opts),
                        async (DemoOptions opts) => await RunDemoAsync(opts, serviceProvider),
                        errs => Task.FromResult(1));
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"发生未处理的错误: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Console.ResetColor();
                return 1;
            }
        }

        private static async Task<int> ValidateConfigAsync(ValidateOptions options)
        {
            try
            {
                var provider = new Configuration.ConfigurationProvider(options.ConfigFile);
                var config = await provider.GetMigrationConfigAsync();
                Console.WriteLine("配置验证通过！");

                if (options.TestConnection)
                {
                    Console.WriteLine("正在测试数据库连接...");
                    // TODO: 实现数据库连接测试
                }

                return 0;
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"配置验证失败: {ex.Message}");
                Console.ResetColor();
                return 1;
            }
        }

        private static async Task<int> RunDemoAsync(DemoOptions options, ServiceProvider serviceProvider)
        {
            try
            {
                Console.WriteLine($"运行演示: {options.DemoType}");

                if (options.DemoType.ToLower() == "partition")
                {
                    var demo = serviceProvider.GetRequiredService<PartitionTableMigrationDemo>();
                    demo.DemonstratePartitionTableProcessing();
                }
                else
                {
                    Console.WriteLine($"未知的演示类型: {options.DemoType}");
                    return 1;
                }

                return 0;
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"演示运行失败: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Console.ResetColor();
                return 1;
            }
        }
    }
}