{"Source": {"Type": "PostgreSQL", "ConnectionString": "Host=127.0.0.1;Database=YbtDb_Test;Username=********;Password=********", "BatchSize": 1000, "CommandTimeout": 30}, "Target": {"Type": "MySQL", "ConnectionString": "Server=localhost;Database=test_db1;Uid=root;Pwd=******", "BatchSize": 1000, "CommandTimeout": 30}, "ExcludedTables": ["__EFMigrationsHistory"], "EnableCheckpoint": true, "CheckpointFilePath": "ybtdb_migration_checkpoint.json", "SkipSchemaCreation": false, "SkipForeignKeys": false, "MaxDegreeOfParallelism": 4, "Logging": {"LogLevel": "Information", "FilePath": "ybtdb_migration.log", "ConsoleOutput": true}, "TypeMappings": {"array": "JSON", "jsonb": "JSON", "timestamp": "datetime", "timestamp without time zone": "datetime", "money": "decimal(19,4)", "serial": "int", "bigserial": "bigint", "integer": "int", "character varying": "<PERSON><PERSON><PERSON>(255)", "date": "date"}}