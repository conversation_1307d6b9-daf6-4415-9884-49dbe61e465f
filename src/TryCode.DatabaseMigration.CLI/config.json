{"Source": {"Type": "PostgreSQL", "ConnectionString": "Host=127.0.0.1;Database=YbtDb_Test;Username=********;Password=********", "BatchSize": 1000, "CommandTimeout": 30}, "Target": {"Type": "PostgreSQL", "ConnectionString": "Host=127.0.0.1;Database=YbtDb_Test_Target;Username=********;Password=********", "BatchSize": 1000, "CommandTimeout": 30}, "Tables": ["users", "products", "orders"], "ExcludedTables": ["__EFMigrationsHistory"], "EnableCheckpoint": true, "CheckpointFilePath": "migration_checkpoint.json", "SkipSchemaCreation": false, "SkipForeignKeys": false, "MaxDegreeOfParallelism": 4, "Logging": {"LogLevel": "Information", "FilePath": "migration.log", "ConsoleOutput": true}, "TypeMappings": {"array": "JSON", "jsonb": "JSON", "timestamp": "datetime", "timestamp without time zone": "datetime", "money": "decimal(19,4)", "serial": "int", "bigserial": "bigint", "integer": "int", "character varying": "<PERSON><PERSON><PERSON>", "date": "date", "text": "text"}}