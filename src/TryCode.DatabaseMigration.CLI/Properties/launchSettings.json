{"profiles": {"迁移数据库": {"commandName": "Project", "commandLineArgs": "migrate -c \"config.json\" -t \"users,products,orders\" -p 4 --checkpoint \"migration_checkpoint.json\" --batch-size 1000 --timeout 30 --retry 3 --retry-delay 5 --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "迁移YbtDb_Test": {"commandName": "Project", "commandLineArgs": "migrate -c \"ybtdb-test.config.json\" -x \"__EFMigrationsHistory\" -p 4 --checkpoint \"ybtdb_migration_checkpoint.json\" --batch-size 1000 --timeout 30 --retry 3 --retry-delay 5 --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "显示YbtDb_Test迁移计划": {"commandName": "Project", "commandLineArgs": "migrate -c \"ybtdb-test.config.json\" -x \"__EFMigrationsHistory\" --plan --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "显示YbtDb_Test迁移计划并执行": {"commandName": "Project", "commandLineArgs": "migrate -c \"ybtdb-test.config.json\" -x \"__EFMigrationsHistory\" -p 4 --checkpoint \"ybtdb_migration_checkpoint.json\" --batch-size 1000 --timeout 30 --retry 3 --retry-delay 5 --verbose --no-confirm", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "迁移数据库(跳过结构)": {"commandName": "Project", "commandLineArgs": "migrate -c \"config.json\" -t \"users,products,orders\" --skip-schema --skip-fk --dry-run --no-progress", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "迁移YbtDb_Test(跳过结构)": {"commandName": "Project", "commandLineArgs": "migrate -c \"ybtdb-test.config.json\" -x \"__EFMigrationsHistory\" --skip-schema --skip-fk --dry-run --no-progress", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "验证配置": {"commandName": "Project", "commandLineArgs": "validate -c \"config.json\" --test-connection --test-permissions --estimate-size", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "验证YbtDb_Test配置": {"commandName": "Project", "commandLineArgs": "validate -c \"ybtdb-test.config.json\" --test-connection --test-permissions --estimate-size", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "Postgres到MySQL迁移": {"commandName": "Project", "commandLineArgs": "migrate -c \"pg-to-mysql.config.json\" -p 4 --checkpoint \"pgtomysql_migration_checkpoint.json\" --batch-size 1000 --timeout 30 --retry 3 --retry-delay 5 --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "Postgres到MySQL的Ledger迁移": {"commandName": "Project", "commandLineArgs": "migrate -c \"pg-to-mysql-ledgerdata.config.json\" -p 4 --checkpoint \"pgtomysql_migration_checkpoint.json\" --batch-size 1000 --timeout 30 --retry 3 --retry-delay 5 --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "显示Postgres到MySQL迁移计划": {"commandName": "Project", "commandLineArgs": "migrate -c \"pg-to-mysql.config.json\" --plan --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "Postgres到MySQL仅迁移表结构": {"commandName": "Project", "commandLineArgs": "migrate -c \"pg-to-mysql.config.json\" -p 4 --schema-only --checkpoint \"pgtomysql_schema_checkpoint.json\" --timeout 30 --retry 3 --retry-delay 5 --verbose", "workingDirectory": "$(ProjectDir)", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}}}