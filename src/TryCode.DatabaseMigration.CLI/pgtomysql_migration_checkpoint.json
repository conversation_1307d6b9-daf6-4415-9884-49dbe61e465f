{"MigrationId": "340709b0-3a14-4264-84ed-d1b5818d0624", "SourceType": "MySQL", "TargetType": "MySQL", "StartTime": "2025-06-20T15:21:31.156786+08:00", "LastUpdateTime": "2025-06-20T15:32:39.544017+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 170, "TotalRows": 7265227, "MigratedRows": 4814022, "TableCheckpoints": {"AbpAuditLogActions": {"TableName": "AbpAuditLogActions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.174884+08:00", "LastUpdateTime": "2025-06-20T15:22:18.06199+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpAuditLogs": {"TableName": "AbpAuditLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.194837+08:00", "LastUpdateTime": "2025-06-20T15:22:18.30689+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityChanges": {"TableName": "AbpEntityChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.195329+08:00", "LastUpdateTime": "2025-06-20T15:22:18.358839+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityPropertyChanges": {"TableName": "AbpEntityPropertyChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.195797+08:00", "LastUpdateTime": "2025-06-20T15:22:18.592942+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSecurityLogs": {"TableName": "AbpSecurityLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.196202+08:00", "LastUpdateTime": "2025-06-20T15:22:18.607728+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserClaims": {"TableName": "AbpUserClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.196659+08:00", "LastUpdateTime": "2025-06-20T15:22:18.792034+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotifications": {"TableName": "AppNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.196973+08:00", "LastUpdateTime": "2025-06-20T15:22:18.863+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserNotifications": {"TableName": "AppUserNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.197272+08:00", "LastUpdateTime": "2025-06-20T15:22:19.031523+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserSubscribes": {"TableName": "AppUserSubscribes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.197651+08:00", "LastUpdateTime": "2025-06-20T15:22:19.073164+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataUpdateLogs": {"TableName": "Ledger_LedgerDataUpdateLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:40.198716+08:00", "LastUpdateTime": "2025-06-20T15:22:19.283986+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "__EFMigrationsHistory": {"TableName": "__EFMigrationsHistory", "TotalRows": 391, "MigratedRows": 391, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:40.221915+08:00", "LastUpdateTime": "2025-06-20T15:21:42.279666+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpClaimTypes": {"TableName": "AbpClaimTypes", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:40.223169+08:00", "LastUpdateTime": "2025-06-20T15:21:43.284661+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEditions": {"TableName": "AbpEditions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:40.223656+08:00", "LastUpdateTime": "2025-06-20T15:21:44.289832+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpFeatureValues": {"TableName": "AbpFeature<PERSON><PERSON>ues", "TotalRows": 24, "MigratedRows": 24, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:40.223951+08:00", "LastUpdateTime": "2025-06-20T15:21:45.295843+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLinkUsers": {"TableName": "AbpLinkUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.02582+08:00", "LastUpdateTime": "2025-06-20T15:21:46.301671+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationLanguages": {"TableName": "AbpLocalizationLanguages", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.027265+08:00", "LastUpdateTime": "2025-06-20T15:21:47.30765+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationResources": {"TableName": "AbpLocalizationResources", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.093079+08:00", "LastUpdateTime": "2025-06-20T15:21:48.308672+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationTexts": {"TableName": "AbpLocalizationTexts", "TotalRows": 20, "MigratedRows": 20, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.093552+08:00", "LastUpdateTime": "2025-06-20T15:21:49.312757+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpRoles": {"TableName": "AbpRoles", "TotalRows": 18, "MigratedRows": 18, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.399169+08:00", "LastUpdateTime": "2025-06-20T15:21:50.318708+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpOrganizationUnits": {"TableName": "AbpOrganizationUnits", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.581529+08:00", "LastUpdateTime": "2025-06-20T15:21:51.472945+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpOrganizationUnitRoles": {"TableName": "AbpOrganizationUnitRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.792127+08:00", "LastUpdateTime": "2025-06-20T15:21:52.727038+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpPermissionGrants": {"TableName": "AbpPermissionGrants", "TotalRows": 5696, "MigratedRows": 5696, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:41.942418+08:00", "LastUpdateTime": "2025-06-20T15:21:53.729322+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpRoleClaims": {"TableName": "AbpRoleClaims", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:42.130256+08:00", "LastUpdateTime": "2025-06-20T15:21:54.865137+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSettings": {"TableName": "AbpSettings", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:42.310517+08:00", "LastUpdateTime": "2025-06-20T15:21:55.869018+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpTenants": {"TableName": "AbpTenants", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:42.446765+08:00", "LastUpdateTime": "2025-06-20T15:21:57.032492+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpTenantConnectionStrings": {"TableName": "AbpTenantConnectionStrings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:42.58617+08:00", "LastUpdateTime": "2025-06-20T15:21:58.205758+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUsers": {"TableName": "AbpUsers", "TotalRows": 483309, "MigratedRows": 483309, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:42.721203+08:00", "LastUpdateTime": "2025-06-20T15:29:25.78037+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserLogins": {"TableName": "AbpUserLogins", "TotalRows": 269, "MigratedRows": 269, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:42.765185+08:00", "LastUpdateTime": "2025-06-20T15:29:26.985398+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserOrganizationUnits": {"TableName": "AbpUserOrganizationUnits", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.00537+08:00", "LastUpdateTime": "2025-06-20T15:29:28.183501+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserRoles": {"TableName": "AbpUserRoles", "TotalRows": 523722, "MigratedRows": 523722, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.005622+08:00", "LastUpdateTime": "2025-06-20T15:29:29.383113+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserRoles_923bf": {"TableName": "AbpUserRoles_923bf", "TotalRows": 455580, "MigratedRows": 455580, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.248016+08:00", "LastUpdateTime": "2025-06-20T15:29:30.384425+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserTokens": {"TableName": "AbpUserTokens", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.248396+08:00", "LastUpdateTime": "2025-06-20T15:29:31.50323+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksEvents": {"TableName": "AbpWebhooksEvents", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.403232+08:00", "LastUpdateTime": "2025-06-20T15:29:32.511182+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksSendAttempts": {"TableName": "AbpWebhooksSendAttempts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.443398+08:00", "LastUpdateTime": "2025-06-20T15:29:33.668513+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksSubscriptions": {"TableName": "AbpWebhooksSubscriptions", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.62048+08:00", "LastUpdateTime": "2025-06-20T15:29:34.675434+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_AssetLiabilities": {"TableName": "Airport_AssetLiabilities", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.676946+08:00", "LastUpdateTime": "2025-06-20T15:29:35.684372+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_BudgetAccounts": {"TableName": "Airport_BudgetAccounts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.904514+08:00", "LastUpdateTime": "2025-06-20T15:29:36.691444+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_BudgetOrganizations": {"TableName": "Airport_BudgetOrganizations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:43.942249+08:00", "LastUpdateTime": "2025-06-20T15:29:37.694236+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceAuditBatches": {"TableName": "Airport_FinanceAuditBatches", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.159377+08:00", "LastUpdateTime": "2025-06-20T15:29:38.69617+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceAuditedDatas": {"TableName": "Airport_FinanceAuditedDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.164879+08:00", "LastUpdateTime": "2025-06-20T15:29:39.700908+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceProjects": {"TableName": "Airport_FinanceProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.353048+08:00", "LastUpdateTime": "2025-06-20T15:29:40.709556+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_NoFinanceProjects": {"TableName": "Airport_NoFinanceProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.395449+08:00", "LastUpdateTime": "2025-06-20T15:29:41.71673+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_NoFinancialBudgetProjects": {"TableName": "Airport_NoFinancialBudgetProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.634506+08:00", "LastUpdateTime": "2025-06-20T15:29:42.71956+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_OverheadCostCenters": {"TableName": "Airport_OverheadCostCenters", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.659287+08:00", "LastUpdateTime": "2025-06-20T15:29:43.726986+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppChatGroups": {"TableName": "AppChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.865435+08:00", "LastUpdateTime": "2025-06-20T15:29:44.731383+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppGroupChatBlacks": {"TableName": "AppGroupChatBlacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:44.932787+08:00", "LastUpdateTime": "2025-06-20T15:29:45.736569+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppGroupMessages": {"TableName": "AppGroupMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:45.097145+08:00", "LastUpdateTime": "2025-06-20T15:29:46.742066+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotificationDefinitionGroups": {"TableName": "AppNotificationDefinitionGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:45.180352+08:00", "LastUpdateTime": "2025-06-20T15:29:47.747208+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotificationDefinitions": {"TableName": "AppNotificationDefinitions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:45.3873+08:00", "LastUpdateTime": "2025-06-20T15:29:48.75061+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformDatas": {"TableName": "AppPlatformDatas", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:45.418965+08:00", "LastUpdateTime": "2025-06-20T15:29:49.763168+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformDataItems": {"TableName": "AppPlatformDataItems", "TotalRows": 27, "MigratedRows": 27, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:45.874943+08:00", "LastUpdateTime": "2025-06-20T15:29:50.89667+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformLayouts": {"TableName": "AppPlatformLayouts", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:45.876662+08:00", "LastUpdateTime": "2025-06-20T15:29:51.903609+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformMenus": {"TableName": "AppPlatformMenus", "TotalRows": 263, "MigratedRows": 263, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.062654+08:00", "LastUpdateTime": "2025-06-20T15:29:52.912105+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformPackages": {"TableName": "AppPlatformPackages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.062819+08:00", "LastUpdateTime": "2025-06-20T15:29:53.918419+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformPackageBlobs": {"TableName": "AppPlatformPackageBlobs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.388744+08:00", "LastUpdateTime": "2025-06-20T15:29:55.058806+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformRoleMenus": {"TableName": "AppPlatformRoleMenus", "TotalRows": 585, "MigratedRows": 585, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.428165+08:00", "LastUpdateTime": "2025-06-20T15:29:56.063013+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformUserFavoriteMenus": {"TableName": "AppPlatformUserFavoriteMenus", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.682546+08:00", "LastUpdateTime": "2025-06-20T15:29:57.067626+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformUserMenus": {"TableName": "AppPlatformUserMenus", "TotalRows": 5281, "MigratedRows": 5281, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.706633+08:00", "LastUpdateTime": "2025-06-20T15:29:58.072484+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformVersion": {"TableName": "AppPlatformVersion", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.962465+08:00", "LastUpdateTime": "2025-06-20T15:29:59.076253+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformVersionFile": {"TableName": "AppPlatformVersionFile", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:46.963614+08:00", "LastUpdateTime": "2025-06-20T15:30:00.216903+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatCards": {"TableName": "AppUserChatCards", "TotalRows": 49825, "MigratedRows": 49825, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.139547+08:00", "LastUpdateTime": "2025-06-20T15:30:01.223578+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatFriends": {"TableName": "AppUserChatFriends", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.167355+08:00", "LastUpdateTime": "2025-06-20T15:30:02.225821+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatGroups": {"TableName": "AppUserChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.350624+08:00", "LastUpdateTime": "2025-06-20T15:30:03.228576+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatSettings": {"TableName": "AppUserChatSettings", "TotalRows": 49825, "MigratedRows": 49825, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.435565+08:00", "LastUpdateTime": "2025-06-20T15:30:04.232145+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserGroupCards": {"TableName": "AppUserGroupCards", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.672175+08:00", "LastUpdateTime": "2025-06-20T15:30:05.236071+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserMessages": {"TableName": "AppUserMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.721703+08:00", "LastUpdateTime": "2025-06-20T15:30:06.241182+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_BaseInfoStatistics": {"TableName": "Bank_Ledger_BaseInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.913832+08:00", "LastUpdateTime": "2025-06-20T15:30:07.246059+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_ResettleHelpEducates": {"TableName": "Bank_Ledger_ResettleHelpEducates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:47.929308+08:00", "LastUpdateTime": "2025-06-20T15:30:08.252193+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_RiskDetermines": {"TableName": "Bank_Ledger_RiskDetermines", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:48.164741+08:00", "LastUpdateTime": "2025-06-20T15:30:09.254925+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_ServeSentenceAndCriminalInfoStatistics": {"TableName": "Bank_Ledger_ServeSentenceAndCriminalInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:48.196871+08:00", "LastUpdateTime": "2025-06-20T15:30:10.260232+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "BigScreenAuditDatas": {"TableName": "BigScreenAuditDatas", "TotalRows": 39, "MigratedRows": 39, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:48.388162+08:00", "LastUpdateTime": "2025-06-20T15:30:11.265236+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "BSPUser": {"TableName": "BSPUser", "TotalRows": 455368, "MigratedRows": 455368, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:48.521899+08:00", "LastUpdateTime": "2025-06-20T15:30:12.272988+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "dept_new_zhong": {"TableName": "dept_new_zhong", "TotalRows": 194, "MigratedRows": 194, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:48.755152+08:00", "LastUpdateTime": "2025-06-20T15:30:13.278161+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "excelbookuser": {"TableName": "excelbookuser", "TotalRows": 1858, "MigratedRows": 1858, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:48.801988+08:00", "LastUpdateTime": "2025-06-20T15:30:14.283891+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "excepbook": {"TableName": "excepbook", "TotalRows": 92, "MigratedRows": 92, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.031162+08:00", "LastUpdateTime": "2025-06-20T15:30:15.289322+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "ExistingTables": {"TableName": "ExistingTables", "TotalRows": 8073, "MigratedRows": 8073, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.050001+08:00", "LastUpdateTime": "2025-06-20T15:30:16.292874+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "fill_user_book": {"TableName": "fill_user_book", "TotalRows": 205200, "MigratedRows": 205200, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.24551+08:00", "LastUpdateTime": "2025-06-20T15:30:17.300346+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_AreaOrganizationUnits": {"TableName": "Filling_AreaOrganizationUnits", "TotalRows": 98986, "MigratedRows": 98986, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.245875+08:00", "LastUpdateTime": "2025-06-20T15:30:18.44239+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_CollaborativeReports": {"TableName": "Filling_CollaborativeReports", "TotalRows": 1111, "MigratedRows": 1111, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.426202+08:00", "LastUpdateTime": "2025-06-20T15:30:19.445965+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_Staff": {"TableName": "Filling_Staff", "TotalRows": 483643, "MigratedRows": 483643, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.488228+08:00", "LastUpdateTime": "2025-06-20T15:30:20.46479+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTasks": {"TableName": "Filling_PlanTasks", "TotalRows": 3627, "MigratedRows": 3627, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.655999+08:00", "LastUpdateTime": "2025-06-20T15:30:21.821953+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTasks": {"TableName": "Filling_ReportTasks", "TotalRows": 4883, "MigratedRows": 4883, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:49.789267+08:00", "LastUpdateTime": "2025-06-20T15:30:23.020257+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_FileInfos": {"TableName": "Filling_FileInfos", "TotalRows": 1570, "MigratedRows": 1570, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.033394+08:00", "LastUpdateTime": "2025-06-20T15:30:24.230115+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnits": {"TableName": "Filling_ReportTaskAreaOrganizationUnits", "TotalRows": 1911, "MigratedRows": 1911, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.233858+08:00", "LastUpdateTime": "2025-06-20T15:30:25.658582+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTables": {"TableName": "Filling_ReportTables", "TotalRows": 1997, "MigratedRows": 1997, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.430868+08:00", "LastUpdateTime": "2025-06-20T15:30:26.789625+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_FillingConfigs": {"TableName": "Filling_FillingConfigs", "TotalRows": 33, "MigratedRows": 33, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.688189+08:00", "LastUpdateTime": "2025-06-20T15:30:27.999827+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskAreaOrganizationUnits": {"TableName": "Filling_PlanTaskAreaOrganizationUnits", "TotalRows": 14241, "MigratedRows": 14241, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.704341+08:00", "LastUpdateTime": "2025-06-20T15:30:29.120113+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTableTemplates": {"TableName": "Filling_ReportTableTemplates", "TotalRows": 4819, "MigratedRows": 4819, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.953277+08:00", "LastUpdateTime": "2025-06-20T15:30:30.128203+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskReportTableTemplates": {"TableName": "Filling_PlanTaskReportTableTemplates", "TotalRows": 3832, "MigratedRows": 3832, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:50.980776+08:00", "LastUpdateTime": "2025-06-20T15:30:31.366043+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskStaffAreaOrganizationUnits": {"TableName": "Filling_PlanTaskStaffAreaOrganizationUnits", "TotalRows": 217, "MigratedRows": 217, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.147194+08:00", "LastUpdateTime": "2025-06-20T15:30:32.567812+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskStaffs": {"TableName": "Filling_PlanTaskStaffs", "TotalRows": 1913, "MigratedRows": 1913, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.201522+08:00", "LastUpdateTime": "2025-06-20T15:30:33.765742+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportMessageInfos": {"TableName": "Filling_ReportMessageInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.442623+08:00", "LastUpdateTime": "2025-06-20T15:30:34.77214+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTableRows": {"TableName": "Filling_ReportTableRows", "TotalRows": 2042, "MigratedRows": 2042, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.4611+08:00", "LastUpdateTime": "2025-06-20T15:30:35.902246+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnitAudits": {"TableName": "Filling_ReportTaskAreaOrganizationUnitAudits", "TotalRows": 957, "MigratedRows": 957, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.645252+08:00", "LastUpdateTime": "2025-06-20T15:30:37.040237+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnitFillers": {"TableName": "Filling_ReportTaskAreaOrganizationUnitFillers", "TotalRows": 1067, "MigratedRows": 1067, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.649146+08:00", "LastUpdateTime": "2025-06-20T15:30:38.274351+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_StaffPlanTaskHides": {"TableName": "Filling_StaffPlanTaskHides", "TotalRows": 865, "MigratedRows": 865, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.819451+08:00", "LastUpdateTime": "2025-06-20T15:30:39.469525+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableLedgerRuleConfigs": {"TableName": "Filling_TableLedgerRuleConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:51.883386+08:00", "LastUpdateTime": "2025-06-20T15:30:40.662422+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableTemplateColumns": {"TableName": "Filling_TableTemplateColumns", "TotalRows": 175494, "MigratedRows": 175494, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.108379+08:00", "LastUpdateTime": "2025-06-20T15:30:41.800299+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableTemplateDataRows": {"TableName": "Filling_TableTemplateDataRows", "TotalRows": 370, "MigratedRows": 370, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.108818+08:00", "LastUpdateTime": "2025-06-20T15:30:42.936933+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_YkzOrgUnits": {"TableName": "Filling_YkzOrgUnits", "TotalRows": 97762, "MigratedRows": 97762, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.302318+08:00", "LastUpdateTime": "2025-06-20T15:30:43.943296+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "hg_t_audit_log": {"TableName": "hg_t_audit_log", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.339365+08:00", "LastUpdateTime": "2025-06-20T15:30:44.94606+08:00", "Status": "Completed", "ErrorMessage": "获取表行数失败: 获取表hg_t_audit_log的总行数在3次尝试后操作仍然失败。最后一次错误: XX000: permission denied ", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResources": {"TableName": "IdentityServerApiResources", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.609708+08:00", "LastUpdateTime": "2025-06-20T15:30:45.964499+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceClaims": {"TableName": "IdentityServerApiResourceClaims", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.649685+08:00", "LastUpdateTime": "2025-06-20T15:30:47.084051+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceProperties": {"TableName": "IdentityServerApiResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.823831+08:00", "LastUpdateTime": "2025-06-20T15:30:48.21235+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceScopes": {"TableName": "IdentityServerApiResourceScopes", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:52.914315+08:00", "LastUpdateTime": "2025-06-20T15:30:49.336468+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceSecrets": {"TableName": "IdentityServerApiResourceSecrets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.29606+08:00", "LastUpdateTime": "2025-06-20T15:30:50.490881+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopes": {"TableName": "IdentityServerApiScopes", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.303262+08:00", "LastUpdateTime": "2025-06-20T15:30:51.496314+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopeClaims": {"TableName": "IdentityServerApiScopeClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.478939+08:00", "LastUpdateTime": "2025-06-20T15:30:52.62241+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopeProperties": {"TableName": "IdentityServerApiScopeProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.479339+08:00", "LastUpdateTime": "2025-06-20T15:30:53.780923+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClients": {"TableName": "IdentityServerClients", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.660804+08:00", "LastUpdateTime": "2025-06-20T15:30:54.786291+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientClaims": {"TableName": "IdentityServerClientClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.740493+08:00", "LastUpdateTime": "2025-06-20T15:30:55.924387+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientCorsOrigins": {"TableName": "IdentityServerClientCorsOrigins", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.9655+08:00", "LastUpdateTime": "2025-06-20T15:30:57.052094+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientGrantTypes": {"TableName": "IdentityServerClientGrantTypes", "TotalRows": 19, "MigratedRows": 19, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:53.965712+08:00", "LastUpdateTime": "2025-06-20T15:30:58.207961+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientIdPRestrictions": {"TableName": "IdentityServerClientIdPRestrictions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.253713+08:00", "LastUpdateTime": "2025-06-20T15:30:59.333922+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientPostLogoutRedirectUris": {"TableName": "IdentityServerClientPostLogoutRedirectUris", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.267851+08:00", "LastUpdateTime": "2025-06-20T15:31:00.500839+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientProperties": {"TableName": "IdentityServerClientProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.426515+08:00", "LastUpdateTime": "2025-06-20T15:31:01.648575+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientRedirectUris": {"TableName": "IdentityServerClientRedirectUris", "TotalRows": 12, "MigratedRows": 12, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.471142+08:00", "LastUpdateTime": "2025-06-20T15:31:02.813248+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientScopes": {"TableName": "IdentityServerClientScopes", "TotalRows": 58, "MigratedRows": 58, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.643761+08:00", "LastUpdateTime": "2025-06-20T15:31:03.937456+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientSecrets": {"TableName": "IdentityServerClientSecrets", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.667339+08:00", "LastUpdateTime": "2025-06-20T15:31:05.118624+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerDeviceFlowCodes": {"TableName": "IdentityServerDeviceFlowCodes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.853034+08:00", "LastUpdateTime": "2025-06-20T15:31:06.121078+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResources": {"TableName": "IdentityServerIdentityResources", "TotalRows": 8, "MigratedRows": 8, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:54.853364+08:00", "LastUpdateTime": "2025-06-20T15:31:07.126516+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResourceClaims": {"TableName": "IdentityServerIdentityResourceClaims", "TotalRows": 34, "MigratedRows": 34, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.027139+08:00", "LastUpdateTime": "2025-06-20T15:31:08.289435+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResourceProperties": {"TableName": "IdentityServerIdentityResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.086435+08:00", "LastUpdateTime": "2025-06-20T15:31:09.428228+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerPersistedGrants": {"TableName": "IdentityServerPersistedGrants", "TotalRows": 1012, "MigratedRows": 1012, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.311267+08:00", "LastUpdateTime": "2025-06-20T15:31:10.435887+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AjcBaseInfoStatistics": {"TableName": "Ledger_AjcBaseInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.316172+08:00", "LastUpdateTime": "2025-06-20T15:31:11.442258+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldGroups": {"TableName": "Ledger_TableFieldGroups", "TotalRows": 7016, "MigratedRows": 7016, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.491498+08:00", "LastUpdateTime": "2025-06-20T15:31:12.663219+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataSources": {"TableName": "Ledger_DataSources", "TotalRows": 26, "MigratedRows": 26, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.541311+08:00", "LastUpdateTime": "2025-06-20T15:31:13.670275+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceDbTableInfos": {"TableName": "Ledger_SourceDbTableInfos", "TotalRows": 926, "MigratedRows": 926, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.740431+08:00", "LastUpdateTime": "2025-06-20T15:31:14.814008+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DbTableFields": {"TableName": "Ledger_DbTableFields", "TotalRows": 23219, "MigratedRows": 23219, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:55.776359+08:00", "LastUpdateTime": "2025-06-20T15:31:15.960778+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSets": {"TableName": "Ledger_TableDataSets", "TotalRows": 145, "MigratedRows": 145, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.013929+08:00", "LastUpdateTime": "2025-06-20T15:31:17.10347+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetMappingFields": {"TableName": "Ledger_TableDataSetMappingFields", "TotalRows": 3575, "MigratedRows": 3575, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.014171+08:00", "LastUpdateTime": "2025-06-20T15:31:18.328146+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFields": {"TableName": "Ledger_TableFields", "TotalRows": 290241, "MigratedRows": 290241, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.200826+08:00", "LastUpdateTime": "2025-06-20T15:31:19.836353+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableInfos": {"TableName": "Ledger_TableInfos", "TotalRows": 9036, "MigratedRows": 9036, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.201139+08:00", "LastUpdateTime": "2025-06-20T15:31:21.046528+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Departments_error": {"TableName": "Platform_Departments_error", "TotalRows": 219108, "MigratedRows": 219108, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.453799+08:00", "LastUpdateTime": "2025-06-20T15:31:22.474694+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Departments": {"TableName": "Platform_Departments", "TotalRows": 258980, "MigratedRows": 258980, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.454043+08:00", "LastUpdateTime": "2025-06-20T15:31:35.538681+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerTypes": {"TableName": "Ledger_LedgerTypes", "TotalRows": 1259, "MigratedRows": 1259, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.680902+08:00", "LastUpdateTime": "2025-06-20T15:31:36.752847+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_Ledgers": {"TableName": "Ledger_Ledgers", "TotalRows": 8403, "MigratedRows": 8403, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.710326+08:00", "LastUpdateTime": "2025-06-20T15:31:38.127122+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillSources": {"TableName": "Ledger_ApiAuxiliaryFillSources", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.956478+08:00", "LastUpdateTime": "2025-06-20T15:31:39.132985+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillingRules": {"TableName": "Ledger_ApiAuxiliaryFillingRules", "TotalRows": 179, "MigratedRows": 179, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:56.988773+08:00", "LastUpdateTime": "2025-06-20T15:31:40.430383+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillings": {"TableName": "Ledger_ApiAuxiliaryFillings", "TotalRows": 53, "MigratedRows": 53, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:57.195931+08:00", "LastUpdateTime": "2025-06-20T15:31:41.437167+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiDataSets": {"TableName": "Ledger_ApiDataSets", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:57.318485+08:00", "LastUpdateTime": "2025-06-20T15:31:42.570936+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiDataSetMappingFields": {"TableName": "Ledger_ApiDataSetMappingFields", "TotalRows": 278, "MigratedRows": 278, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:57.480778+08:00", "LastUpdateTime": "2025-06-20T15:31:43.718034+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiRequestParameters": {"TableName": "Ledger_ApiRequestParameters", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:57.53583+08:00", "LastUpdateTime": "2025-06-20T15:31:44.840478+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataAnalyses": {"TableName": "Ledger_LedgerDataAnalyses", "TotalRows": 539, "MigratedRows": 539, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:57.721509+08:00", "LastUpdateTime": "2025-06-20T15:31:46.043138+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedConfigs": {"TableName": "Ledger_AssociatedConfigs", "TotalRows": 215, "MigratedRows": 215, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:57.780896+08:00", "LastUpdateTime": "2025-06-20T15:31:47.407964+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedSyns": {"TableName": "Ledger_AssociatedSyns", "TotalRows": 43, "MigratedRows": 43, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:58.014231+08:00", "LastUpdateTime": "2025-06-20T15:31:48.619427+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedSynConfigs": {"TableName": "Ledger_AssociatedSynConfigs", "TotalRows": 296, "MigratedRows": 296, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:58.041506+08:00", "LastUpdateTime": "2025-06-20T15:31:50.690759+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BaseInfoStatistics": {"TableName": "Ledger_BaseInfoStatistics", "TotalRows": 1072, "MigratedRows": 1072, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:58.279877+08:00", "LastUpdateTime": "2025-06-20T15:31:51.833347+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BigViewTypicalScenarios": {"TableName": "Ledger_BigViewTypicalScenarios", "TotalRows": 44, "MigratedRows": 44, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T15:21:58.323233+08:00", "LastUpdateTime": "2025-06-20T15:31:52.966252+08:00", "Status": "Completed", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Users": {"TableName": "Platform_Users", "TotalRows": 483649, "MigratedRows": 297000, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:58.654581+08:00", "LastUpdateTime": "2025-06-20T15:32:39.54399+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BigViewTypicalScenarioUploadRecords": {"TableName": "Ledger_BigViewTypicalScenarioUploadRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:58.654881+08:00", "LastUpdateTime": "2025-06-20T15:29:56.371876+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_CommonGoalsTasks": {"TableName": "Ledger_CommonGoalsTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:58.933225+08:00", "LastUpdateTime": "2025-06-20T15:29:56.421978+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataItemsManagements": {"TableName": "Ledger_DataItemsManagements", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:58.936439+08:00", "LastUpdateTime": "2025-06-20T15:29:56.448468+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataItemsUpdateRecords": {"TableName": "Ledger_DataItemsUpdateRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.177505+08:00", "LastUpdateTime": "2025-06-20T15:29:56.472189+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluates": {"TableName": "Ledger_StarEvaluates", "TotalRows": 41, "MigratedRows": 41, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.205943+08:00", "LastUpdateTime": "2025-06-20T15:29:56.75403+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPoints": {"TableName": "Ledger_DeductPoints", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.379727+08:00", "LastUpdateTime": "2025-06-20T15:29:56.845158+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluatePublishes": {"TableName": "Ledger_StarEvaluatePublishes", "TotalRows": 779, "MigratedRows": 779, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.381132+08:00", "LastUpdateTime": "2025-06-20T15:29:58.296389+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPointsPublishes": {"TableName": "Ledger_DeductPointsPublishes", "TotalRows": 61, "MigratedRows": 61, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.578523+08:00", "LastUpdateTime": "2025-06-20T15:29:56.969523+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HistoryEvaluates": {"TableName": "Ledger_HistoryEvaluates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.655336+08:00", "LastUpdateTime": "2025-06-20T15:29:56.985268+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluateRecords": {"TableName": "Ledger_StarEvaluateRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.901104+08:00", "LastUpdateTime": "2025-06-20T15:29:56.999667+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPointsRecords": {"TableName": "Ledger_DeductPointsRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:21:59.924558+08:00", "LastUpdateTime": "2025-06-20T15:29:57.01449+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_Disabilities": {"TableName": "Ledger_Disabilities", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:00.148988+08:00", "LastUpdateTime": "2025-06-20T15:29:57.031031+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgerDataPushOriginPlans": {"TableName": "Ledger_HierarchicalLedgerDataPushOriginPlans", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:00.163718+08:00", "LastUpdateTime": "2025-06-20T15:29:57.063887+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgers": {"TableName": "Ledger_HierarchicalLedgers", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:00.403583+08:00", "LastUpdateTime": "2025-06-20T15:29:57.188358+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgerTaskItems": {"TableName": "Ledger_HierarchicalLedgerTaskItems", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:00.480703+08:00", "LastUpdateTime": "2025-06-20T15:29:57.249235+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_IndicatorCockpits": {"TableName": "Ledger_IndicatorCockpits", "TotalRows": 45623, "MigratedRows": 45623, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:00.741996+08:00", "LastUpdateTime": "2025-06-20T15:31:10.790587+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_IndicatorCockpitConfigs": {"TableName": "Ledger_IndicatorCockpitConfigs", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:00.797492+08:00", "LastUpdateTime": "2025-06-20T15:29:58.443481+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatches": {"TableName": "Ledger_LedgerAuditBatches", "TotalRows": 73785, "MigratedRows": 73785, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.039165+08:00", "LastUpdateTime": "2025-06-20T15:30:58.775811+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchFlows": {"TableName": "Ledger_LedgerAuditBatchFlows", "TotalRows": 169, "MigratedRows": 169, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.03952+08:00", "LastUpdateTime": "2025-06-20T15:30:58.940442+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchFlowNodes": {"TableName": "Ledger_LedgerAuditBatchFlowNodes", "TotalRows": 397, "MigratedRows": 397, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.336042+08:00", "LastUpdateTime": "2025-06-20T15:30:59.290137+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchStatistics": {"TableName": "Ledger_LedgerAuditBatchStatistics", "TotalRows": 67421, "MigratedRows": 67421, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.336371+08:00", "LastUpdateTime": "2025-06-20T15:31:08.635014+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillings": {"TableName": "Ledger_LedgerAuxiliaryFillings", "TotalRows": 51, "MigratedRows": 51, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.569863+08:00", "LastUpdateTime": "2025-06-20T15:31:08.737402+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillingConfigs": {"TableName": "Ledger_LedgerAuxiliaryFillingConfigs", "TotalRows": 172, "MigratedRows": 172, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.600282+08:00", "LastUpdateTime": "2025-06-20T15:31:08.811174+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSetAuxiliaryFillings": {"TableName": "Ledger_LedgerDataSetAuxiliaryFillings", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.815932+08:00", "LastUpdateTime": "2025-06-20T15:31:08.872552+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillingPreviews": {"TableName": "Ledger_LedgerAuxiliaryFillingPreviews", "TotalRows": 159, "MigratedRows": 159, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:01.888795+08:00", "LastUpdateTime": "2025-06-20T15:31:08.996383+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerChangeRecords": {"TableName": "Ledger_LedgerChangeRecords", "TotalRows": 73, "MigratedRows": 73, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.12265+08:00", "LastUpdateTime": "2025-06-20T15:31:09.10789+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerChangeRecordDetails": {"TableName": "Ledger_LedgerChangeRecordDetails", "TotalRows": 135, "MigratedRows": 135, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.123076+08:00", "LastUpdateTime": "2025-06-20T15:31:09.292524+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataComparisonRecords": {"TableName": "Ledger_LedgerDataComparisonRecords", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.382899+08:00", "LastUpdateTime": "2025-06-20T15:31:09.398442+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSetAuxiliaryFillingConfigs": {"TableName": "Ledger_LedgerDataSetAuxiliaryFillingConfigs", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.389526+08:00", "LastUpdateTime": "2025-06-20T15:31:09.467968+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncConfigs": {"TableName": "Ledger_LedgerDataSyncConfigs", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.584705+08:00", "LastUpdateTime": "2025-06-20T15:31:09.545267+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncTasks": {"TableName": "Ledger_LedgerDataSyncTasks", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.652215+08:00", "LastUpdateTime": "2025-06-20T15:31:09.627599+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncFields": {"TableName": "Ledger_LedgerDataSyncFields", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.910875+08:00", "LastUpdateTime": "2025-06-20T15:31:09.698568+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartmentDataDetailStatistics": {"TableName": "Ledger_LedgerDepartmentDataDetailStatistics", "TotalRows": 86453, "MigratedRows": 86453, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:02.911487+08:00", "LastUpdateTime": "2025-06-20T15:32:08.580729+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartmentDataStatistics": {"TableName": "Ledger_LedgerDepartmentDataStatistics", "TotalRows": 1388, "MigratedRows": 1388, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.09836+08:00", "LastUpdateTime": "2025-06-20T15:31:11.238348+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartments": {"TableName": "Ledger_LedgerDepartments", "TotalRows": 168891, "MigratedRows": 113000, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.143769+08:00", "LastUpdateTime": "2025-06-20T15:32:39.353079+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerHierarchicalAuthorizations": {"TableName": "Ledger_LedgerHierarchicalAuthorizations", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.376848+08:00", "LastUpdateTime": "2025-06-20T15:31:34.20481+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerPermissionsAuthorizationModes": {"TableName": "Ledger_LedgerPermissionsAuthorizationModes", "TotalRows": 1263125, "MigratedRows": 104000, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.377169+08:00", "LastUpdateTime": "2025-06-20T15:32:39.450515+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerRunways": {"TableName": "Ledger_LedgerRunways", "TotalRows": 2642, "MigratedRows": 2642, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.546559+08:00", "LastUpdateTime": "2025-06-20T15:32:11.357676+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerRunwayRelations": {"TableName": "Ledger_LedgerRunwayRelations", "TotalRows": 44, "MigratedRows": 44, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.677044+08:00", "LastUpdateTime": "2025-06-20T15:32:11.463053+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableQueryConfigs": {"TableName": "Ledger_TableQueryConfigs", "TotalRows": 256, "MigratedRows": 256, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:03.9432+08:00", "LastUpdateTime": "2025-06-20T15:32:11.730944+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerStatistics": {"TableName": "Ledger_LedgerStatistics", "TotalRows": 183, "MigratedRows": 183, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:04.135566+08:00", "LastUpdateTime": "2025-06-20T15:32:11.896545+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerTemplates": {"TableName": "Ledger_LedgerTemplates", "TotalRows": 139, "MigratedRows": 139, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:04.351004+08:00", "LastUpdateTime": "2025-06-20T15:32:12.573655+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerUsers": {"TableName": "Ledger_LedgerUsers", "TotalRows": 1096540, "MigratedRows": 47000, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:04.490035+08:00", "LastUpdateTime": "2025-06-20T15:32:39.419315+08:00", "Status": "InProgress", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerWayAndRelations": {"TableName": "Ledger_LedgerWayAndRelations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:04.734925+08:00", "LastUpdateTime": "2025-06-20T15:22:05.227192+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LlmMessages": {"TableName": "Ledger_LlmMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:04.76124+08:00", "LastUpdateTime": "2025-06-20T15:22:05.31535+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_MyLedgerExportRecords": {"TableName": "Ledger_MyLedgerExportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:05.001716+08:00", "LastUpdateTime": "2025-06-20T15:22:05.601658+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejectPublishes": {"TableName": "Ledger_OneVoteRejectPublishes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:05.040368+08:00", "LastUpdateTime": "2025-06-20T15:22:05.814323+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejectRecords": {"TableName": "Ledger_OneVoteRejectRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:05.228052+08:00", "LastUpdateTime": "2025-06-20T15:22:06.032157+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejects": {"TableName": "Ledger_OneVoteRejects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:05.315859+08:00", "LastUpdateTime": "2025-06-20T15:22:06.240588+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OnLineAnalysisConfigs": {"TableName": "Ledger_OnLineAnalysisConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:05.602143+08:00", "LastUpdateTime": "2025-06-20T15:22:06.457233+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OnLineAnalysisEchartConfigs": {"TableName": "Ledger_OnLineAnalysisEchartConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:05.815042+08:00", "LastUpdateTime": "2025-06-20T15:22:06.652226+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_PlanProgressConfigs": {"TableName": "Ledger_PlanProgressConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:06.035732+08:00", "LastUpdateTime": "2025-06-20T15:22:06.845503+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ResettleHelpEducates": {"TableName": "Ledger_ResettleHelpEducates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:06.24155+08:00", "LastUpdateTime": "2025-06-20T15:22:07.138212+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_RiskDetermines": {"TableName": "Ledger_RiskDetermines", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:06.457896+08:00", "LastUpdateTime": "2025-06-20T15:22:07.349695+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ServeSentenceAndCriminalInfoStatistics": {"TableName": "Ledger_ServeSentenceAndCriminalInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:06.654408+08:00", "LastUpdateTime": "2025-06-20T15:22:07.556056+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SocialAssistances": {"TableName": "Ledger_SocialAssistances", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:06.846029+08:00", "LastUpdateTime": "2025-06-20T15:22:07.810312+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerTypes": {"TableName": "Ledger_SourceLedgerTypes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:07.139071+08:00", "LastUpdateTime": "2025-06-20T15:22:07.937528+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerFillStatistics": {"TableName": "Ledger_SourceLedgerFillStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:07.350603+08:00", "LastUpdateTime": "2025-06-20T15:22:08.079338+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerTypeRelations": {"TableName": "Ledger_SourceLedgerTypeRelations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:07.556544+08:00", "LastUpdateTime": "2025-06-20T15:22:08.140072+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StreetBigDataEchartAnalyses": {"TableName": "Ledger_StreetBigDataEchartAnalyses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:07.811383+08:00", "LastUpdateTime": "2025-06-20T15:22:08.325182+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StreetBigDataEchartAnalysisConfigs": {"TableName": "Ledger_StreetBigDataEchartAnalysisConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:07.938115+08:00", "LastUpdateTime": "2025-06-20T15:22:08.388688+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataExportRecords": {"TableName": "Ledger_TableDataExportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.080279+08:00", "LastUpdateTime": "2025-06-20T15:22:08.620434+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataImportRecords": {"TableName": "Ledger_TableDataImportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.140633+08:00", "LastUpdateTime": "2025-06-20T15:22:08.681498+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetSourceDbTableInfos": {"TableName": "Ledger_TableDataSetSourceDbTableInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.32574+08:00", "LastUpdateTime": "2025-06-20T15:22:08.874902+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetJoinConfigs": {"TableName": "Ledger_TableDataSetJoinConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.389681+08:00", "LastUpdateTime": "2025-06-20T15:22:08.892314+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetJoinFieldConfigs": {"TableName": "Ledger_TableDataSetJoinFieldConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.621268+08:00", "LastUpdateTime": "2025-06-20T15:22:09.092161+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldCalculateRules": {"TableName": "Ledger_TableFieldCalculateRules", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.682456+08:00", "LastUpdateTime": "2025-06-20T15:22:09.091592+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldClientSettings": {"TableName": "Ledger_TableFieldClientSettings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.87766+08:00", "LastUpdateTime": "2025-06-20T15:22:09.259437+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldMultiples": {"TableName": "Ledger_TableFieldMultiples", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:08.892681+08:00", "LastUpdateTime": "2025-06-20T15:22:09.315054+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFiledValidateRules": {"TableName": "Ledger_TableFiledValidateRules", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.092525+08:00", "LastUpdateTime": "2025-06-20T15:22:09.605539+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TelecomTaskItems": {"TableName": "Ledger_TelecomTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.092873+08:00", "LastUpdateTime": "2025-06-20T15:22:09.55493+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TaskChargePeople": {"TableName": "Ledger_TaskChargePeople", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.260243+08:00", "LastUpdateTime": "2025-06-20T15:22:09.852536+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_AuthorityAudits": {"TableName": "NewFeature_AuthorityAudits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.315585+08:00", "LastUpdateTime": "2025-06-20T15:22:09.846076+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_AutomatedVerifications": {"TableName": "NewFeature_AutomatedVerifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.555728+08:00", "LastUpdateTime": "2025-06-20T15:22:10.076548+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_ProgressManagements": {"TableName": "NewFeature_ProgressManagements", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.606022+08:00", "LastUpdateTime": "2025-06-20T15:22:10.128842+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_ProgressManagementItems": {"TableName": "NewFeature_ProgressManagementItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.846913+08:00", "LastUpdateTime": "2025-06-20T15:22:10.396676+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_DataProcesses": {"TableName": "NewFeature_DataProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:09.854734+08:00", "LastUpdateTime": "2025-06-20T15:22:10.42624+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_FunctionLogs": {"TableName": "NewFeature_FunctionLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.077271+08:00", "LastUpdateTime": "2025-06-20T15:22:10.615904+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartGroups": {"TableName": "Platform_ChartGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.129209+08:00", "LastUpdateTime": "2025-06-20T15:22:10.611309+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Charts": {"TableName": "Platform_Charts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.398505+08:00", "LastUpdateTime": "2025-06-20T15:22:10.812701+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartDicDatas": {"TableName": "Platform_ChartDicDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.429698+08:00", "LastUpdateTime": "2025-06-20T15:22:10.892592+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartListDatas": {"TableName": "Platform_ChartListDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.612061+08:00", "LastUpdateTime": "2025-06-20T15:22:11.113814+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartTimeFlowDatas": {"TableName": "Platform_ChartTimeFlowDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.617337+08:00", "LastUpdateTime": "2025-06-20T15:22:11.114286+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentFavoriteGroups": {"TableName": "Platform_DepartmentFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.813524+08:00", "LastUpdateTime": "2025-06-20T15:22:11.299219+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentFavorites": {"TableName": "Platform_DepartmentFavorites", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:10.893553+08:00", "LastUpdateTime": "2025-06-20T15:22:11.333189+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentGroups": {"TableName": "Platform_DepartmentGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.114709+08:00", "LastUpdateTime": "2025-06-20T15:22:11.498602+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentGroupDepartments": {"TableName": "Platform_DepartmentGroupDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.115477+08:00", "LastUpdateTime": "2025-06-20T15:22:11.529673+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Regions": {"TableName": "Platform_Regions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.300616+08:00", "LastUpdateTime": "2025-06-20T15:22:11.767891+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_QuestionFeedbacks": {"TableName": "Platform_QuestionFeedbacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.333745+08:00", "LastUpdateTime": "2025-06-20T15:22:11.816453+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_RegionFavoriteGroups": {"TableName": "Platform_RegionFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.49936+08:00", "LastUpdateTime": "2025-06-20T15:22:12.003894+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_RegionFavorites": {"TableName": "Platform_RegionFavorites", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.530297+08:00", "LastUpdateTime": "2025-06-20T15:22:12.056188+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ReportLedgerNotices": {"TableName": "Platform_ReportLedgerNotices", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.768865+08:00", "LastUpdateTime": "2025-06-20T15:22:12.365872+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Routes": {"TableName": "Platform_Routes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:11.817279+08:00", "LastUpdateTime": "2025-06-20T15:22:12.349957+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_SyncRecords": {"TableName": "Platform_SyncRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.004871+08:00", "LastUpdateTime": "2025-06-20T15:22:12.586252+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartmentFavoriteGroups": {"TableName": "Platform_UserDepartmentFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.056886+08:00", "LastUpdateTime": "2025-06-20T15:22:12.587315+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartmentRoles": {"TableName": "Platform_UserDepartmentRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.35072+08:00", "LastUpdateTime": "2025-06-20T15:22:12.77255+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartments": {"TableName": "Platform_UserDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.366519+08:00", "LastUpdateTime": "2025-06-20T15:22:12.847061+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WebWidgets": {"TableName": "Platform_WebWidgets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.589694+08:00", "LastUpdateTime": "2025-06-20T15:22:13.116719+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFPlanTasks": {"TableName": "Platform_WFPlanTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.59013+08:00", "LastUpdateTime": "2025-06-20T15:22:13.138309+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFTaskItems": {"TableName": "Platform_WFTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.777949+08:00", "LastUpdateTime": "2025-06-20T15:22:13.369177+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFDataProcesses": {"TableName": "Platform_WFDataProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:12.847736+08:00", "LastUpdateTime": "2025-06-20T15:22:13.410858+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WorkToDoRecords": {"TableName": "Platform_WorkToDoRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.117461+08:00", "LastUpdateTime": "2025-06-20T15:22:13.655511+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YbtUsers": {"TableName": "Platform_YbtUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.139014+08:00", "LastUpdateTime": "2025-06-20T15:22:13.654722+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YkzOrganizations": {"TableName": "Platform_YkzOrganizations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.370072+08:00", "LastUpdateTime": "2025-06-20T15:22:13.896051+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YkzOrgUnits": {"TableName": "Platform_YkzOrgUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.411683+08:00", "LastUpdateTime": "2025-06-20T15:22:13.895165+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_Applications": {"TableName": "RPA_Applications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.655878+08:00", "LastUpdateTime": "2025-06-20T15:22:14.151742+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ApplicationDepartments": {"TableName": "RPA_ApplicationDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.656255+08:00", "LastUpdateTime": "2025-06-20T15:22:14.148093+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ApplicationUsers": {"TableName": "RPA_ApplicationUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.898156+08:00", "LastUpdateTime": "2025-06-20T15:22:14.330296+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_AppTasks": {"TableName": "RPA_AppTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:13.898607+08:00", "LastUpdateTime": "2025-06-20T15:22:14.482546+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_AppTaskItems": {"TableName": "RPA_AppTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:14.156101+08:00", "LastUpdateTime": "2025-06-20T15:22:14.912907+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_DataSources": {"TableName": "RPA_DataSources", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:14.157041+08:00", "LastUpdateTime": "2025-06-20T15:22:14.71838+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_DataFieldMappings": {"TableName": "RPA_DataFieldMappings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:14.331471+08:00", "LastUpdateTime": "2025-06-20T15:22:15.048866+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ProcessServices": {"TableName": "RPA_ProcessServices", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:14.48344+08:00", "LastUpdateTime": "2025-06-20T15:22:15.19095+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ProcessFields": {"TableName": "RPA_ProcessFields", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:14.719492+08:00", "LastUpdateTime": "2025-06-20T15:22:15.274089+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "SrcOrgs": {"TableName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:14.913698+08:00", "LastUpdateTime": "2025-06-20T15:22:15.481694+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TableInfosToCheck": {"TableName": "TableInfosToCheck", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.049616+08:00", "LastUpdateTime": "2025-06-20T15:22:15.503084+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Batch": {"TableName": "Te<PERSON>_Batch", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.191414+08:00", "LastUpdateTime": "2025-06-20T15:22:15.668881+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Fill_Fix": {"TableName": "Temp_Ledger_Department_Fill_Fix", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.274985+08:00", "LastUpdateTime": "2025-06-20T15:22:15.742265+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Operation_Count": {"TableName": "Temp_Ledger_Department_Operation_Count", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.48233+08:00", "LastUpdateTime": "2025-06-20T15:22:15.946223+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Operation_Count_NewV1": {"TableName": "Temp_Ledger_Department_Operation_Count_NewV1", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.503876+08:00", "LastUpdateTime": "2025-06-20T15:22:15.966993+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TempOrgs": {"TableName": "TempOrgs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.669918+08:00", "LastUpdateTime": "2025-06-20T15:22:16.174485+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TempUsers": {"TableName": "TempUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.743904+08:00", "LastUpdateTime": "2025-06-20T15:22:16.19695+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Test_DEC": {"TableName": "Test_DEC", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.946813+08:00", "LastUpdateTime": "2025-06-20T15:22:16.402066+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TK_BackgroundJobLogs": {"TableName": "TK_BackgroundJobLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:15.967531+08:00", "LastUpdateTime": "2025-06-20T15:22:16.439342+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TK_BackgroundJobs": {"TableName": "TK_Background<PERSON><PERSON>s", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.175481+08:00", "LastUpdateTime": "2025-06-20T15:22:16.704954+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "UserLoginRecords": {"TableName": "UserLoginRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.199994+08:00", "LastUpdateTime": "2025-06-20T15:22:16.679729+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowTasks": {"TableName": "WF_WorkflowTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.40275+08:00", "LastUpdateTime": "2025-06-20T15:22:16.921049+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeInfos": {"TableName": "WF_WorkflowSchemeInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.443782+08:00", "LastUpdateTime": "2025-06-20T15:22:16.970773+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemes": {"TableName": "WF_WorkflowSchemes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.680551+08:00", "LastUpdateTime": "2025-06-20T15:22:17.223766+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowProcesses": {"TableName": "WF_WorkflowProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.705734+08:00", "LastUpdateTime": "2025-06-20T15:22:17.286751+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeAuths": {"TableName": "WF_WorkflowSchemeAuths", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.926895+08:00", "LastUpdateTime": "2025-06-20T15:22:17.470082+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeInfoPermissionGrants": {"TableName": "WF_WorkflowSchemeInfoPermissionGrants", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:16.971863+08:00", "LastUpdateTime": "2025-06-20T15:22:17.51672+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowStamps": {"TableName": "WF_WorkflowStamps", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:17.227334+08:00", "LastUpdateTime": "2025-06-20T15:22:17.790039+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowTaskLogs": {"TableName": "WF_WorkflowTaskLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:17.287166+08:00", "LastUpdateTime": "2025-06-20T15:22:17.801636+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowUnits": {"TableName": "WF_WorkflowUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T15:22:17.471113+08:00", "LastUpdateTime": "2025-06-20T15:22:18.065968+08:00", "Status": "NotStarted", "ErrorMessage": null, "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}}, "ErrorMessage": "表 hg_t_audit_log 迁移失败: 获取表行数失败: 获取表hg_t_audit_log的总行数在3次尝试后操作仍然失败。最后一次错误: XX000: permission denied "}