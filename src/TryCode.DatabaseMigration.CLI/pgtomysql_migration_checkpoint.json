{"MigrationId": "c7b38e34-7035-44b5-b564-dcfb312e3944", "SourceType": "MySQL", "TargetType": "MySQL", "StartTime": "2025-06-20T17:56:28.48222+08:00", "LastUpdateTime": "2025-06-20T18:21:21.486464+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 11, "TotalRows": 1265433, "MigratedRows": 180892, "TableCheckpoints": {"AbpAuditLogActions": {"TableName": "AbpAuditLogActions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.541311+08:00", "LastUpdateTime": "2025-06-20T18:21:22.274348+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpAuditLogs": {"TableName": "AbpAuditLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.591001+08:00", "LastUpdateTime": "2025-06-20T18:21:22.274525+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityChanges": {"TableName": "AbpEntityChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.616187+08:00", "LastUpdateTime": "2025-06-20T18:21:22.274813+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityPropertyChanges": {"TableName": "AbpEntityPropertyChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.642376+08:00", "LastUpdateTime": "2025-06-20T18:21:22.275009+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSecurityLogs": {"TableName": "AbpSecurityLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.643984+08:00", "LastUpdateTime": "2025-06-20T18:21:22.275188+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserClaims": {"TableName": "AbpUserClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.645219+08:00", "LastUpdateTime": "2025-06-20T18:21:22.275397+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotifications": {"TableName": "AppNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.646552+08:00", "LastUpdateTime": "2025-06-20T18:21:22.275604+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserNotifications": {"TableName": "AppUserNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.648262+08:00", "LastUpdateTime": "2025-06-20T18:21:22.275769+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserSubscribes": {"TableName": "AppUserSubscribes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.649754+08:00", "LastUpdateTime": "2025-06-20T18:21:22.275922+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataUpdateLogs": {"TableName": "Ledger_LedgerDataUpdateLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.676247+08:00", "LastUpdateTime": "2025-06-20T18:21:22.276074+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartments": {"TableName": "Ledger_LedgerDepartments", "TotalRows": 168892, "MigratedRows": 168892, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T17:56:37.724581+08:00", "LastUpdateTime": "2025-06-20T18:21:22.273833+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerUsers": {"TableName": "Ledger_LedgerUsers", "TotalRows": 1096541, "MigratedRows": 12000, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T18:20:34.501597+08:00", "LastUpdateTime": "2025-06-20T18:21:22.274147+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}}, "ErrorMessage": "表 Ledger_LedgerDataUpdateLogs 迁移失败: 表 Ledger_LedgerUsers 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。"}