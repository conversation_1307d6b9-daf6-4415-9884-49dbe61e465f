{"MigrationId": "2676776e-d9db-4341-9c21-302d30c5f06a", "SourceType": "MySQL", "TargetType": "MySQL", "StartTime": "2025-06-20T16:02:14.932744+08:00", "LastUpdateTime": "2025-06-20T16:43:39.292246+08:00", "Status": "Failed", "TotalTables": 0, "CompletedTables": 177, "TotalRows": 6165469, "MigratedRows": 6051577, "TableCheckpoints": {"AbpAuditLogActions": {"TableName": "AbpAuditLogActions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.756221+08:00", "LastUpdateTime": "2025-06-20T16:43:39.520338+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpAuditLogs": {"TableName": "AbpAuditLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.768939+08:00", "LastUpdateTime": "2025-06-20T16:43:39.520864+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityChanges": {"TableName": "AbpEntityChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.769483+08:00", "LastUpdateTime": "2025-06-20T16:43:39.521422+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEntityPropertyChanges": {"TableName": "AbpEntityPropertyChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.770014+08:00", "LastUpdateTime": "2025-06-20T16:43:39.522+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSecurityLogs": {"TableName": "AbpSecurityLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.770373+08:00", "LastUpdateTime": "2025-06-20T16:43:39.522535+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserClaims": {"TableName": "AbpUserClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.770671+08:00", "LastUpdateTime": "2025-06-20T16:43:39.523601+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotifications": {"TableName": "AppNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.770956+08:00", "LastUpdateTime": "2025-06-20T16:43:39.52416+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserNotifications": {"TableName": "AppUserNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.771284+08:00", "LastUpdateTime": "2025-06-20T16:43:39.524964+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserSubscribes": {"TableName": "AppUserSubscribes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.771648+08:00", "LastUpdateTime": "2025-06-20T16:43:39.526083+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataUpdateLogs": {"TableName": "Ledger_LedgerDataUpdateLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:23.773897+08:00", "LastUpdateTime": "2025-06-20T16:43:39.526769+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": true, "IsPartitioned": false, "PartitionCheckpoints": {}}, "__EFMigrationsHistory": {"TableName": "__EFMigrationsHistory", "TotalRows": 391, "MigratedRows": 391, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:23.798021+08:00", "LastUpdateTime": "2025-06-20T16:43:39.309236+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpClaimTypes": {"TableName": "AbpClaimTypes", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:23.799126+08:00", "LastUpdateTime": "2025-06-20T16:43:39.311011+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpEditions": {"TableName": "AbpEditions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:23.799608+08:00", "LastUpdateTime": "2025-06-20T16:43:39.312625+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpFeatureValues": {"TableName": "AbpFeature<PERSON><PERSON>ues", "TotalRows": 24, "MigratedRows": 24, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:23.800051+08:00", "LastUpdateTime": "2025-06-20T16:43:39.314178+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLinkUsers": {"TableName": "AbpLinkUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:24.451788+08:00", "LastUpdateTime": "2025-06-20T16:43:39.315734+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationLanguages": {"TableName": "AbpLocalizationLanguages", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:24.658728+08:00", "LastUpdateTime": "2025-06-20T16:43:39.317465+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationResources": {"TableName": "AbpLocalizationResources", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:24.707206+08:00", "LastUpdateTime": "2025-06-20T16:43:39.325498+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpLocalizationTexts": {"TableName": "AbpLocalizationTexts", "TotalRows": 20, "MigratedRows": 20, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:24.707767+08:00", "LastUpdateTime": "2025-06-20T16:43:39.326927+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpRoles": {"TableName": "AbpRoles", "TotalRows": 18, "MigratedRows": 18, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:24.889893+08:00", "LastUpdateTime": "2025-06-20T16:43:39.331906+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpOrganizationUnits": {"TableName": "AbpOrganizationUnits", "TotalRows": 10, "MigratedRows": 10, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.084239+08:00", "LastUpdateTime": "2025-06-20T16:43:39.329168+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpOrganizationUnitRoles": {"TableName": "AbpOrganizationUnitRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.084485+08:00", "LastUpdateTime": "2025-06-20T16:43:39.328031+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpPermissionGrants": {"TableName": "AbpPermissionGrants", "TotalRows": 5696, "MigratedRows": 5696, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.138238+08:00", "LastUpdateTime": "2025-06-20T16:43:39.330141+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpRoleClaims": {"TableName": "AbpRoleClaims", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.350289+08:00", "LastUpdateTime": "2025-06-20T16:43:39.331127+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpSettings": {"TableName": "AbpSettings", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.603292+08:00", "LastUpdateTime": "2025-06-20T16:43:39.332993+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpTenants": {"TableName": "AbpTenants", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.603541+08:00", "LastUpdateTime": "2025-06-20T16:43:39.335232+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpTenantConnectionStrings": {"TableName": "AbpTenantConnectionStrings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.640664+08:00", "LastUpdateTime": "2025-06-20T16:43:39.334109+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUsers": {"TableName": "AbpUsers", "TotalRows": 483309, "MigratedRows": 483309, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.815267+08:00", "LastUpdateTime": "2025-06-20T16:43:39.341099+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserLogins": {"TableName": "AbpUserLogins", "TotalRows": 269, "MigratedRows": 269, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:25.99504+08:00", "LastUpdateTime": "2025-06-20T16:43:39.336373+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserOrganizationUnits": {"TableName": "AbpUserOrganizationUnits", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.08063+08:00", "LastUpdateTime": "2025-06-20T16:43:39.337787+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserRoles": {"TableName": "AbpUserRoles", "TotalRows": 523722, "MigratedRows": 523722, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.087088+08:00", "LastUpdateTime": "2025-06-20T16:43:39.338533+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserRoles_923bf": {"TableName": "AbpUserRoles_923bf", "TotalRows": 455580, "MigratedRows": 455580, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.315589+08:00", "LastUpdateTime": "2025-06-20T16:43:39.33929+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpUserTokens": {"TableName": "AbpUserTokens", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.516816+08:00", "LastUpdateTime": "2025-06-20T16:43:39.342919+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksEvents": {"TableName": "AbpWebhooksEvents", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.525708+08:00", "LastUpdateTime": "2025-06-20T16:43:39.34413+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksSendAttempts": {"TableName": "AbpWebhooksSendAttempts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.543022+08:00", "LastUpdateTime": "2025-06-20T16:43:39.345009+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AbpWebhooksSubscriptions": {"TableName": "AbpWebhooksSubscriptions", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.73215+08:00", "LastUpdateTime": "2025-06-20T16:43:39.345714+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_AssetLiabilities": {"TableName": "Airport_AssetLiabilities", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.99865+08:00", "LastUpdateTime": "2025-06-20T16:43:39.346714+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_BudgetAccounts": {"TableName": "Airport_BudgetAccounts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:26.999142+08:00", "LastUpdateTime": "2025-06-20T16:43:39.347425+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_BudgetOrganizations": {"TableName": "Airport_BudgetOrganizations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.040586+08:00", "LastUpdateTime": "2025-06-20T16:43:39.34817+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceAuditBatches": {"TableName": "Airport_FinanceAuditBatches", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.289362+08:00", "LastUpdateTime": "2025-06-20T16:43:39.348832+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceAuditedDatas": {"TableName": "Airport_FinanceAuditedDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.484513+08:00", "LastUpdateTime": "2025-06-20T16:43:39.349775+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_FinanceProjects": {"TableName": "Airport_FinanceProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.515115+08:00", "LastUpdateTime": "2025-06-20T16:43:39.350339+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_NoFinanceProjects": {"TableName": "Airport_NoFinanceProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.515325+08:00", "LastUpdateTime": "2025-06-20T16:43:39.351298+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_NoFinancialBudgetProjects": {"TableName": "Airport_NoFinancialBudgetProjects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.749933+08:00", "LastUpdateTime": "2025-06-20T16:43:39.35187+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Airport_OverheadCostCenters": {"TableName": "Airport_OverheadCostCenters", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:27.977643+08:00", "LastUpdateTime": "2025-06-20T16:43:39.352482+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppChatGroups": {"TableName": "AppChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:28.009437+08:00", "LastUpdateTime": "2025-06-20T16:43:39.353079+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppGroupChatBlacks": {"TableName": "AppGroupChatBlacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:28.013161+08:00", "LastUpdateTime": "2025-06-20T16:43:39.353681+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppGroupMessages": {"TableName": "AppGroupMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:28.177469+08:00", "LastUpdateTime": "2025-06-20T16:43:39.354364+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotificationDefinitionGroups": {"TableName": "AppNotificationDefinitionGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:28.437966+08:00", "LastUpdateTime": "2025-06-20T16:43:39.354926+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppNotificationDefinitions": {"TableName": "AppNotificationDefinitions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:28.647366+08:00", "LastUpdateTime": "2025-06-20T16:43:39.355497+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformDatas": {"TableName": "AppPlatformDatas", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:28.923918+08:00", "LastUpdateTime": "2025-06-20T16:43:39.356588+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformDataItems": {"TableName": "AppPlatformDataItems", "TotalRows": 27, "MigratedRows": 27, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:29.143479+08:00", "LastUpdateTime": "2025-06-20T16:43:39.356067+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformLayouts": {"TableName": "AppPlatformLayouts", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:29.333399+08:00", "LastUpdateTime": "2025-06-20T16:43:39.357475+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformMenus": {"TableName": "AppPlatformMenus", "TotalRows": 263, "MigratedRows": 263, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:29.508884+08:00", "LastUpdateTime": "2025-06-20T16:43:39.358017+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformPackages": {"TableName": "AppPlatformPackages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:29.764536+08:00", "LastUpdateTime": "2025-06-20T16:43:39.359497+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformPackageBlobs": {"TableName": "AppPlatformPackageBlobs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:29.993247+08:00", "LastUpdateTime": "2025-06-20T16:43:39.358966+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformRoleMenus": {"TableName": "AppPlatformRoleMenus", "TotalRows": 585, "MigratedRows": 585, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:30.276925+08:00", "LastUpdateTime": "2025-06-20T16:43:39.360145+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformUserFavoriteMenus": {"TableName": "AppPlatformUserFavoriteMenus", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:30.516612+08:00", "LastUpdateTime": "2025-06-20T16:43:39.360682+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformUserMenus": {"TableName": "AppPlatformUserMenus", "TotalRows": 5281, "MigratedRows": 5281, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:30.741494+08:00", "LastUpdateTime": "2025-06-20T16:43:39.361355+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformVersion": {"TableName": "AppPlatformVersion", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:30.948581+08:00", "LastUpdateTime": "2025-06-20T16:43:39.362559+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppPlatformVersionFile": {"TableName": "AppPlatformVersionFile", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.077825+08:00", "LastUpdateTime": "2025-06-20T16:43:39.364083+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatCards": {"TableName": "AppUserChatCards", "TotalRows": 49825, "MigratedRows": 49825, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.240124+08:00", "LastUpdateTime": "2025-06-20T16:43:39.365451+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatFriends": {"TableName": "AppUserChatFriends", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.302674+08:00", "LastUpdateTime": "2025-06-20T16:43:39.366569+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatGroups": {"TableName": "AppUserChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.537136+08:00", "LastUpdateTime": "2025-06-20T16:43:39.368012+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserChatSettings": {"TableName": "AppUserChatSettings", "TotalRows": 49825, "MigratedRows": 49825, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.580582+08:00", "LastUpdateTime": "2025-06-20T16:43:39.369334+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserGroupCards": {"TableName": "AppUserGroupCards", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.835963+08:00", "LastUpdateTime": "2025-06-20T16:43:39.372743+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "AppUserMessages": {"TableName": "AppUserMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:31.843871+08:00", "LastUpdateTime": "2025-06-20T16:43:39.374003+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_BaseInfoStatistics": {"TableName": "Bank_Ledger_BaseInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.108424+08:00", "LastUpdateTime": "2025-06-20T16:43:39.375189+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_ResettleHelpEducates": {"TableName": "Bank_Ledger_ResettleHelpEducates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.108609+08:00", "LastUpdateTime": "2025-06-20T16:43:39.376448+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_RiskDetermines": {"TableName": "Bank_Ledger_RiskDetermines", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.388981+08:00", "LastUpdateTime": "2025-06-20T16:43:39.376912+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Bank_Ledger_ServeSentenceAndCriminalInfoStatistics": {"TableName": "Bank_Ledger_ServeSentenceAndCriminalInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.389162+08:00", "LastUpdateTime": "2025-06-20T16:43:39.377672+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "BigScreenAuditDatas": {"TableName": "BigScreenAuditDatas", "TotalRows": 39, "MigratedRows": 39, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.672261+08:00", "LastUpdateTime": "2025-06-20T16:43:39.378097+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "BSPUser": {"TableName": "BSPUser", "TotalRows": 455368, "MigratedRows": 455368, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.721066+08:00", "LastUpdateTime": "2025-06-20T16:43:39.37854+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "dept_new_zhong": {"TableName": "dept_new_zhong", "TotalRows": 194, "MigratedRows": 194, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.958699+08:00", "LastUpdateTime": "2025-06-20T16:43:39.37901+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "excelbookuser": {"TableName": "excelbookuser", "TotalRows": 1858, "MigratedRows": 1858, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:32.959325+08:00", "LastUpdateTime": "2025-06-20T16:43:39.379839+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "excepbook": {"TableName": "excepbook", "TotalRows": 92, "MigratedRows": 92, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.175508+08:00", "LastUpdateTime": "2025-06-20T16:43:39.380324+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "ExistingTables": {"TableName": "ExistingTables", "TotalRows": 8073, "MigratedRows": 8073, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.18528+08:00", "LastUpdateTime": "2025-06-20T16:43:39.380889+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "fill_user_book": {"TableName": "fill_user_book", "TotalRows": 205200, "MigratedRows": 205200, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.36682+08:00", "LastUpdateTime": "2025-06-20T16:43:39.381344+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_AreaOrganizationUnits": {"TableName": "Filling_AreaOrganizationUnits", "TotalRows": 98986, "MigratedRows": 98986, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.397385+08:00", "LastUpdateTime": "2025-06-20T16:43:39.381784+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_CollaborativeReports": {"TableName": "Filling_CollaborativeReports", "TotalRows": 1111, "MigratedRows": 1111, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.571388+08:00", "LastUpdateTime": "2025-06-20T16:43:39.382196+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_Staff": {"TableName": "Filling_Staff", "TotalRows": 483643, "MigratedRows": 483643, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.621749+08:00", "LastUpdateTime": "2025-06-20T16:43:39.396612+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTasks": {"TableName": "Filling_PlanTasks", "TotalRows": 3627, "MigratedRows": 3627, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.787271+08:00", "LastUpdateTime": "2025-06-20T16:43:39.387006+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTasks": {"TableName": "Filling_ReportTasks", "TotalRows": 4883, "MigratedRows": 4883, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:33.882091+08:00", "LastUpdateTime": "2025-06-20T16:43:39.39485+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_FileInfos": {"TableName": "Filling_FileInfos", "TotalRows": 1570, "MigratedRows": 1570, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:34.109329+08:00", "LastUpdateTime": "2025-06-20T16:43:39.382627+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnits": {"TableName": "Filling_ReportTaskAreaOrganizationUnits", "TotalRows": 1911, "MigratedRows": 1911, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:34.146786+08:00", "LastUpdateTime": "2025-06-20T16:43:39.393241+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTables": {"TableName": "Filling_ReportTables", "TotalRows": 1997, "MigratedRows": 1997, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:34.480349+08:00", "LastUpdateTime": "2025-06-20T16:43:39.389255+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_FillingConfigs": {"TableName": "Filling_FillingConfigs", "TotalRows": 33, "MigratedRows": 33, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:34.502534+08:00", "LastUpdateTime": "2025-06-20T16:43:39.38303+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskAreaOrganizationUnits": {"TableName": "Filling_PlanTaskAreaOrganizationUnits", "TotalRows": 14241, "MigratedRows": 14241, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:34.739617+08:00", "LastUpdateTime": "2025-06-20T16:43:39.38379+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTableTemplates": {"TableName": "Filling_ReportTableTemplates", "TotalRows": 4819, "MigratedRows": 4819, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:34.788223+08:00", "LastUpdateTime": "2025-06-20T16:43:39.389669+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskReportTableTemplates": {"TableName": "Filling_PlanTaskReportTableTemplates", "TotalRows": 3832, "MigratedRows": 3832, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.053218+08:00", "LastUpdateTime": "2025-06-20T16:43:39.384713+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskStaffAreaOrganizationUnits": {"TableName": "Filling_PlanTaskStaffAreaOrganizationUnits", "TotalRows": 217, "MigratedRows": 217, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.094601+08:00", "LastUpdateTime": "2025-06-20T16:43:39.387585+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_PlanTaskStaffs": {"TableName": "Filling_PlanTaskStaffs", "TotalRows": 1913, "MigratedRows": 1913, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.328569+08:00", "LastUpdateTime": "2025-06-20T16:43:39.387998+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportMessageInfos": {"TableName": "Filling_ReportMessageInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.340266+08:00", "LastUpdateTime": "2025-06-20T16:43:39.388437+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTableRows": {"TableName": "Filling_ReportTableRows", "TotalRows": 2042, "MigratedRows": 2042, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.494373+08:00", "LastUpdateTime": "2025-06-20T16:43:39.388853+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnitAudits": {"TableName": "Filling_ReportTaskAreaOrganizationUnitAudits", "TotalRows": 957, "MigratedRows": 957, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.533729+08:00", "LastUpdateTime": "2025-06-20T16:43:39.390883+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_ReportTaskAreaOrganizationUnitFillers": {"TableName": "Filling_ReportTaskAreaOrganizationUnitFillers", "TotalRows": 1067, "MigratedRows": 1067, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.789411+08:00", "LastUpdateTime": "2025-06-20T16:43:39.391875+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_StaffPlanTaskHides": {"TableName": "Filling_StaffPlanTaskHides", "TotalRows": 865, "MigratedRows": 865, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:35.838009+08:00", "LastUpdateTime": "2025-06-20T16:43:39.397171+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableLedgerRuleConfigs": {"TableName": "Filling_TableLedgerRuleConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.05041+08:00", "LastUpdateTime": "2025-06-20T16:43:39.398111+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableTemplateColumns": {"TableName": "Filling_TableTemplateColumns", "TotalRows": 175494, "MigratedRows": 175494, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.050602+08:00", "LastUpdateTime": "2025-06-20T16:43:39.398548+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_TableTemplateDataRows": {"TableName": "Filling_TableTemplateDataRows", "TotalRows": 370, "MigratedRows": 370, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.229165+08:00", "LastUpdateTime": "2025-06-20T16:43:39.398981+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Filling_YkzOrgUnits": {"TableName": "Filling_YkzOrgUnits", "TotalRows": 97762, "MigratedRows": 97762, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.231118+08:00", "LastUpdateTime": "2025-06-20T16:43:39.399764+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "hg_t_audit_log": {"TableName": "hg_t_audit_log", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.501782+08:00", "LastUpdateTime": "2025-06-20T16:43:39.40014+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResources": {"TableName": "IdentityServerApiResources", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.507188+08:00", "LastUpdateTime": "2025-06-20T16:43:39.401977+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceClaims": {"TableName": "IdentityServerApiResourceClaims", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.685944+08:00", "LastUpdateTime": "2025-06-20T16:43:39.40111+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceProperties": {"TableName": "IdentityServerApiResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:36.756532+08:00", "LastUpdateTime": "2025-06-20T16:43:39.401548+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceScopes": {"TableName": "IdentityServerApiResourceScopes", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.011767+08:00", "LastUpdateTime": "2025-06-20T16:43:39.402394+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiResourceSecrets": {"TableName": "IdentityServerApiResourceSecrets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.072993+08:00", "LastUpdateTime": "2025-06-20T16:43:39.402787+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopes": {"TableName": "IdentityServerApiScopes", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.232687+08:00", "LastUpdateTime": "2025-06-20T16:43:39.404694+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopeClaims": {"TableName": "IdentityServerApiScopeClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.274447+08:00", "LastUpdateTime": "2025-06-20T16:43:39.403562+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerApiScopeProperties": {"TableName": "IdentityServerApiScopeProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.440574+08:00", "LastUpdateTime": "2025-06-20T16:43:39.404001+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClients": {"TableName": "IdentityServerClients", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.489621+08:00", "LastUpdateTime": "2025-06-20T16:43:39.408567+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientClaims": {"TableName": "IdentityServerClientClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.712505+08:00", "LastUpdateTime": "2025-06-20T16:43:39.405107+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientCorsOrigins": {"TableName": "IdentityServerClientCorsOrigins", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.713788+08:00", "LastUpdateTime": "2025-06-20T16:43:39.405519+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientGrantTypes": {"TableName": "IdentityServerClientGrantTypes", "TotalRows": 19, "MigratedRows": 19, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:37.892019+08:00", "LastUpdateTime": "2025-06-20T16:43:39.406208+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientIdPRestrictions": {"TableName": "IdentityServerClientIdPRestrictions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.006462+08:00", "LastUpdateTime": "2025-06-20T16:43:39.406603+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientPostLogoutRedirectUris": {"TableName": "IdentityServerClientPostLogoutRedirectUris", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.181361+08:00", "LastUpdateTime": "2025-06-20T16:43:39.407032+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientProperties": {"TableName": "IdentityServerClientProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.218962+08:00", "LastUpdateTime": "2025-06-20T16:43:39.40773+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientRedirectUris": {"TableName": "IdentityServerClientRedirectUris", "TotalRows": 12, "MigratedRows": 12, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.367758+08:00", "LastUpdateTime": "2025-06-20T16:43:39.408155+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientScopes": {"TableName": "IdentityServerClientScopes", "TotalRows": 58, "MigratedRows": 58, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.417493+08:00", "LastUpdateTime": "2025-06-20T16:43:39.409004+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerClientSecrets": {"TableName": "IdentityServerClientSecrets", "TotalRows": 13, "MigratedRows": 13, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.586716+08:00", "LastUpdateTime": "2025-06-20T16:43:39.409444+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerDeviceFlowCodes": {"TableName": "IdentityServerDeviceFlowCodes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.589975+08:00", "LastUpdateTime": "2025-06-20T16:43:39.40989+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResources": {"TableName": "IdentityServerIdentityResources", "TotalRows": 8, "MigratedRows": 8, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.747515+08:00", "LastUpdateTime": "2025-06-20T16:43:39.411179+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResourceClaims": {"TableName": "IdentityServerIdentityResourceClaims", "TotalRows": 34, "MigratedRows": 34, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:38.811768+08:00", "LastUpdateTime": "2025-06-20T16:43:39.410328+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerIdentityResourceProperties": {"TableName": "IdentityServerIdentityResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.058501+08:00", "LastUpdateTime": "2025-06-20T16:43:39.410758+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "IdentityServerPersistedGrants": {"TableName": "IdentityServerPersistedGrants", "TotalRows": 1023, "MigratedRows": 1023, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.062698+08:00", "LastUpdateTime": "2025-06-20T16:43:39.411758+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AjcBaseInfoStatistics": {"TableName": "Ledger_AjcBaseInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.30029+08:00", "LastUpdateTime": "2025-06-20T16:43:39.412176+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldGroups": {"TableName": "Ledger_TableFieldGroups", "TotalRows": 7017, "MigratedRows": 7017, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.300614+08:00", "LastUpdateTime": "2025-06-20T16:43:39.468876+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataSources": {"TableName": "Ledger_DataSources", "TotalRows": 26, "MigratedRows": 26, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.506351+08:00", "LastUpdateTime": "2025-06-20T16:43:39.421256+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceDbTableInfos": {"TableName": "Ledger_SourceDbTableInfos", "TotalRows": 926, "MigratedRows": 926, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.555091+08:00", "LastUpdateTime": "2025-06-20T16:43:39.449703+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DbTableFields": {"TableName": "Ledger_DbTableFields", "TotalRows": 23219, "MigratedRows": 23219, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.819509+08:00", "LastUpdateTime": "2025-06-20T16:43:39.421993+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSets": {"TableName": "Ledger_TableDataSets", "TotalRows": 145, "MigratedRows": 145, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:39.822618+08:00", "LastUpdateTime": "2025-06-20T16:43:39.463216+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetMappingFields": {"TableName": "Ledger_TableDataSetMappingFields", "TotalRows": 3575, "MigratedRows": 3575, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.033181+08:00", "LastUpdateTime": "2025-06-20T16:43:39.460289+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFields": {"TableName": "Ledger_TableFields", "TotalRows": 290271, "MigratedRows": 290271, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.034775+08:00", "LastUpdateTime": "2025-06-20T16:43:39.470034+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableInfos": {"TableName": "Ledger_TableInfos", "TotalRows": 9037, "MigratedRows": 9037, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.226515+08:00", "LastUpdateTime": "2025-06-20T16:43:39.471562+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Departments_error": {"TableName": "Platform_Departments_error", "TotalRows": 219108, "MigratedRows": 219108, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.351306+08:00", "LastUpdateTime": "2025-06-20T16:43:39.483429+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Departments": {"TableName": "Platform_Departments", "TotalRows": 258980, "MigratedRows": 258980, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.652741+08:00", "LastUpdateTime": "2025-06-20T16:43:39.482954+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerTypes": {"TableName": "Ledger_LedgerTypes", "TotalRows": 1259, "MigratedRows": 1259, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.652998+08:00", "LastUpdateTime": "2025-06-20T16:43:39.441601+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_Ledgers": {"TableName": "Ledger_Ledgers", "TotalRows": 8404, "MigratedRows": 8404, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.879892+08:00", "LastUpdateTime": "2025-06-20T16:43:39.440162+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillSources": {"TableName": "Ledger_ApiAuxiliaryFillSources", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:40.903781+08:00", "LastUpdateTime": "2025-06-20T16:43:39.414014+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillingRules": {"TableName": "Ledger_ApiAuxiliaryFillingRules", "TotalRows": 179, "MigratedRows": 179, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.093374+08:00", "LastUpdateTime": "2025-06-20T16:43:39.412585+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiAuxiliaryFillings": {"TableName": "Ledger_ApiAuxiliaryFillings", "TotalRows": 53, "MigratedRows": 53, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.180154+08:00", "LastUpdateTime": "2025-06-20T16:43:39.413292+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiDataSets": {"TableName": "Ledger_ApiDataSets", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.368625+08:00", "LastUpdateTime": "2025-06-20T16:43:39.415022+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiDataSetMappingFields": {"TableName": "Ledger_ApiDataSetMappingFields", "TotalRows": 278, "MigratedRows": 278, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.461664+08:00", "LastUpdateTime": "2025-06-20T16:43:39.414421+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ApiRequestParameters": {"TableName": "Ledger_ApiRequestParameters", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.640915+08:00", "LastUpdateTime": "2025-06-20T16:43:39.415448+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataAnalyses": {"TableName": "Ledger_LedgerDataAnalyses", "TotalRows": 539, "MigratedRows": 539, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.647435+08:00", "LastUpdateTime": "2025-06-20T16:43:39.432409+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedConfigs": {"TableName": "Ledger_AssociatedConfigs", "TotalRows": 215, "MigratedRows": 215, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.894997+08:00", "LastUpdateTime": "2025-06-20T16:43:39.41589+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedSyns": {"TableName": "Ledger_AssociatedSyns", "TotalRows": 43, "MigratedRows": 43, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:41.897671+08:00", "LastUpdateTime": "2025-06-20T16:43:39.416895+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_AssociatedSynConfigs": {"TableName": "Ledger_AssociatedSynConfigs", "TotalRows": 296, "MigratedRows": 296, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.095051+08:00", "LastUpdateTime": "2025-06-20T16:43:39.416486+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BaseInfoStatistics": {"TableName": "Ledger_BaseInfoStatistics", "TotalRows": 1072, "MigratedRows": 1072, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.169595+08:00", "LastUpdateTime": "2025-06-20T16:43:39.417345+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BigViewTypicalScenarios": {"TableName": "Ledger_BigViewTypicalScenarios", "TotalRows": 44, "MigratedRows": 44, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.40785+08:00", "LastUpdateTime": "2025-06-20T16:43:39.417774+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Users": {"TableName": "Platform_Users", "TotalRows": 483649, "MigratedRows": 483649, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.408225+08:00", "LastUpdateTime": "2025-06-20T16:43:39.489476+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_BigViewTypicalScenarioUploadRecords": {"TableName": "Ledger_BigViewTypicalScenarioUploadRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.596491+08:00", "LastUpdateTime": "2025-06-20T16:43:39.418203+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_CommonGoalsTasks": {"TableName": "Ledger_CommonGoalsTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.641139+08:00", "LastUpdateTime": "2025-06-20T16:43:39.418636+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataItemsManagements": {"TableName": "Ledger_DataItemsManagements", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.894969+08:00", "LastUpdateTime": "2025-06-20T16:43:39.419518+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DataItemsUpdateRecords": {"TableName": "Ledger_DataItemsUpdateRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:42.897173+08:00", "LastUpdateTime": "2025-06-20T16:43:39.420291+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluates": {"TableName": "Ledger_StarEvaluates", "TotalRows": 41, "MigratedRows": 41, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.077676+08:00", "LastUpdateTime": "2025-06-20T16:43:39.455027+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPoints": {"TableName": "Ledger_DeductPoints", "TotalRows": 11, "MigratedRows": 11, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.125549+08:00", "LastUpdateTime": "2025-06-20T16:43:39.422933+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluatePublishes": {"TableName": "Ledger_StarEvaluatePublishes", "TotalRows": 779, "MigratedRows": 779, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.33089+08:00", "LastUpdateTime": "2025-06-20T16:43:39.452003+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPointsPublishes": {"TableName": "Ledger_DeductPointsPublishes", "TotalRows": 61, "MigratedRows": 61, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.338082+08:00", "LastUpdateTime": "2025-06-20T16:43:39.423634+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HistoryEvaluates": {"TableName": "Ledger_HistoryEvaluates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.542648+08:00", "LastUpdateTime": "2025-06-20T16:43:39.426667+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StarEvaluateRecords": {"TableName": "Ledger_StarEvaluateRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.610653+08:00", "LastUpdateTime": "2025-06-20T16:43:39.452646+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_DeductPointsRecords": {"TableName": "Ledger_DeductPointsRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.844865+08:00", "LastUpdateTime": "2025-06-20T16:43:39.424084+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_Disabilities": {"TableName": "Ledger_Disabilities", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:43.886979+08:00", "LastUpdateTime": "2025-06-20T16:43:39.42457+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgerDataPushOriginPlans": {"TableName": "Ledger_HierarchicalLedgerDataPushOriginPlans", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.097731+08:00", "LastUpdateTime": "2025-06-20T16:43:39.425031+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgers": {"TableName": "Ledger_HierarchicalLedgers", "TotalRows": 3, "MigratedRows": 3, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.177988+08:00", "LastUpdateTime": "2025-06-20T16:43:39.425749+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_HierarchicalLedgerTaskItems": {"TableName": "Ledger_HierarchicalLedgerTaskItems", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.41943+08:00", "LastUpdateTime": "2025-06-20T16:43:39.426215+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_IndicatorCockpits": {"TableName": "Ledger_IndicatorCockpits", "TotalRows": 45623, "MigratedRows": 45623, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.475219+08:00", "LastUpdateTime": "2025-06-20T16:43:39.427636+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_IndicatorCockpitConfigs": {"TableName": "Ledger_IndicatorCockpitConfigs", "TotalRows": 35, "MigratedRows": 35, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.669115+08:00", "LastUpdateTime": "2025-06-20T16:43:39.427134+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatches": {"TableName": "Ledger_LedgerAuditBatches", "TotalRows": 73785, "MigratedRows": 73785, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.70296+08:00", "LastUpdateTime": "2025-06-20T16:43:39.428118+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchFlows": {"TableName": "Ledger_LedgerAuditBatchFlows", "TotalRows": 169, "MigratedRows": 169, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:44.867704+08:00", "LastUpdateTime": "2025-06-20T16:43:39.429047+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchFlowNodes": {"TableName": "Ledger_LedgerAuditBatchFlowNodes", "TotalRows": 397, "MigratedRows": 397, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.018161+08:00", "LastUpdateTime": "2025-06-20T16:43:39.42859+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuditBatchStatistics": {"TableName": "Ledger_LedgerAuditBatchStatistics", "TotalRows": 67421, "MigratedRows": 67421, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.278521+08:00", "LastUpdateTime": "2025-06-20T16:43:39.429661+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillings": {"TableName": "Ledger_LedgerAuxiliaryFillings", "TotalRows": 51, "MigratedRows": 51, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.283568+08:00", "LastUpdateTime": "2025-06-20T16:43:39.43106+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillingConfigs": {"TableName": "Ledger_LedgerAuxiliaryFillingConfigs", "TotalRows": 172, "MigratedRows": 172, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.319084+08:00", "LastUpdateTime": "2025-06-20T16:43:39.430151+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSetAuxiliaryFillings": {"TableName": "Ledger_LedgerDataSetAuxiliaryFillings", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.829246+08:00", "LastUpdateTime": "2025-06-20T16:43:39.434229+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerAuxiliaryFillingPreviews": {"TableName": "Ledger_LedgerAuxiliaryFillingPreviews", "TotalRows": 159, "MigratedRows": 159, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.926981+08:00", "LastUpdateTime": "2025-06-20T16:43:39.430591+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerChangeRecords": {"TableName": "Ledger_LedgerChangeRecords", "TotalRows": 73, "MigratedRows": 73, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.940394+08:00", "LastUpdateTime": "2025-06-20T16:43:39.431971+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerChangeRecordDetails": {"TableName": "Ledger_LedgerChangeRecordDetails", "TotalRows": 135, "MigratedRows": 135, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:45.983518+08:00", "LastUpdateTime": "2025-06-20T16:43:39.431533+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataComparisonRecords": {"TableName": "Ledger_LedgerDataComparisonRecords", "TotalRows": 38, "MigratedRows": 38, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:46.269406+08:00", "LastUpdateTime": "2025-06-20T16:43:39.432902+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSetAuxiliaryFillingConfigs": {"TableName": "Ledger_LedgerDataSetAuxiliaryFillingConfigs", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:46.45983+08:00", "LastUpdateTime": "2025-06-20T16:43:39.433616+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncConfigs": {"TableName": "Ledger_LedgerDataSyncConfigs", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:46.519248+08:00", "LastUpdateTime": "2025-06-20T16:43:39.434706+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncTasks": {"TableName": "Ledger_LedgerDataSyncTasks", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:46.536667+08:00", "LastUpdateTime": "2025-06-20T16:43:39.43575+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDataSyncFields": {"TableName": "Ledger_LedgerDataSyncFields", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:46.754727+08:00", "LastUpdateTime": "2025-06-20T16:43:39.435155+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartmentDataDetailStatistics": {"TableName": "Ledger_LedgerDepartmentDataDetailStatistics", "TotalRows": 86453, "MigratedRows": 86453, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:46.91789+08:00", "LastUpdateTime": "2025-06-20T16:43:39.436181+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartmentDataStatistics": {"TableName": "Ledger_LedgerDepartmentDataStatistics", "TotalRows": 1388, "MigratedRows": 1388, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:47.150658+08:00", "LastUpdateTime": "2025-06-20T16:43:39.436663+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerDepartments": {"TableName": "Ledger_LedgerDepartments", "TotalRows": 168892, "MigratedRows": 55000, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:47.318246+08:00", "LastUpdateTime": "2025-06-20T16:43:39.437132+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerHierarchicalAuthorizations": {"TableName": "Ledger_LedgerHierarchicalAuthorizations", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:47.471474+08:00", "LastUpdateTime": "2025-06-20T16:43:39.437611+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerPermissionsAuthorizationModes": {"TableName": "Ledger_LedgerPermissionsAuthorizationModes", "TotalRows": 1263126, "MigratedRows": 1263126, "SchemaCreated": true, "ForeignKeysCreated": true, "StartTime": "2025-06-20T16:02:47.593812+08:00", "LastUpdateTime": "2025-06-20T16:43:39.438787+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerRunways": {"TableName": "Ledger_LedgerRunways", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:47.767503+08:00", "LastUpdateTime": "2025-06-20T16:43:39.43971+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerRunwayRelations": {"TableName": "Ledger_LedgerRunwayRelations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:47.8697+08:00", "LastUpdateTime": "2025-06-20T16:43:39.439253+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableQueryConfigs": {"TableName": "Ledger_TableQueryConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:48.101191+08:00", "LastUpdateTime": "2025-06-20T16:43:39.472043+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerStatistics": {"TableName": "Ledger_LedgerStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:48.160972+08:00", "LastUpdateTime": "2025-06-20T16:43:39.440651+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerTemplates": {"TableName": "Ledger_LedgerTemplates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:48.466311+08:00", "LastUpdateTime": "2025-06-20T16:43:39.441103+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerUsers": {"TableName": "Ledger_LedgerUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:48.467279+08:00", "LastUpdateTime": "2025-06-20T16:43:39.442153+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LedgerWayAndRelations": {"TableName": "Ledger_LedgerWayAndRelations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:48.796858+08:00", "LastUpdateTime": "2025-06-20T16:43:39.442621+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_LlmMessages": {"TableName": "Ledger_LlmMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:48.797681+08:00", "LastUpdateTime": "2025-06-20T16:43:39.443077+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_MyLedgerExportRecords": {"TableName": "Ledger_MyLedgerExportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:49.035814+08:00", "LastUpdateTime": "2025-06-20T16:43:39.443745+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejectPublishes": {"TableName": "Ledger_OneVoteRejectPublishes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:49.065537+08:00", "LastUpdateTime": "2025-06-20T16:43:39.444426+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejectRecords": {"TableName": "Ledger_OneVoteRejectRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:49.25459+08:00", "LastUpdateTime": "2025-06-20T16:43:39.444888+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OneVoteRejects": {"TableName": "Ledger_OneVoteRejects", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:49.329669+08:00", "LastUpdateTime": "2025-06-20T16:43:39.445349+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OnLineAnalysisConfigs": {"TableName": "Ledger_OnLineAnalysisConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:49.702074+08:00", "LastUpdateTime": "2025-06-20T16:43:39.445824+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_OnLineAnalysisEchartConfigs": {"TableName": "Ledger_OnLineAnalysisEchartConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:50.096873+08:00", "LastUpdateTime": "2025-06-20T16:43:39.446792+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_PlanProgressConfigs": {"TableName": "Ledger_PlanProgressConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:50.362715+08:00", "LastUpdateTime": "2025-06-20T16:43:39.447254+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ResettleHelpEducates": {"TableName": "Ledger_ResettleHelpEducates", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:50.557944+08:00", "LastUpdateTime": "2025-06-20T16:43:39.447703+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_RiskDetermines": {"TableName": "Ledger_RiskDetermines", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:50.799552+08:00", "LastUpdateTime": "2025-06-20T16:43:39.448159+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_ServeSentenceAndCriminalInfoStatistics": {"TableName": "Ledger_ServeSentenceAndCriminalInfoStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:51.037544+08:00", "LastUpdateTime": "2025-06-20T16:43:39.44864+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SocialAssistances": {"TableName": "Ledger_SocialAssistances", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:51.293439+08:00", "LastUpdateTime": "2025-06-20T16:43:39.449189+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerTypes": {"TableName": "Ledger_SourceLedgerTypes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:51.526758+08:00", "LastUpdateTime": "2025-06-20T16:43:39.451519+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerFillStatistics": {"TableName": "Ledger_SourceLedgerFillStatistics", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:51.763222+08:00", "LastUpdateTime": "2025-06-20T16:43:39.450177+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_SourceLedgerTypeRelations": {"TableName": "Ledger_SourceLedgerTypeRelations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:51.998428+08:00", "LastUpdateTime": "2025-06-20T16:43:39.451024+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StreetBigDataEchartAnalyses": {"TableName": "Ledger_StreetBigDataEchartAnalyses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:52.191547+08:00", "LastUpdateTime": "2025-06-20T16:43:39.456449+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_StreetBigDataEchartAnalysisConfigs": {"TableName": "Ledger_StreetBigDataEchartAnalysisConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:52.352972+08:00", "LastUpdateTime": "2025-06-20T16:43:39.456952+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataExportRecords": {"TableName": "Ledger_TableDataExportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:52.607346+08:00", "LastUpdateTime": "2025-06-20T16:43:39.457411+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataImportRecords": {"TableName": "Ledger_TableDataImportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:52.7595+08:00", "LastUpdateTime": "2025-06-20T16:43:39.457939+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetSourceDbTableInfos": {"TableName": "Ledger_TableDataSetSourceDbTableInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:52.909381+08:00", "LastUpdateTime": "2025-06-20T16:43:39.465203+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetJoinConfigs": {"TableName": "Ledger_TableDataSetJoinConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:52.991569+08:00", "LastUpdateTime": "2025-06-20T16:43:39.458485+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableDataSetJoinFieldConfigs": {"TableName": "Ledger_TableDataSetJoinFieldConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.403305+08:00", "LastUpdateTime": "2025-06-20T16:43:39.459216+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldCalculateRules": {"TableName": "Ledger_TableFieldCalculateRules", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.40599+08:00", "LastUpdateTime": "2025-06-20T16:43:39.466801+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldClientSettings": {"TableName": "Ledger_TableFieldClientSettings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.568804+08:00", "LastUpdateTime": "2025-06-20T16:43:39.467682+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFieldMultiples": {"TableName": "Ledger_TableFieldMultiples", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.603311+08:00", "LastUpdateTime": "2025-06-20T16:43:39.46957+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TableFiledValidateRules": {"TableName": "Ledger_TableFiledValidateRules", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.812026+08:00", "LastUpdateTime": "2025-06-20T16:43:39.470635+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TelecomTaskItems": {"TableName": "Ledger_TelecomTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.834648+08:00", "LastUpdateTime": "2025-06-20T16:43:39.472952+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Ledger_TaskChargePeople": {"TableName": "Ledger_TaskChargePeople", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:53.983965+08:00", "LastUpdateTime": "2025-06-20T16:43:39.472497+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_AuthorityAudits": {"TableName": "NewFeature_AuthorityAudits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.071282+08:00", "LastUpdateTime": "2025-06-20T16:43:39.473397+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_AutomatedVerifications": {"TableName": "NewFeature_AutomatedVerifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.273442+08:00", "LastUpdateTime": "2025-06-20T16:43:39.473846+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_ProgressManagements": {"TableName": "NewFeature_ProgressManagements", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.386919+08:00", "LastUpdateTime": "2025-06-20T16:43:39.476144+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_ProgressManagementItems": {"TableName": "NewFeature_ProgressManagementItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.62102+08:00", "LastUpdateTime": "2025-06-20T16:43:39.475477+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_DataProcesses": {"TableName": "NewFeature_DataProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.621389+08:00", "LastUpdateTime": "2025-06-20T16:43:39.474541+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "NewFeature_FunctionLogs": {"TableName": "NewFeature_FunctionLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.828997+08:00", "LastUpdateTime": "2025-06-20T16:43:39.475007+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartGroups": {"TableName": "Platform_ChartGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:54.910042+08:00", "LastUpdateTime": "2025-06-20T16:43:39.478376+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Charts": {"TableName": "Platform_Charts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:55.159594+08:00", "LastUpdateTime": "2025-06-20T16:43:39.480168+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartDicDatas": {"TableName": "Platform_ChartDicDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:55.159994+08:00", "LastUpdateTime": "2025-06-20T16:43:39.477216+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartListDatas": {"TableName": "Platform_ChartListDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:55.520808+08:00", "LastUpdateTime": "2025-06-20T16:43:39.479439+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ChartTimeFlowDatas": {"TableName": "Platform_ChartTimeFlowDatas", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:55.527209+08:00", "LastUpdateTime": "2025-06-20T16:43:39.480639+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentFavoriteGroups": {"TableName": "Platform_DepartmentFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:55.77873+08:00", "LastUpdateTime": "2025-06-20T16:43:39.481096+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentFavorites": {"TableName": "Platform_DepartmentFavorites", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:55.896587+08:00", "LastUpdateTime": "2025-06-20T16:43:39.481549+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentGroups": {"TableName": "Platform_DepartmentGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.134268+08:00", "LastUpdateTime": "2025-06-20T16:43:39.482488+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_DepartmentGroupDepartments": {"TableName": "Platform_DepartmentGroupDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.135792+08:00", "LastUpdateTime": "2025-06-20T16:43:39.48202+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Regions": {"TableName": "Platform_Regions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.291625+08:00", "LastUpdateTime": "2025-06-20T16:43:39.485543+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_QuestionFeedbacks": {"TableName": "Platform_QuestionFeedbacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.350641+08:00", "LastUpdateTime": "2025-06-20T16:43:39.483891+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_RegionFavoriteGroups": {"TableName": "Platform_RegionFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.535867+08:00", "LastUpdateTime": "2025-06-20T16:43:39.484599+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_RegionFavorites": {"TableName": "Platform_RegionFavorites", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.536458+08:00", "LastUpdateTime": "2025-06-20T16:43:39.485062+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_ReportLedgerNotices": {"TableName": "Platform_ReportLedgerNotices", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.782252+08:00", "LastUpdateTime": "2025-06-20T16:43:39.486403+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_Routes": {"TableName": "Platform_Routes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:56.828182+08:00", "LastUpdateTime": "2025-06-20T16:43:39.486884+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_SyncRecords": {"TableName": "Platform_SyncRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.012525+08:00", "LastUpdateTime": "2025-06-20T16:43:39.487405+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartmentFavoriteGroups": {"TableName": "Platform_UserDepartmentFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.023836+08:00", "LastUpdateTime": "2025-06-20T16:43:39.488066+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartmentRoles": {"TableName": "Platform_UserDepartmentRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.304352+08:00", "LastUpdateTime": "2025-06-20T16:43:39.488532+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_UserDepartments": {"TableName": "Platform_UserDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.320597+08:00", "LastUpdateTime": "2025-06-20T16:43:39.48901+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WebWidgets": {"TableName": "Platform_WebWidgets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.533695+08:00", "LastUpdateTime": "2025-06-20T16:43:39.490136+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFPlanTasks": {"TableName": "Platform_WFPlanTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.535027+08:00", "LastUpdateTime": "2025-06-20T16:43:39.491214+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFTaskItems": {"TableName": "Platform_WFTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.696528+08:00", "LastUpdateTime": "2025-06-20T16:43:39.491673+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WFDataProcesses": {"TableName": "Platform_WFDataProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:57.763418+08:00", "LastUpdateTime": "2025-06-20T16:43:39.490744+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_WorkToDoRecords": {"TableName": "Platform_WorkToDoRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.080498+08:00", "LastUpdateTime": "2025-06-20T16:43:39.492122+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YbtUsers": {"TableName": "Platform_YbtUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.129851+08:00", "LastUpdateTime": "2025-06-20T16:43:39.492593+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YkzOrganizations": {"TableName": "Platform_YkzOrganizations", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.372793+08:00", "LastUpdateTime": "2025-06-20T16:43:39.493064+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Platform_YkzOrgUnits": {"TableName": "Platform_YkzOrgUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.428039+08:00", "LastUpdateTime": "2025-06-20T16:43:39.493535+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_Applications": {"TableName": "RPA_Applications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.643175+08:00", "LastUpdateTime": "2025-06-20T16:43:39.495091+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ApplicationDepartments": {"TableName": "RPA_ApplicationDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.691776+08:00", "LastUpdateTime": "2025-06-20T16:43:39.494626+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ApplicationUsers": {"TableName": "RPA_ApplicationUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.895455+08:00", "LastUpdateTime": "2025-06-20T16:43:39.496019+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_AppTasks": {"TableName": "RPA_AppTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:58.897499+08:00", "LastUpdateTime": "2025-06-20T16:43:39.497638+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_AppTaskItems": {"TableName": "RPA_AppTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.102847+08:00", "LastUpdateTime": "2025-06-20T16:43:39.496467+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_DataSources": {"TableName": "RPA_DataSources", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.123219+08:00", "LastUpdateTime": "2025-06-20T16:43:39.499297+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_DataFieldMappings": {"TableName": "RPA_DataFieldMappings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.341604+08:00", "LastUpdateTime": "2025-06-20T16:43:39.49865+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ProcessServices": {"TableName": "RPA_ProcessServices", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.37483+08:00", "LastUpdateTime": "2025-06-20T16:43:39.500692+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "RPA_ProcessFields": {"TableName": "RPA_ProcessFields", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.643128+08:00", "LastUpdateTime": "2025-06-20T16:43:39.500044+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "SrcOrgs": {"TableName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.64369+08:00", "LastUpdateTime": "2025-06-20T16:43:39.501173+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TableInfosToCheck": {"TableName": "TableInfosToCheck", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.837404+08:00", "LastUpdateTime": "2025-06-20T16:43:39.501643+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Batch": {"TableName": "Te<PERSON>_Batch", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:02:59.865171+08:00", "LastUpdateTime": "2025-06-20T16:43:39.502715+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Fill_Fix": {"TableName": "Temp_Ledger_Department_Fill_Fix", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.107094+08:00", "LastUpdateTime": "2025-06-20T16:43:39.50338+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Operation_Count": {"TableName": "Temp_Ledger_Department_Operation_Count", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.108761+08:00", "LastUpdateTime": "2025-06-20T16:43:39.503848+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Temp_Ledger_Department_Operation_Count_NewV1": {"TableName": "Temp_Ledger_Department_Operation_Count_NewV1", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.29103+08:00", "LastUpdateTime": "2025-06-20T16:43:39.504338+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TempOrgs": {"TableName": "TempOrgs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.338107+08:00", "LastUpdateTime": "2025-06-20T16:43:39.504866+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TempUsers": {"TableName": "TempUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.573764+08:00", "LastUpdateTime": "2025-06-20T16:43:39.505364+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "Test_DEC": {"TableName": "Test_DEC", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.579+08:00", "LastUpdateTime": "2025-06-20T16:43:39.505887+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TK_BackgroundJobLogs": {"TableName": "TK_BackgroundJobLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.781565+08:00", "LastUpdateTime": "2025-06-20T16:43:39.506791+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "TK_BackgroundJobs": {"TableName": "TK_Background<PERSON><PERSON>s", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.79977+08:00", "LastUpdateTime": "2025-06-20T16:43:39.507305+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "UserLoginRecords": {"TableName": "UserLoginRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.980081+08:00", "LastUpdateTime": "2025-06-20T16:43:39.507926+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowTasks": {"TableName": "WF_WorkflowTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:00.981238+08:00", "LastUpdateTime": "2025-06-20T16:43:39.518676+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeInfos": {"TableName": "WF_WorkflowSchemeInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:01.174919+08:00", "LastUpdateTime": "2025-06-20T16:43:39.514537+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemes": {"TableName": "WF_WorkflowSchemes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:01.26771+08:00", "LastUpdateTime": "2025-06-20T16:43:39.515406+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowProcesses": {"TableName": "WF_WorkflowProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:01.472839+08:00", "LastUpdateTime": "2025-06-20T16:43:39.509757+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeAuths": {"TableName": "WF_WorkflowSchemeAuths", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:01.514173+08:00", "LastUpdateTime": "2025-06-20T16:43:39.511564+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowSchemeInfoPermissionGrants": {"TableName": "WF_WorkflowSchemeInfoPermissionGrants", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:01.768484+08:00", "LastUpdateTime": "2025-06-20T16:43:39.513577+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowStamps": {"TableName": "WF_WorkflowStamps", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:01.838795+08:00", "LastUpdateTime": "2025-06-20T16:43:39.516788+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowTaskLogs": {"TableName": "WF_WorkflowTaskLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:02.022392+08:00", "LastUpdateTime": "2025-06-20T16:43:39.517805+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}, "WF_WorkflowUnits": {"TableName": "WF_WorkflowUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-06-20T16:03:02.091011+08:00", "LastUpdateTime": "2025-06-20T16:43:39.519616+08:00", "Status": "Failed", "ErrorMessage": "表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。", "SchemaOnly": false, "IsPartitioned": false, "PartitionCheckpoints": {}}}, "ErrorMessage": "表 Ledger_LedgerDataUpdateLogs 迁移失败: 表 Ledger_LedgerDepartments 出现主键冲突，可能是由于数据重复读取导致。建议检查ORDER BY逻辑或重新开始迁移。"}