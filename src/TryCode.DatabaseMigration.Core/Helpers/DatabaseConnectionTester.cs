using System;
using System.Data.Common;
using System.Threading.Tasks;
using Nhgdb;
using TryCode.DatabaseMigration.Core.Exceptions;

namespace TryCode.DatabaseMigration.Core.Helpers
{
    /// <summary>
    /// 数据库连接测试器
    /// </summary>
    public static class DatabaseConnectionTester
    {
        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public static async Task TestConnectionAsync(string type, string connectionString)
        {
            DbProviderFactory factory = type.ToLower() switch
            {
                "postgresql" => NhgdbFactory.Instance,
                "mysql" => MySql.Data.MySqlClient.MySqlClientFactory.Instance,
                _ => throw new ConfigurationValidationException($"不支持的数据库类型: {type}")
            };

            try
            {
                using var connection = factory.CreateConnection();
                connection.ConnectionString = connectionString;
                await connection.OpenAsync();
                
                // 执行简单查询以确保连接正常
                using var command = connection.CreateCommand();
                command.CommandText = type.ToLower() switch
                {
                    "postgresql" => "SELECT version()",
                    "mysql" => "SELECT version()",
                    _ => throw new ConfigurationValidationException($"不支持的数据库类型: {type}")
                };

                var version = await command.ExecuteScalarAsync();
                Console.WriteLine($"数据库连接成功！版本信息: {version}");
            }
            catch (Exception ex)
            {
                throw new DatabaseConnectionException($"连接到 {type} 数据库失败", ex);
            }
        }
    }
}
