using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 数据读取接口
    /// </summary>
    public interface IDataReader
    {
        /// <summary>
        /// 获取数据库中所有表的结构信息
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表结构信息列表</returns>
        Task<List<TableSchema>> GetTableSchemasAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取表的结构信息
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="excludedColumns">要排除的列名列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表结构信息</returns>
        Task<TableSchema> GetTableSchemaAsync(string tableName, IEnumerable<string>? excludedColumns = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 分页读取表数据
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="offset">偏移量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>数据行集合</returns>
        Task<IEnumerable<dynamic>> ReadTableDataAsync(string tableName, int pageSize, long offset, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取表的总行数
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表的总行数</returns>
        Task<long> GetTableRowCountAsync(string tableName, CancellationToken cancellationToken = default);

        /// <summary>
        /// 从指定分区读取数据
        /// </summary>
        /// <param name="partitionName">分区名称</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="offset">偏移量</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>数据行集合</returns>
        Task<IEnumerable<dynamic>> ReadPartitionDataAsync(string partitionName, int pageSize, long offset, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取分区的总行数
        /// </summary>
        /// <param name="partitionName">分区名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分区的总行数</returns>
        Task<long> GetPartitionRowCountAsync(string partitionName, CancellationToken cancellationToken = default);
    }
}
