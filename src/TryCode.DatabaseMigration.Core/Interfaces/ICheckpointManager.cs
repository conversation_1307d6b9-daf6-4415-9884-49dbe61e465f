using System.Threading;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Reports;
using TryCode.DatabaseMigration.Checkpoint.Models;

namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 检查点管理器接口
    /// </summary>
    public interface ICheckpointManager
    {
        /// <summary>
        /// 初始化迁移检查点
        /// </summary>
        /// <param name="totalTables">总表数</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task InitializeMigrationCheckpointAsync(
            int totalTables,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 标记表为进行中
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task MarkTableInProgressAsync(
            string tableName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新表的行数
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="totalRows">总行数</param>
        /// <param name="migratedRows">已迁移行数</param>
        /// <param name="stage">阶段</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task UpdateTableRowsAsync(
            string tableName,
            long totalRows,
            long migratedRows,
            string stage,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 标记表为已完成
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task MarkTableCompletedAsync(
            string tableName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 标记表为失败
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="error">错误信息</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task MarkTableFailedAsync(
            string tableName,
            string error,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 标记表为已跳过
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="reason">原因</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task MarkTableSkippedAsync(
            string tableName,
            string reason,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取迁移检查点
        /// </summary>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>迁移检查点</returns>
        Task<Core.Reports.MigrationCheckpoint> GetMigrationCheckpointAsync(
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 保存检查点
        /// </summary>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task SaveCheckpointAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取表的断点信息
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>表断点信息</returns>
        Task<Checkpoint.Models.TableCheckpoint> GetTableCheckpointAsync(
            string tableName,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新表的断点信息
        /// </summary>
        /// <param name="tableCheckpoint">表断点信息</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        Task UpdateTableCheckpointAsync(
            Checkpoint.Models.TableCheckpoint tableCheckpoint,
            CancellationToken cancellationToken = default);
    }
}
