using System.Collections.Generic;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 表依赖关系解析器接口
    /// </summary>
    public interface ITableDependencyResolver
    {
        /// <summary>
        /// 解析表之间的依赖关系
        /// </summary>
        /// <param name="tables">表结构列表</param>
        /// <returns>按照依赖关系排序后的表列表</returns>
        IEnumerable<TableSchema> ResolveDependencies(IEnumerable<TableSchema> tables);
    }
}
