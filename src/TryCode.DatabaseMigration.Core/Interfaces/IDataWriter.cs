using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 数据写入接口
    /// </summary>
    public interface IDataWriter
    {
        /// <summary>
        /// 创建表结构
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <param name="skipSchemaCreation">是否跳过创建表结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task CreateTableAsync(TableSchema schema, bool skipSchemaCreation = false, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量写入数据
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="data">数据行集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task BulkWriteAsync(string tableName, IEnumerable<dynamic> data, CancellationToken cancellationToken = default);

        /// <summary>
        /// 创建外键约束
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="foreignKeys">外键约束列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task CreateForeignKeysAsync(string tableName, List<ForeignKeySchema> foreignKeys, CancellationToken cancellationToken = default);
    }
}
