namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 数据库提供程序工厂接口
    /// </summary>
    public interface IDatabaseProviderFactory
    {
        /// <summary>
        /// 创建数据读取器
        /// </summary>
        /// <returns>数据读取器实例</returns>
        IDataReader CreateReader();

        /// <summary>
        /// 创建数据写入器
        /// </summary>
        /// <returns>数据写入器实例</returns>
        IDataWriter CreateWriter();

        /// <summary>
        /// 创建类型转换器
        /// </summary>
        /// <returns>类型转换器实例</returns>
        ITypeConverter CreateTypeConverter();
    }
}
