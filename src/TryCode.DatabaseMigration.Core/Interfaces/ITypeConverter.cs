using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 类型转换器接口
    /// </summary>
    public interface ITypeConverter
    {
        /// <summary>
        /// 将源数据库类型转换为目标数据库类型
        /// </summary>
        /// <param name="sourceType">源数据类型</param>
        /// <param name="sourceColumn">源列信息</param>
        /// <returns>目标数据库中对应的类型</returns>
        string ConvertDataType(string sourceType, ColumnSchema sourceColumn);

        /// <summary>
        /// 转换数据值
        /// </summary>
        /// <param name="value">源数据值</param>
        /// <param name="sourceType">源数据类型</param>
        /// <param name="targetType">目标数据类型</param>
        /// <returns>转换后的值</returns>
        Task<object> ConvertValueAsync(object value, string sourceType, string targetType);

        /// <summary>
        /// 检查是否支持特定类型的转换
        /// </summary>
        /// <param name="sourceType">源数据类型</param>
        /// <param name="targetType">目标数据类型</param>
        /// <returns>是否支持转换</returns>
        bool CanConvert(string sourceType, string targetType);

        /// <summary>
        /// 转换数据行
        /// </summary>
        /// <param name="data">源数据</param>
        /// <param name="table">表结构</param>
        /// <returns>转换后的数据</returns>
        IEnumerable<dynamic> Convert(IEnumerable<dynamic> data, TableSchema table);
    }
}
