using System;

namespace TryCode.DatabaseMigration.Core.Interfaces
{
    /// <summary>
    /// 迁移进度接口
    /// </summary>
    public interface IMigrationProgress
    {
        /// <summary>
        /// 总表数
        /// </summary>
        int TotalTables { get; }

        /// <summary>
        /// 已完成表数
        /// </summary>
        int CompletedTables { get; }

        /// <summary>
        /// 当前表名
        /// </summary>
        string CurrentTable { get; }

        /// <summary>
        /// 当前表总行数
        /// </summary>
        long CurrentTableTotalRows { get; }

        /// <summary>
        /// 当前表已处理行数
        /// </summary>
        long CurrentTableProcessedRows { get; }

        /// <summary>
        /// 开始时间
        /// </summary>
        DateTime StartTime { get; }

        /// <summary>
        /// 预计剩余时间（秒）
        /// </summary>
        double EstimatedRemainingSeconds { get; }

        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="totalRows">总行数</param>
        /// <param name="processedRows">已处理行数</param>
        void UpdateProgress(string tableName, long totalRows, long processedRows);

        /// <summary>
        /// 完成一个表的迁移
        /// </summary>
        /// <param name="tableName">表名</param>
        void CompleteTable(string tableName);

        /// <summary>
        /// 进度变更事件
        /// </summary>
        event EventHandler<MigrationProgressEventArgs> ProgressChanged;
    }

    /// <summary>
    /// 迁移进度事件参数
    /// </summary>
    public class MigrationProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 总表数
        /// </summary>
        public int TotalTables { get; set; }

        /// <summary>
        /// 已完成表数
        /// </summary>
        public int CompletedTables { get; set; }

        /// <summary>
        /// 当前表名
        /// </summary>
        public string CurrentTable { get; set; }

        /// <summary>
        /// 当前表总行数
        /// </summary>
        public long CurrentTableTotalRows { get; set; }

        /// <summary>
        /// 当前表已处理行数
        /// </summary>
        public long CurrentTableProcessedRows { get; set; }

        /// <summary>
        /// 预计剩余时间（秒）
        /// </summary>
        public double EstimatedRemainingSeconds { get; set; }
    }
}
