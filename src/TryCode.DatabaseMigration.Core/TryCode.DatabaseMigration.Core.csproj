<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.28" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.4" />
    <PackageReference Include="MySql.Data" Version="8.0.33" />
    <PackageReference Include="Nhgdb.EntityFrameworkCore.HGDB" Version="6.0.22" />
    <!--    <PackageReference Include="Npgsql" Version="6.0.10" />-->
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TryCode.DatabaseMigration.Checkpoint\TryCode.DatabaseMigration.Checkpoint.csproj" />
  </ItemGroup>

</Project>
