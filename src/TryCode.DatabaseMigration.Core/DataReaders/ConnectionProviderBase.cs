using System.Data.Common;
using TryCode.DatabaseMigration.Core.Interfaces;

namespace TryCode.DatabaseMigration.Core.DataReaders
{
    /// <summary>
    /// 数据库连接提供者基类
    /// </summary>
    public abstract class ConnectionProviderBase : IDbConnectionProvider
    {
        protected readonly string ConnectionString;

        protected ConnectionProviderBase(string connectionString)
        {
            ConnectionString = connectionString;
        }

        /// <summary>
        /// 获取数据库连接
        /// </summary>
        public abstract DbConnection GetConnection();
    }
}
