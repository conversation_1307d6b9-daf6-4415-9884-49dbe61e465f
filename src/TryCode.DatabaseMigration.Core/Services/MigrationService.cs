using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using TryCode.DatabaseMigration.Core.Interfaces;

namespace TryCode.DatabaseMigration.Core.Services
{
    /// <summary>
    /// 数据库迁移服务
    /// </summary>
    public class MigrationService
    {
        private readonly IDataReader _sourceReader;
        private readonly IDataWriter _targetWriter;
        private readonly ITypeConverter _typeConverter;
        private readonly IMigrationProgress _progress;
        private readonly ILogger<MigrationService> _logger;
        private readonly ITableDependencyResolver _dependencyResolver;
        private readonly int _batchSize;

        public MigrationService(
            IDataReader sourceReader,
            IDataWriter targetWriter,
            ITypeConverter typeConverter,
            IMigrationProgress progress,
            ILogger<MigrationService>? logger,
            ITableDependencyResolver dependencyResolver,
            int batchSize = 1000)
        {
            _sourceReader = sourceReader ?? throw new ArgumentNullException(nameof(sourceReader));
            _targetWriter = targetWriter ?? throw new ArgumentNullException(nameof(targetWriter));
            _typeConverter = typeConverter ?? throw new ArgumentNullException(nameof(typeConverter));
            _progress = progress ?? throw new ArgumentNullException(nameof(progress));
            _logger = logger ?? NullLogger<MigrationService>.Instance;
            _dependencyResolver = dependencyResolver ?? throw new ArgumentNullException(nameof(dependencyResolver));
            _batchSize = batchSize;
        }

        /// <summary>
        /// 执行数据库迁移
        /// </summary>
        public async Task MigrateAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("开始数据库迁移");

                // 获取所有表结构
                var tables = await _sourceReader.GetTableSchemasAsync(cancellationToken);
                _logger.LogInformation("获取到 {Count} 个表的结构信息", tables.Count);

                // 解析表依赖关系
                var orderedTables = _dependencyResolver.ResolveDependencies(tables);
                _logger.LogInformation("已确定表的迁移顺序");

                // 按照依赖顺序迁移表
                foreach (var table in orderedTables)
                {
                    await MigrateTableAsync(table, cancellationToken);
                }

                _logger.LogInformation("数据库迁移完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库迁移过程中发生错误");
                throw;
            }
        }

        private async Task MigrateTableAsync(Models.TableSchema table, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始迁移表 {TableName}", table.Name);

                // 创建表结构
                await _targetWriter.CreateTableAsync(table, false, cancellationToken);

                // 获取总行数
                var totalRows = await _sourceReader.GetTableRowCountAsync(table.Name, cancellationToken);
                _logger.LogInformation("表 {TableName} 共有 {RowCount} 行数据", table.Name, totalRows);

                // 分批读取和写入数据
                long offset = 0;
                while (offset < totalRows)
                {
                    var data = await _sourceReader.ReadTableDataAsync(
                        table.Name,
                        _batchSize,
                        offset,
                        cancellationToken);

                    // 转换数据类型
                    var convertedData = _typeConverter.Convert(data, table);

                    // 写入数据
                    await _targetWriter.BulkWriteAsync(table.Name, convertedData, cancellationToken);

                    offset += _batchSize;
                    _progress.UpdateProgress(table.Name, totalRows, offset);
                }

                // 创建外键约束
                if (table.ForeignKeys.Count > 0)
                {
                    await _targetWriter.CreateForeignKeysAsync(table.Name, table.ForeignKeys, cancellationToken);
                }

                _progress.CompleteTable(table.Name);
                _logger.LogInformation("表 {TableName} 迁移完成", table.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "迁移表 {TableName} 时发生错误", table.Name);
                throw;
            }
        }
    }
}
