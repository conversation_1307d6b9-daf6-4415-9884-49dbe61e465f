using System;
using System.Collections.Generic;
using System.Linq;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Services
{
    /// <summary>
    /// 表依赖关系解析器实现
    /// </summary>
    public class TableDependencyResolver : ITableDependencyResolver
    {
        /// <summary>
        /// 解析表之间的依赖关系
        /// </summary>
        /// <param name="tables">表结构列表</param>
        /// <returns>按照依赖关系排序后的表列表</returns>
        public IEnumerable<TableSchema> ResolveDependencies(IEnumerable<TableSchema> tables)
        {
            if (tables == null)
                throw new ArgumentNullException(nameof(tables));

            var tableList = tables.ToList();
            var visited = new HashSet<string>();
            var result = new List<TableSchema>();

            // 对每个表进行深度优先搜索
            foreach (var table in tableList)
            {
                if (!visited.Contains(table.Name))
                {
                    VisitTable(table, tableList, visited, result);
                }
            }

            return result;
        }

        private void VisitTable(TableSchema table, List<TableSchema> allTables, HashSet<string> visited, List<TableSchema> result)
        {
            visited.Add(table.Name);

            // 先处理依赖的表
            foreach (var foreignKey in table.ForeignKeys)
            {
                var referencedTable = allTables.FirstOrDefault(t => t.Name == foreignKey.ReferencedTable);
                if (referencedTable != null && !visited.Contains(referencedTable.Name))
                {
                    VisitTable(referencedTable, allTables, visited, result);
                }
            }

            // 将当前表添加到结果中
            result.Add(table);
        }
    }
}
