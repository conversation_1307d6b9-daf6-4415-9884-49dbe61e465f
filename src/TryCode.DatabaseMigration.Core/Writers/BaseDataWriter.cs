using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Writers
{
    /// <summary>
    /// 数据库写入器基类，提供通用功能和抽象方法
    /// </summary>
    public abstract class BaseDataWriter : IDataWriter
    {
        /// <summary>
        /// 默认批处理大小
        /// </summary>
        protected const int DefaultBatchSize = 1000;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        protected const int MaxRetryAttempts = 3;
        
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        protected readonly string ConnectionString;
        
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;
        
        /// <summary>
        /// 批处理大小
        /// </summary>
        protected readonly int BatchSize;
        
        /// <summary>
        /// 初始化数据库写入器基类
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="batchSize">批处理大小</param>
        protected BaseDataWriter(string connectionString, ILogger logger, int batchSize = DefaultBatchSize)
        {
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            Logger = logger ?? NullLogger.Instance;
            BatchSize = batchSize > 0 ? batchSize : DefaultBatchSize;
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <param name="skipSchemaCreation">是否跳过创建表结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        public abstract Task CreateTableAsync(TableSchema schema, bool skipSchemaCreation = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 批量写入数据
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="data">数据行集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        public abstract Task BulkWriteAsync(string tableName, IEnumerable<dynamic> data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 创建外键约束
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="foreignKeys">外键约束列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        public abstract Task CreateForeignKeysAsync(string tableName, List<ForeignKeySchema> foreignKeys, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 处理数据行值（共享方法）
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        protected abstract object? ProcessValue(object? value);
        
        /// <summary>
        /// 获取执行中重试的延迟时间（指数退避）
        /// </summary>
        /// <param name="retryCount">当前重试次数</param>
        /// <returns>重试延迟时间</returns>
        protected TimeSpan GetRetryDelay(int retryCount)
        {
            return TimeSpan.FromSeconds(Math.Pow(2, retryCount));
        }
    }
}
