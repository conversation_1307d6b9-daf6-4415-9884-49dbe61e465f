using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;

namespace TryCode.DatabaseMigration.Core.Writers
{
    /// <summary>
    /// 批量数据写入器基类
    /// </summary>
    public abstract class BaseBulkWriter
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        protected readonly string ConnectionString;
        
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;
        
        /// <summary>
        /// 批处理大小
        /// </summary>
        protected readonly int BatchSize;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        protected const int MaxRetryAttempts = 3;
        
        /// <summary>
        /// 初始化批量数据写入器基类
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="batchSize">批处理大小</param>
        protected BaseBulkWriter(string connectionString, ILogger logger, int batchSize)
        {
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            BatchSize = batchSize > 0 ? batchSize : 1000;
        }
        
        /// <summary>
        /// 批量写入数据
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="data">数据行集合</param>
        /// <param name="cancellationToken">取消令牌</param>
        public abstract Task BulkWriteAsync(string tableName, IEnumerable<dynamic> data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 处理值转换
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        public abstract object? ProcessValue(object? value);
        
        /// <summary>
        /// 获取重试延迟时间
        /// </summary>
        /// <param name="retryCount">当前重试次数</param>
        /// <returns>重试延迟时间</returns>
        protected TimeSpan GetRetryDelay(int retryCount)
        {
            return TimeSpan.FromSeconds(Math.Pow(2, retryCount));
        }
        
        /// <summary>
        /// 准备批量参数
        /// </summary>
        /// <param name="batch">批处理数据</param>
        /// <param name="columns">列名列表</param>
        /// <returns>动态参数</returns>
        protected abstract DynamicParameters PrepareBatchParameters(List<IDictionary<string, object>> batch, List<string> columns);
    }
}
