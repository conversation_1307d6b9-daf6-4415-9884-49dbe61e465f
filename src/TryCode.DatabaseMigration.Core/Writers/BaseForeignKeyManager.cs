using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Writers
{
    /// <summary>
    /// 外键管理器基类
    /// </summary>
    public abstract class BaseForeignKeyManager
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        protected readonly string ConnectionString;
        
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;
        
        /// <summary>
        /// 初始化外键管理器基类
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        protected BaseForeignKeyManager(string connectionString, ILogger logger)
        {
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 创建外键约束
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="foreignKeys">外键约束列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        public abstract Task CreateForeignKeysAsync(string tableName, List<ForeignKeySchema> foreignKeys, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 构建外键SQL
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="fk">外键信息</param>
        /// <returns>外键SQL语句</returns>
        protected abstract string BuildForeignKeySQL(string tableName, ForeignKeySchema fk);
    }
}
