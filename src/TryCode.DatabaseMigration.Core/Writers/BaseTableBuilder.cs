using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Writers
{
    /// <summary>
    /// 表结构构建器基类
    /// </summary>
    public abstract class BaseTableBuilder
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        protected readonly string ConnectionString;

        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;

        /// <summary>
        /// 初始化表结构构建器基类
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        protected BaseTableBuilder(string connectionString, ILogger logger)
        {
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <param name="skipSchemaCreation">是否跳过创建表结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        public abstract Task CreateTableAsync(TableSchema schema, bool skipSchemaCreation = false, CancellationToken cancellationToken = default);

        /// <summary>
        /// 构建创建表的SQL语句
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>创建表的SQL语句</returns>
        protected abstract string BuildCreateTableSql(TableSchema schema);

        /// <summary>
        /// 构建列定义
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>列定义列表</returns>
        protected abstract List<string> BuildColumnDefinitions(TableSchema schema);

        /// <summary>
        /// 构建主键定义
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>主键定义</returns>
        protected abstract string? BuildPrimaryKeyDefinition(TableSchema schema);
    }
}
