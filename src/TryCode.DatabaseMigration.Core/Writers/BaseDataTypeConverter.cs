using System;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Writers
{
    /// <summary>
    /// 数据库类型转换器基类
    /// </summary>
    public abstract class BaseDataTypeConverter
    {
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected readonly ILogger Logger;
        
        /// <summary>
        /// 初始化数据类型转换器基类
        /// </summary>
        /// <param name="logger">日志记录器</param>
        protected BaseDataTypeConverter(ILogger logger)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 将数据库一般类型转换为具体数据库类型
        /// </summary>
        /// <param name="column">列信息</param>
        /// <returns>数据库特定的数据类型字符串</returns>
        public abstract string ConvertDataType(ColumnSchema column);
        
        /// <summary>
        /// 处理值转换
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        public abstract object? ProcessValue(object? value);
    }
}
