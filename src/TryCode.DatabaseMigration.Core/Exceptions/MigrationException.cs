using System;

namespace TryCode.DatabaseMigration.Core.Exceptions
{
    /// <summary>
    /// 迁移异常基类
    /// </summary>
    public class MigrationException : Exception
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string? TableName { get; }

        /// <summary>
        /// 阶段
        /// </summary>
        public string? Stage { get; }

        public MigrationException(string message, string? tableName = null, string? stage = null)
            : base(message)
        {
            TableName = tableName;
            Stage = stage;
        }

        public MigrationException(string message, Exception? innerException, string? tableName = null, string? stage = null)
            : base(message, innerException)
        {
            TableName = tableName;
            Stage = stage;
        }
    }

    /// <summary>
    /// 表结构创建异常
    /// </summary>
    public class SchemaCreationException : MigrationException
    {
        public SchemaCreationException(string tableName, string message, Exception? innerException = null)
            : base(message, innerException, tableName, "SchemaCreation")
        {
        }
    }

    /// <summary>
    /// 数据迁移异常
    /// </summary>
    public class DataMigrationException : MigrationException
    {
        /// <summary>
        /// 已处理的行数
        /// </summary>
        public long ProcessedRows { get; }

        public DataMigrationException(string tableName, string message, long processedRows, Exception? innerException = null)
            : base(message, innerException, tableName, "DataMigration")
        {
            ProcessedRows = processedRows;
        }
    }

    /// <summary>
    /// 外键创建异常
    /// </summary>
    public class ForeignKeyCreationException : MigrationException
    {
        public ForeignKeyCreationException(string tableName, string message, Exception? innerException = null)
            : base(message, innerException, tableName, "ForeignKeyCreation")
        {
        }
    }

    /// <summary>
    /// 配置验证异常
    /// </summary>
    public class ConfigurationValidationException : MigrationException
    {
        public ConfigurationValidationException(string message)
            : base(message, null, null, "Configuration")
        {
        }

        public ConfigurationValidationException(string message, Exception? innerException)
            : base(message, innerException, null, "Configuration")
        {
        }
    }

    /// <summary>
    /// 数据库连接异常
    /// </summary>
    public class DatabaseConnectionException : MigrationException
    {
        public DatabaseConnectionException(string message, Exception? innerException = null)
            : base(message, innerException, null, "Connection")
        {
        }
    }
}
