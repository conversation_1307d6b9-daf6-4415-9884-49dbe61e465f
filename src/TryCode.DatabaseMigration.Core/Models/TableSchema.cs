using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace TryCode.DatabaseMigration.Core.Models
{
    /// <summary>
    /// 表结构信息
    /// </summary>
    public class TableSchema
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 表的所有列
        /// </summary>
        public List<ColumnSchema> Columns { get; set; } = new List<ColumnSchema>();

        /// <summary>
        /// 表的所有外键约束
        /// </summary>
        public List<ForeignKeySchema> ForeignKeys { get; set; } = new();

        /// <summary>
        /// 主键列名列表
        /// </summary>
        private List<string> _primaryKeys = new();

        /// <summary>
        /// 主键列名列表
        /// </summary>
        public List<string> PrimaryKeys
        {
            get => _primaryKeys;
            set
            {
                _primaryKeys = value;
                // 更新列的主键标记
                foreach (var column in Columns)
                {
                    column.IsPrimaryKey = _primaryKeys.Contains(column.Name);
                }
            }
        }

        /// <summary>
        /// 添加主键
        /// </summary>
        public void AddPrimaryKey(string columnName)
        {
            if (!_primaryKeys.Contains(columnName))
            {
                _primaryKeys.Add(columnName);
                // 更新列的主键标记
                var column = Columns.FirstOrDefault(c => c.Name == columnName);
                if (column != null)
                {
                    column.IsPrimaryKey = true;
                }
            }
        }

        /// <summary>
        /// 添加主键
        /// </summary>
        public void AddPrimaryKeys(IEnumerable<string> columnNames)
        {
            foreach (var columnName in columnNames)
            {
                AddPrimaryKey(columnName);
            }
        }

        /// <summary>
        /// 移除主键
        /// </summary>
        public void RemovePrimaryKey(string columnName)
        {
            _primaryKeys.Remove(columnName);
            // 更新列的主键标记
            var column = Columns.FirstOrDefault(c => c.Name == columnName);
            if (column != null)
            {
                column.IsPrimaryKey = false;
            }
        }

        /// <summary>
        /// 清除所有主键
        /// </summary>
        public void ClearPrimaryKeys()
        {
            _primaryKeys.Clear();
            // 更新所有列的主键标记
            foreach (var column in Columns)
            {
                column.IsPrimaryKey = false;
            }
        }

        /// <summary>
        /// 表的预计总行数
        /// </summary>
        public long EstimatedRowCount { get; set; }

        /// <summary>
        /// 是否是分区表
        /// </summary>
        public bool IsPartitioned { get; set; }

        /// <summary>
        /// 分区表的分区键
        /// </summary>
        public List<string> PartitionKeys { get; set; } = new List<string>();

        /// <summary>
        /// 分区表的主分区键
        /// </summary>
        public string? PartitionKey { get; set; }

        /// <summary>
        /// 分区数量
        /// </summary>
        public int PartitionCount { get; set; }

        /// <summary>
        /// 分区类型（例如：RANGE, LIST, HASH等）
        /// </summary>
        public string? PartitionType { get; set; }

        /// <summary>
        /// 分区表定义
        /// </summary>
        public List<PartitionDefinition> Partitions { get; set; } = new List<PartitionDefinition>();

        /// <summary>
        /// 分区表定义（别名，兼容旧代码）
        /// </summary>
        public List<PartitionDefinition> PartitionDefinitions => Partitions;

        /// <summary>
        /// 添加额外信息
        /// </summary>
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();

        /// <summary>
        /// 是否仅创建表结构而不迁移数据
        /// </summary>
        public bool SchemaOnly { get; set; }
    }

    /// <summary>
    /// 分区定义信息
    /// </summary>
    public class PartitionDefinition
    {
        /// <summary>
        /// 分区名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分区条件（例如：VALUES LESS THAN (100)）
        /// </summary>
        public string Condition { get; set; } = string.Empty;

        /// <summary>
        /// 分区值（兼容旧代码）
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 子分区定义
        /// </summary>
        public List<PartitionDefinition>? SubPartitions { get; set; }

        /// <summary>
        /// 额外信息
        /// </summary>
        public Dictionary<string, string> AdditionalInfo { get; set; } = new();
    }

    /// <summary>
    /// 列结构信息
    /// </summary>
    public class ColumnSchema
    {
        /// <summary>
        /// 列名
        /// </summary>
        public string Name { get; set; }= string.Empty;

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }= string.Empty;

        /// <summary>
        /// 是否允许为空
        /// </summary>
        public bool IsNullable { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public string? DefaultValue { get; set; }

        /// <summary>
        /// 是否是自增字段
        /// </summary>
        public bool IsAutoIncrement { get; set; }

        /// <summary>
        /// 是否是主键
        /// </summary>
        public bool IsPrimaryKey { get; set; }

        /// <summary>
        /// 最大长度（适用于字符串类型）
        /// </summary>
        public int? MaxLength { get; set; }

        /// <summary>
        /// 精度（适用于数值类型）
        /// </summary>
        public int? NumericPrecision { get; set; }

        /// <summary>
        /// 小数位数（适用于数值类型）
        /// </summary>
        public int? Scale { get; set; }

        /// <summary>
        /// 是否为布尔类型（适用于tinyint(1)）
        /// </summary>
        public bool IsBoolean { get; set; }

        /// <summary>
        /// 是否是索引列
        /// </summary>
        public bool IsIndexed { get; set; }
    }

    /// <summary>
    /// 外键约束信息
    /// </summary>
    public class ForeignKeySchema
    {
        /// <summary>
        /// 外键名称
        /// </summary>
        public string Name { get; set; }= string.Empty;

        /// <summary>
        /// 外键列名
        /// </summary>
        public string ColumnName { get; set; }= string.Empty;

        /// <summary>
        /// 外键列名列表（支持多列外键）
        /// </summary>
        public List<string> Columns { get; set; } = new List<string>();

        /// <summary>
        /// 引用的表名
        /// </summary>
        public string ReferencedTable { get; set; }= string.Empty;

        /// <summary>
        /// 引用的列名
        /// </summary>
        public string ReferencedColumn { get; set; }= string.Empty;

        /// <summary>
        /// 引用的列名列表（支持多列外键）
        /// </summary>
        public List<string> ReferencedColumns { get; set; } = new List<string>();

        /// <summary>
        /// 更新时的行为
        /// </summary>
        public string? OnUpdate { get; set; }

        /// <summary>
        /// 删除时的行为
        /// </summary>
        public string? OnDelete { get; set; }
    }
}
