using System;

namespace TryCode.DatabaseMigration.Core.Models
{
    /// <summary>
    /// 迁移进度
    /// </summary>
    public class MigrationProgress
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// 当前阶段
        /// </summary>
        public string Stage { get; set; } = string.Empty;

        /// <summary>
        /// 状态详情
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 总数
        /// </summary>
        public long Total { get; set; }

        /// <summary>
        /// 已处理数量
        /// </summary>
        public long Processed { get; set; }

        /// <summary>
        /// 进度百分比（0-100）
        /// </summary>
        public double Percentage
        {
            get
            {
                if (Total <= 0) return 0;
                return Math.Round((double)Processed / Total * 100, 2);
            }
        }
    }
}
