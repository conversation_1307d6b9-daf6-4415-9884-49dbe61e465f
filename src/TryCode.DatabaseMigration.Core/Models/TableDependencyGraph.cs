using System.Collections.Generic;
using System.Linq;

namespace TryCode.DatabaseMigration.Core.Models
{
    /// <summary>
    /// 表依赖关系图，用于处理表的迁移顺序
    /// </summary>
    public class TableDependencyGraph
    {
        private readonly Dictionary<string, HashSet<string>> _dependencies = new();

        /// <summary>
        /// 添加表依赖关系
        /// </summary>
        /// <param name="table">依赖表</param>
        /// <param name="dependsOn">被依赖的表</param>
        public void AddDependency(string table, string dependsOn)
        {
            if (!_dependencies.ContainsKey(table))
            {
                _dependencies[table] = new HashSet<string>();
            }
            _dependencies[table].Add(dependsOn);
        }

        /// <summary>
        /// 获取所有表的拓扑排序
        /// </summary>
        /// <returns>排序后的表列表</returns>
        public List<TableSchema> TopologicalSort(IEnumerable<TableSchema> tables)
        {
            var visited = new HashSet<string>();
            var sorted = new List<TableSchema>();
            var tableDict = tables.ToDictionary(t => t.Name);

            foreach (var table in tables)
            {
                if (!visited.Contains(table.Name))
                {
                    Visit(table.Name, visited, sorted, tableDict);
                }
            }

            return sorted;
        }

        private void Visit(string tableName, HashSet<string> visited, List<TableSchema> sorted, Dictionary<string, TableSchema> tableDict)
        {
            visited.Add(tableName);

            if (_dependencies.TryGetValue(tableName, out var dependencies))
            {
                foreach (var dep in dependencies)
                {
                    if (!visited.Contains(dep) && tableDict.ContainsKey(dep))
                    {
                        Visit(dep, visited, sorted, tableDict);
                    }
                }
            }

            if (tableDict.TryGetValue(tableName, out var table))
            {
                sorted.Add(table);
            }
        }
    }
}
