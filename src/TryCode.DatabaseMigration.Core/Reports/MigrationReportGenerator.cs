using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TryCode.DatabaseMigration.Core.Reports
{
    /// <summary>
    /// 表迁移状态
    /// </summary>
    public enum TableStatus
    {
        /// <summary>
        /// 等待处理
        /// </summary>
        Pending,

        /// <summary>
        /// 处理中
        /// </summary>
        InProgress,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 失败
        /// </summary>
        Failed,

        /// <summary>
        /// 已跳过
        /// </summary>
        Skipped
    }

    /// <summary>
    /// 表检查点
    /// </summary>
    public class TableCheckpoint
    {
        /// <summary>
        /// 表状态
        /// </summary>
        public TableStatus Status { get; set; }

        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; set; }

        /// <summary>
        /// 已迁移行数
        /// </summary>
        public long MigratedRows { get; set; }

        /// <summary>
        /// 阶段
        /// </summary>
        public string? Stage { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? Error { get; set; }
    }

    /// <summary>
    /// 迁移检查点
    /// </summary>
    public class MigrationCheckpoint
    {
        /// <summary>
        /// 总表数
        /// </summary>
        public int TotalTables { get; set; }

        /// <summary>
        /// 表检查点
        /// </summary>
        public Dictionary<string, TableCheckpoint> TableCheckpoints { get; set; } = new();
    }

    /// <summary>
    /// 迁移报告生成器
    /// </summary>
    public class MigrationReportGenerator
    {
        private readonly ILogger _logger;
        private readonly DateTime _startTime;
        private readonly ConcurrentDictionary<string, DateTime> _tableStartTimes = new();
        private readonly ConcurrentDictionary<string, DateTime> _tableEndTimes = new();

        /// <summary>
        /// 初始化新的<see cref="MigrationReportGenerator"/>实例
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public MigrationReportGenerator(ILogger logger)
        {
            _logger = logger;
            _startTime = DateTime.Now;
        }

        /// <summary>
        /// 记录表迁移开始
        /// </summary>
        /// <param name="tableName">表名</param>
        public void RecordTableStart(string tableName)
        {
            _tableStartTimes.AddOrUpdate(tableName, DateTime.Now, (_, _) => DateTime.Now);
        }

        /// <summary>
        /// 记录表迁移结束
        /// </summary>
        /// <param name="tableName">表名</param>
        public void RecordTableEnd(string tableName)
        {
            _tableEndTimes.AddOrUpdate(tableName, DateTime.Now, (_, _) => DateTime.Now);
        }

        /// <summary>
        /// 生成迁移报告
        /// </summary>
        /// <param name="checkpoint">迁移检查点</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>异步任务</returns>
        public async Task GenerateReportAsync(MigrationCheckpoint checkpoint, string outputPath, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("正在生成迁移报告...");

                var report = new MigrationReport
                {
                    StartTime = _startTime,
                    EndTime = DateTime.Now,
                    TotalTables = checkpoint.TotalTables,
                    SuccessTables = 0,
                    FailedTables = 0,
                    SkippedTables = 0,
                    TotalRows = 0,
                    MigratedRows = 0
                };

                // 汇总表信息
                foreach (var tableEntry in checkpoint.TableCheckpoints)
                {
                    var tableName = tableEntry.Key;
                    var tableCheckpoint = tableEntry.Value;

                    var tableReport = new TableMigrationReport
                    {
                        TableName = tableName,
                        TotalRows = tableCheckpoint.TotalRows,
                        MigratedRows = tableCheckpoint.MigratedRows,
                        Status = GetTableStatus(tableCheckpoint.Status),
                        ErrorMessage = tableCheckpoint.Error,
                        Stage = tableCheckpoint.Stage ?? string.Empty
                    };

                    // 设置开始和结束时间
                    if (_tableStartTimes.TryGetValue(tableName, out var startTime))
                    {
                        tableReport.StartTime = startTime;
                    }

                    if (_tableEndTimes.TryGetValue(tableName, out var endTime))
                    {
                        tableReport.EndTime = endTime;
                    }

                    report.Tables.Add(tableReport);

                    // 更新统计信息
                    report.TotalRows += tableCheckpoint.TotalRows;
                    report.MigratedRows += tableCheckpoint.MigratedRows;

                    switch (tableCheckpoint.Status)
                    {
                        case TableStatus.Completed:
                            report.SuccessTables++;
                            break;
                        case TableStatus.Failed:
                            report.FailedTables++;
                            break;
                        case TableStatus.Skipped:
                            report.SkippedTables++;
                            break;
                    }
                }

                // 生成报告文本
                var reportText = report.GenerateReport();

                // 写入文件
                await File.WriteAllTextAsync(outputPath, reportText, cancellationToken);

                _logger.LogInformation("迁移报告已生成: {ReportPath}", outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成迁移报告时出错: {ErrorMessage}", ex.Message);
            }
        }

        private static TableMigrationStatus GetTableStatus(TableStatus status)
        {
            return status switch
            {
                TableStatus.Pending => TableMigrationStatus.Pending,
                TableStatus.InProgress => TableMigrationStatus.InProgress,
                TableStatus.Completed => TableMigrationStatus.Completed,
                TableStatus.Failed => TableMigrationStatus.Failed,
                TableStatus.Skipped => TableMigrationStatus.Skipped,
                _ => TableMigrationStatus.Pending
            };
        }
    }
}
