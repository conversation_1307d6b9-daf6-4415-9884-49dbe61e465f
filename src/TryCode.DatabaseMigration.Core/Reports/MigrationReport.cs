using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace TryCode.DatabaseMigration.Core.Reports
{
    /// <summary>
    /// 迁移报告
    /// </summary>
    public class MigrationReport
    {
        /// <summary>
        /// 迁移开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 迁移结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总表数
        /// </summary>
        public int TotalTables { get; set; }

        /// <summary>
        /// 成功迁移的表数量
        /// </summary>
        public int SuccessTables { get; set; }

        /// <summary>
        /// 失败的表数量
        /// </summary>
        public int FailedTables { get; set; }

        /// <summary>
        /// 已跳过的表数量
        /// </summary>
        public int SkippedTables { get; set; }

        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; set; }

        /// <summary>
        /// 已迁移的行数
        /// </summary>
        public long MigratedRows { get; set; }

        /// <summary>
        /// 表详情
        /// </summary>
        public List<TableMigrationReport> Tables { get; set; } = new();

        /// <summary>
        /// 生成报告文本
        /// </summary>
        /// <returns>报告文本</returns>
        public string GenerateReport()
        {
            var sb = new StringBuilder();
            var duration = EndTime - StartTime;

            sb.AppendLine("===== 数据库迁移报告 =====");
            sb.AppendLine();
            sb.AppendLine($"开始时间: {StartTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"结束时间: {EndTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"执行时间: {duration.Hours}小时 {duration.Minutes}分钟 {duration.Seconds}秒");
            sb.AppendLine();
            sb.AppendLine($"总表数: {TotalTables}");
            sb.AppendLine($"成功: {SuccessTables} ({GetPercentage(SuccessTables, TotalTables)}%)");
            sb.AppendLine($"失败: {FailedTables} ({GetPercentage(FailedTables, TotalTables)}%)");
            sb.AppendLine($"跳过: {SkippedTables} ({GetPercentage(SkippedTables, TotalTables)}%)");
            sb.AppendLine();
            sb.AppendLine($"总行数: {TotalRows:N0}");
            sb.AppendLine($"已迁移: {MigratedRows:N0} ({GetPercentage(MigratedRows, TotalRows)}%)");
            sb.AppendLine();

            if (FailedTables > 0)
            {
                sb.AppendLine("===== 失败表列表 =====");
                sb.AppendLine();
                var failedTables = Tables.Where(t => t.Status == TableMigrationStatus.Failed).OrderBy(t => t.TableName).ToList();
                foreach (var table in failedTables)
                {
                    sb.AppendLine($"- {table.TableName}");
                    sb.AppendLine($"  原因: {table.ErrorMessage}");
                    sb.AppendLine($"  阶段: {table.Stage}");
                    sb.AppendLine($"  行数: {table.TotalRows:N0}，已迁移: {table.MigratedRows:N0} ({GetPercentage(table.MigratedRows, table.TotalRows)}%)");
                    sb.AppendLine();
                }
            }

            sb.AppendLine("===== 详细迁移状态 =====");
            sb.AppendLine();
            sb.AppendLine("表名\t状态\t总行数\t已迁移\t迁移率\t耗时");
            sb.AppendLine("--------------------------------------------------");
            
            foreach (var table in Tables.OrderBy(t => t.TableName))
            {
                var status = table.Status switch
                {
                    TableMigrationStatus.Completed => "成功",
                    TableMigrationStatus.Failed => "失败",
                    TableMigrationStatus.Skipped => "跳过",
                    TableMigrationStatus.Pending => "未处理",
                    TableMigrationStatus.InProgress => "进行中",
                    _ => "未知"
                };
                
                var percentage = GetPercentage(table.MigratedRows, table.TotalRows);
                var tableDuration = table.EndTime.HasValue && table.StartTime.HasValue 
                    ? (table.EndTime.Value - table.StartTime.Value)
                    : TimeSpan.Zero;
                
                sb.AppendLine($"{table.TableName}\t{status}\t{table.TotalRows:N0}\t{table.MigratedRows:N0}\t{percentage}%\t{FormatDuration(tableDuration)}");
            }

            return sb.ToString();
        }

        private static double GetPercentage(long part, long total)
        {
            if (total == 0) return 0;
            return Math.Round((double)part / total * 100, 2);
        }

        private static string FormatDuration(TimeSpan duration)
        {
            if (duration.TotalHours >= 1)
            {
                return $"{duration.Hours}小时{duration.Minutes}分";
            }
            else if (duration.TotalMinutes >= 1)
            {
                return $"{duration.Minutes}分{duration.Seconds}秒";
            }
            else
            {
                return $"{duration.Seconds}秒";
            }
        }
    }

    /// <summary>
    /// 表迁移报告
    /// </summary>
    public class TableMigrationReport
    {
        /// <summary>
        /// 表名
        /// </summary>
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// 迁移状态
        /// </summary>
        public TableMigrationStatus Status { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 迁移阶段
        /// </summary>
        public string Stage { get; set; } = string.Empty;

        /// <summary>
        /// 总行数
        /// </summary>
        public long TotalRows { get; set; }

        /// <summary>
        /// 已迁移行数
        /// </summary>
        public long MigratedRows { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 表迁移状态
    /// </summary>
    public enum TableMigrationStatus
    {
        /// <summary>
        /// 等待处理
        /// </summary>
        Pending,

        /// <summary>
        /// 处理中
        /// </summary>
        InProgress,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 失败
        /// </summary>
        Failed,

        /// <summary>
        /// 已跳过
        /// </summary>
        Skipped
    }
}
