using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.TypeConverters
{
    /// <summary>
    /// 类型转换器基类
    /// </summary>
    public abstract class TypeConverterBase : ITypeConverter
    {
        /// <summary>
        /// 将源数据库类型转换为目标数据库类型
        /// </summary>
        public abstract string ConvertDataType(string sourceType, ColumnSchema sourceColumn);

        /// <summary>
        /// 转换数据值
        /// </summary>
        public virtual async Task<object> ConvertValueAsync(object value, string sourceType, string targetType)
        {
            if (value == null || value == DBNull.Value)
            {
                return null;
            }

            // 检查是否有特殊类型处理
            if (await HandleSpecialTypeAsync(value, sourceType, targetType) is { } specialResult)
            {
                return specialResult;
            }

            // 基础类型转换
            return await BasicTypeConversionAsync(value, sourceType, targetType);
        }

        /// <summary>
        /// 检查是否支持特定类型的转换
        /// </summary>
        public abstract bool CanConvert(string sourceType, string targetType);

        /// <summary>
        /// 处理特殊类型转换，可由子类重写
        /// </summary>
        protected virtual Task<object> HandleSpecialTypeAsync(object value, string sourceType, string targetType)
        {
            return Task.FromResult<object>(null);
        }

        /// <summary>
        /// 基础类型转换，可由子类重写
        /// </summary>
        protected virtual Task<object> BasicTypeConversionAsync(object value, string sourceType, string targetType)
        {
            // 默认实现：直接返回原值
            return Task.FromResult(value);
        }

        /// <summary>
        /// 转换数据行
        /// </summary>
        public virtual IEnumerable<dynamic> Convert(IEnumerable<dynamic> data, TableSchema table)
        {
            var result = new List<dynamic>();
            foreach (var row in data)
            {
                var convertedRow = new Dictionary<string, object>();
                foreach (var column in table.Columns)
                {
                    var sourceValue = ((IDictionary<string, object>)row)[column.Name];
                    var targetType = ConvertDataType(column.DataType, column);
                    var convertedValue = ConvertValueAsync(sourceValue, column.DataType, targetType).Result;
                    convertedRow[column.Name] = convertedValue;
                }
                result.Add(convertedRow);
            }
            return result;
        }
    }
}
