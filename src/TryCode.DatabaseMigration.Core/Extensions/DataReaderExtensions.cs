using System;
using System.Collections.Generic;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.Extensions
{
    /// <summary>
    /// 数据读取器扩展方法
    /// </summary>
    public static class DataReaderExtensions
    {
        /// <summary>
        /// 从动态数据行创建列架构信息
        /// </summary>
        public static ColumnSchema CreateColumnSchema(dynamic column, string nameField = "column_name", string dataTypeField = "data_type")
        {
            var schema = new ColumnSchema
            {
                Name = column.GetType().GetProperty(nameField)?.GetValue(column, null)?.ToString() 
                    ?? throw new ArgumentException($"找不到{nameField}字段"),
                DataType = column.GetType().GetProperty(dataTypeField)?.GetValue(column, null)?.ToString() 
                    ?? throw new ArgumentException($"找不到{dataTypeField}字段")
            };

            // 尝试设置可空属性
            try { schema.IsNullable = column.is_nullable == "YES"; } catch { }

            // 尝试设置默认值
            try { schema.DefaultValue = column.column_default?.ToString(); } catch { }

            // 尝试设置最大长度
            try { schema.MaxLength = column.character_maximum_length; } catch { }

            // 尝试设置数值精度
            try { schema.NumericPrecision = column.numeric_precision; } catch { }

            // 尝试设置小数位数
            try { schema.Scale = column.numeric_scale; } catch { }

            // 尝试设置是否自增
            try 
            { 
                schema.IsAutoIncrement = (column.is_serial ?? false) || 
                                       (column.is_identity ?? false) ||
                                       (column.extra?.ToString()?.Contains("auto_increment") ?? false);
            } catch { }

            // 尝试设置是否主键
            try { schema.IsPrimaryKey = column.is_primary_key ?? false; } catch { }

            return schema;
        }

        /// <summary>
        /// 从字典创建外键架构信息
        /// </summary>
        public static ForeignKeySchema CreateForeignKeySchema(
            string constraintName,
            string columnName,
            string referencedTable,
            string referencedColumn)
        {
            return new ForeignKeySchema
            {
                Name = constraintName,
                ColumnName = columnName,
                ReferencedTable = referencedTable,
                ReferencedColumn = referencedColumn
            };
        }

        /// <summary>
        /// 验证列架构信息是否有效
        /// </summary>
        public static bool IsValidColumnSchema(ColumnSchema schema)
        {
            return !string.IsNullOrEmpty(schema.Name) && !string.IsNullOrEmpty(schema.DataType);
        }

        /// <summary>
        /// 添加主键列到表架构（确保不重复）
        /// </summary>
        public static void AddPrimaryKey(this TableSchema schema, string columnName)
        {
            if (schema.PrimaryKeys == null)
            {
                schema.PrimaryKeys = new List<string>();
            }

            schema.AddPrimaryKey(columnName);
        }

        /// <summary>
        /// 批量添加主键列到表架构（确保不重复）
        /// </summary>
        public static void AddPrimaryKeys(this TableSchema schema, IEnumerable<string> columnNames)
        {
            foreach (var columnName in columnNames)
            {
                schema.AddPrimaryKey(columnName);
            }
        }
    }
}
