using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.PostgreSQL.Extensions
{
    /// <summary>
    /// PostgreSQL数据读取器扩展方法
    /// </summary>
    public static class PostgresDataReaderExtensions
    {
        #region SQL查询常量

        private const string SQL_GET_TABLE_COLUMNS = @"
            SELECT 
                c.column_name,
                c.data_type,
                c.is_nullable,
                c.column_default,
                c.character_maximum_length,
                c.numeric_precision,
                c.numeric_scale,
                CASE WHEN c.column_default LIKE 'nextval%' THEN true ELSE false END as is_serial,
                CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key
            FROM information_schema.columns c
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_name = @tableName
                    AND tc.table_schema = @schema
            ) pk ON c.column_name = pk.column_name
            WHERE c.table_name = @tableName AND c.table_schema = @schema
            ORDER BY ordinal_position";

        private const string SQL_CHECK_PARTITION = @"
            SELECT 
                EXISTS (
                    SELECT 1 
                    FROM pg_class c 
                    JOIN pg_inherits i ON c.oid = i.inhrelid 
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE c.relname = @tableName AND c.relkind = 'r'
                    AND n.nspname = @schema
                ) AS is_child_partition,
                EXISTS (
                    SELECT 1 
                    FROM pg_class c 
                    JOIN pg_partitioned_table p ON c.oid = p.partrelid 
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE c.relname = @tableName
                    AND n.nspname = @schema
                ) AS is_partitioned";

        private const string SQL_GET_PARTITION_STRATEGY = @"
            WITH RECURSIVE partition_info AS (
                SELECT c.relname,
                       c.oid,
                       p.partstrat,
                       pg_get_partkeydef(c.oid) as partition_key
                FROM pg_class c
                JOIN pg_partitioned_table p ON c.oid = p.partrelid
                JOIN pg_namespace n ON c.relnamespace = n.oid
                WHERE c.relname = @tableName
                AND n.nspname = @schema
            )
            SELECT 
                pi.partition_key,
                pi.partstrat as partition_strategy
            FROM partition_info pi";

        private const string SQL_GET_PARTITIONS = @"
            SELECT 
                child.relname AS partition_name,
                pg_get_expr(child.relpartbound, child.oid) AS partition_bound
            FROM pg_class parent
            JOIN pg_inherits i ON parent.oid = i.inhparent
            JOIN pg_class child ON i.inhrelid = child.oid
            JOIN pg_namespace pn ON parent.relnamespace = pn.oid
            JOIN pg_namespace cn ON child.relnamespace = cn.oid
            WHERE parent.relname = @tableName
            AND pn.nspname = @schema
            AND cn.nspname = @schema
            ORDER BY child.relname";

        private const string SQL_GET_ALL_TABLES = @"
            SELECT 
                t.table_name,
                c.column_name,
                c.data_type,
                c.is_nullable = 'YES' as is_nullable,
                c.column_default,
                c.character_maximum_length,
                c.numeric_precision,
                c.numeric_scale,
                c.is_identity = 'YES' as is_identity,
                c.udt_name
            FROM information_schema.tables t
            JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
            WHERE t.table_schema = @schema
            AND t.table_type = 'BASE TABLE'
            ORDER BY t.table_name, c.ordinal_position";

        private const string SQL_GET_FOREIGN_KEYS = @"
            SELECT
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS referenced_table,
                ccu.column_name AS referenced_column,
                tc.constraint_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = @schema";

        private const string SQL_GET_PRIMARY_KEY_COLUMNS = @"
            SELECT kcu.column_name
            FROM information_schema.key_column_usage kcu
            JOIN information_schema.table_constraints tc
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            WHERE kcu.table_name = @tableName
            AND kcu.table_schema = @schema
            AND tc.constraint_type = 'PRIMARY KEY'
            ORDER BY kcu.ordinal_position";

        #endregion

        /// <summary>
        /// 加载表的列信息
        /// </summary>
        public static async Task LoadColumnsInfoAsync(this DbConnection connection, TableSchema schema, 
            IList<string> excludedColumns, string schema_name, ILogger logger)
        {
            var columns = await connection.QueryAsync<dynamic>(
                SQL_GET_TABLE_COLUMNS,
                new { tableName = schema.Name, schema = schema_name });

            foreach (var col in columns)
            {
                string columnName = col.column_name.ToString();
                if (excludedColumns.Contains(columnName))
                    continue;

                var column = new ColumnSchema
                {
                    Name = columnName,
                    DataType = col.data_type.ToString(),
                    IsNullable = col.is_nullable == "YES",
                    DefaultValue = col.column_default as string,
                    MaxLength = col.character_maximum_length,
                    NumericPrecision = col.numeric_precision,
                    Scale = col.numeric_scale,
                    IsAutoIncrement = col.is_serial,
                    IsPrimaryKey = col.is_primary_key
                };

                schema.Columns.Add(column);
                if (col.is_primary_key)
                {
                    schema.AddPrimaryKey(column.Name);
                }
            }
        }

        /// <summary>
        /// 加载表的分区信息
        /// </summary>
        public static async Task LoadPartitionInfoAsync(this DbConnection connection, TableSchema schema, 
            string schema_name, ILogger logger)
        {
            try
            {
                var partitionBasicInfo = await connection.QueryFirstOrDefaultAsync<dynamic>(
                    SQL_CHECK_PARTITION,
                    new { tableName = schema.Name, schema = schema_name });

                if (partitionBasicInfo == null)
                    return;

                schema.IsPartitioned = partitionBasicInfo.is_partitioned ?? false;

                if (schema.IsPartitioned)
                {
                    await LoadPartitionStrategyAsync(connection, schema, schema_name, logger);
                    await LoadPartitionDefinitionsAsync(connection, schema, schema_name);
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "获取表 {TableName} 的分区信息时发生错误", schema.Name);
            }
        }

        private static async Task LoadPartitionStrategyAsync(DbConnection connection, TableSchema schema, 
            string schema_name, ILogger logger)
        {
            var partitionStrategyInfo = await connection.QueryFirstOrDefaultAsync<dynamic>(
                SQL_GET_PARTITION_STRATEGY,
                new { tableName = schema.Name, schema = schema_name });

            if (partitionStrategyInfo == null)
                return;

            schema.PartitionType = MapPartitionStrategy(partitionStrategyInfo.partition_strategy?.ToString());

            if (partitionStrategyInfo.partition_key != null)
            {
                ExtractPartitionKeys(partitionStrategyInfo.partition_key.ToString(), schema, logger);
            }
        }

        private static async Task LoadPartitionDefinitionsAsync(DbConnection connection, TableSchema schema,
            string schema_name)
        {
            var partitions = await connection.QueryAsync<dynamic>(
                SQL_GET_PARTITIONS,
                new { tableName = schema.Name, schema = schema_name });

            foreach (var partition in partitions)
            {
                var partDef = new PartitionDefinition
                {
                    Name = partition.partition_name.ToString(),
                    Condition = partition.partition_bound.ToString()
                };

                schema.Partitions.Add(partDef);
            }

            // 设置分区数量
            schema.PartitionCount = schema.Partitions.Count;

            // 设置主分区键（如果还没有设置且有分区键）
            if (string.IsNullOrEmpty(schema.PartitionKey) && schema.PartitionKeys.Count > 0)
            {
                schema.PartitionKey = schema.PartitionKeys[0];
            }
        }

        private static string MapPartitionStrategy(string? strategy)
        {
            if (string.IsNullOrEmpty(strategy))
                return "UNKNOWN";
                
            return strategy switch
            {
                "r" => "RANGE",
                "l" => "LIST",
                "h" => "HASH",
                _ => "UNKNOWN"
            };
        }

        private static void ExtractPartitionKeys(string partKeyStr, TableSchema schema, ILogger logger)
        {
            try
            {
                // pg_get_partkeydef返回形如 "LIST (region)" 的格式
                var match = Regex.Match(partKeyStr, @"\((.*?)\)");
                if (match.Success)
                {
                    var keyContent = match.Groups[1].Value;
                    var keys = SplitKeepingQuotedParts(keyContent);
                    foreach (var key in keys)
                    {
                        var trimmedKey = key.Trim();
                        if (!string.IsNullOrEmpty(trimmedKey))
                        {
                            if (IsSimpleColumnName(trimmedKey))
                            {
                                var cleanColumnName = RemoveQuotes(trimmedKey);
                                schema.PartitionKeys.Add(cleanColumnName);
                            }
                            else
                            {
                                schema.PartitionKeys.Add(trimmedKey);
                                
                                if (schema.AdditionalInfo == null)
                                {
                                    schema.AdditionalInfo = new Dictionary<string, object>();
                                }
                                schema.AdditionalInfo["has_expression_partition_key"] = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "解析分区键定义时发生错误: {ErrorMessage}", ex.Message);
            }
        }

        private static string[] SplitKeepingQuotedParts(string input)
        {
            var result = new List<string>();
            var currentPart = new System.Text.StringBuilder();
            bool inQuotes = false;
            int inParentheses = 0;
            
            for (int i = 0; i < input.Length; i++)
            {
                char c = input[i];
                
                if (c == '"' || c == '\'')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == '(' && !inQuotes)
                {
                    inParentheses++;
                }
                else if (c == ')' && !inQuotes)
                {
                    inParentheses = Math.Max(0, inParentheses - 1);
                }
                else if (c == ',' && !inQuotes && inParentheses == 0)
                {
                    result.Add(currentPart.ToString());
                    currentPart.Clear();
                    continue;
                }
                
                currentPart.Append(c);
            }
            
            if (currentPart.Length > 0)
            {
                result.Add(currentPart.ToString());
            }
            
            return result.Select(s => s.Trim()).ToArray();
        }

        private static bool IsSimpleColumnName(string name)
        {
            return !name.Contains("(") && 
                   !Regex.IsMatch(name, @"[\+\-\*\/\%]") &&
                   !name.Contains(" ");
        }

        private static string RemoveQuotes(string name)
        {
            if ((name.StartsWith("\"") && name.EndsWith("\"")) ||
                (name.StartsWith("'") && name.EndsWith("'")))
            {
                return name.Substring(1, name.Length - 2);
            }
            return name;
        }

        /// <summary>
        /// 创建列Schema对象
        /// </summary>
        public static ColumnSchema CreateColumnSchema(dynamic column)
        {
            return new ColumnSchema
            {
                Name = column.column_name.ToString(),
                DataType = column.data_type.ToString(),
                IsNullable = column.is_nullable,
                DefaultValue = column.column_default?.ToString(),
                MaxLength = column.character_maximum_length is not null ? 
                    Convert.ToInt32(column.character_maximum_length) : null,
                NumericPrecision = column.numeric_precision is not null ? 
                    Convert.ToInt32(column.numeric_precision) : null,
                Scale = column.numeric_scale is not null ? 
                    Convert.ToInt32(column.numeric_scale) : null,
                IsAutoIncrement = column.is_identity,
                IsBoolean = column.udt_name.ToString().Equals("bool", StringComparison.OrdinalIgnoreCase)
            };
        }
    }
}
