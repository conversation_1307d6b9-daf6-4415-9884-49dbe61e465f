using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.PostgreSQL.Writers
{
    /// <summary>
    /// PostgreSQL数据类型转换器
    /// </summary>
    public class PostgresDataTypeConverter : BaseDataTypeConverter
    {
        /// <summary>
        /// 类型映射字典
        /// </summary>
        private readonly Dictionary<string, string> _typeMappings;
        
        /// <summary>
        /// 初始化PostgreSQL数据类型转换器
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="typeMappings">类型映射</param>
        public PostgresDataTypeConverter(ILogger logger, Dictionary<string, string>? typeMappings = null) 
            : base(logger)
        {
            _typeMappings = typeMappings ?? new Dictionary<string, string>();
        }
        
        /// <summary>
        /// 转换为PostgreSQL数据类型
        /// </summary>
        /// <param name="column">列信息</param>
        /// <returns>PostgreSQL数据类型</returns>
        public override string ConvertDataType(ColumnSchema column)
        {
            if (column == null)
                throw new ArgumentNullException(nameof(column));
                
            var dataType = column.DataType.ToLower();
            
            // 首先检查是否有自定义类型映射
            if (_typeMappings.TryGetValue(dataType, out var mappedType))
            {
                return mappedType;
            }
            
            switch (dataType)
            {
                case "char":
                case "nchar":
                    return column.MaxLength.HasValue ? $"CHAR({column.MaxLength})" : "CHAR(255)";
                
                case "varchar":
                case "nvarchar":
                case "character varying":
                    return column.MaxLength.HasValue ? $"VARCHAR({column.MaxLength})" : "VARCHAR";

                case "text":
                case "ntext":
                case "longtext":
                case "mediumtext":
                case "tinytext":
                    return "TEXT";
                    
                case "json":
                    return "JSONB";
                    
                case "xml":
                    return "XML";

                case "decimal":
                case "numeric":
                    if (column.NumericPrecision.HasValue && column.Scale.HasValue)
                    {
                        return $"DECIMAL({column.NumericPrecision}, {column.Scale})";
                    }
                    return "DECIMAL(18, 2)";

                case "float":
                case "real":
                    return "REAL";

                case "double":
                    return "DOUBLE PRECISION";

                case "bit":
                    return "BIT";

                case "tinyint":
                    return "SMALLINT";

                case "smallint":
                    return "SMALLINT";

                case "int":
                case "integer":
                    return "INTEGER";

                case "bigint":
                    return "BIGINT";

                case "date":
                    return "DATE";

                case "time":
                    return "TIME";

                case "datetime":
                case "datetime2":
                    return "TIMESTAMP";
                    
                case "timestamp":
                    return "TIMESTAMP";

                case "binary":
                case "varbinary":
                case "image":
                case "blob":
                case "longblob":
                case "mediumblob":
                case "tinyblob":
                    return "BYTEA";

                case "uniqueidentifier":
                case "guid":
                case "uuid":
                    return "UUID";

                case "money":
                case "smallmoney":
                    return "MONEY";

                case "citext": // PostgreSQL 不区分大小写的文本类型
                    return "CITEXT";

                case "inet": // PostgreSQL 网络地址类型
                    return "INET";

                case "interval": // PostgreSQL 时间间隔类型
                    return "INTERVAL";

                case "serial":
                case "auto_increment":
                    return "SERIAL";
                    
                case "bigserial":
                    return "BIGSERIAL";
                    
                default:
                    // 检查列名是否包含特定关键字来推断合适的类型
                    var columnName = column.Name.ToLower();
                    if (columnName.Contains("html") || columnName.Contains("description") ||
                        columnName.Contains("content") || columnName.Contains("text"))
                    {
                        return "TEXT";
                    }
                    if (columnName.EndsWith("url") || columnName.Contains("link"))
                    {
                        return "VARCHAR(2048)";
                    }
                    if (columnName.Contains("email"))
                    {
                        return "VARCHAR(255)";
                    }
                    if (columnName.Contains("phone") || columnName.Contains("mobile"))
                    {
                        return "VARCHAR(50)";
                    }
                    if (columnName == "browserinfo" || columnName.Contains("useragent"))
                    {
                        return "VARCHAR(1024)";
                    }
                    if (columnName.Contains("ipaddress"))
                    {
                        return "INET";
                    }
                    if (columnName.Contains("correlationid"))
                    {
                        return "VARCHAR(100)";
                    }
                    
                    return "VARCHAR(255)";
            }
        }
        
        /// <summary>
        /// 处理数据行值
        /// </summary>
        /// <param name="value">要处理的值</param>
        /// <returns>处理后的值</returns>
        public override object? ProcessValue(object? value)
        {
            try
            {
                if (value == null)
                {
                    return null;
                }

                if (value is DBNull)
                {
                    return null;
                }

                // 对于GUID类型的特殊处理
                if (value is Guid || value.GetType().Name == "Guid")
                {
                    return value.ToString();
                }

                return value switch
                {
                    // 处理常见的数据类型
                    Array arr => System.Text.Json.JsonSerializer.Serialize(arr),
                    DateTime dt => dt,
                    TimeSpan ts => ts.ToString(),

                    // 网络相关类型
                    System.Net.IPAddress ip => ip.ToString(),
                    System.Net.NetworkInformation.PhysicalAddress mac => mac.ToString(),

                    // 默认返回值本身
                    _ => value
                };
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "处理值时出错 ({ErrorMessage})，将返回 NULL", ex.Message);
                return null;
            }
        }
    }
}
