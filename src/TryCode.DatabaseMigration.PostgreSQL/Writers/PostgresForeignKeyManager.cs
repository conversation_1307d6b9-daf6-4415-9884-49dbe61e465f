using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Nhgdb;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.PostgreSQL.Writers
{
    /// <summary>
    /// PostgreSQL外键管理器
    /// </summary>
    public class PostgresForeignKeyManager : BaseForeignKeyManager
    {
        /// <summary>
        /// 初始化PostgreSQL外键管理器
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        public PostgresForeignKeyManager(string connectionString, ILogger logger) 
            : base(connectionString, logger)
        {
        }
        
        /// <summary>
        /// 创建外键约束
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="foreignKeys">外键约束列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override async Task CreateForeignKeysAsync(string tableName, List<ForeignKeySchema> foreignKeys, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(tableName))
                throw new ArgumentNullException(nameof(tableName));
                
            if (foreignKeys == null || !foreignKeys.Any())
            {
                Logger.LogInformation("表 {TableName} 没有外键需要创建", tableName);
                return;
            }
            
            Logger.LogInformation("开始为表 {TableName} 创建 {Count} 个外键约束", tableName, foreignKeys.Count);
            
            using var conn = new NhgdbConnection(ConnectionString);
            await conn.OpenAsync(cancellationToken);
            
            try
            {
                foreach (var fk in foreignKeys)
                {
                    try
                    {
                        // 验证外键引用的表是否存在
                        var refTableExists = await ValidateReferencedTable(conn, fk.ReferencedTable);
                        if (!refTableExists)
                        {
                            Logger.LogWarning("跳过创建外键 {ConstraintName}，因为引用的表 {ReferencedTable} 不存在",
                                fk.Name, fk.ReferencedTable);
                            continue;
                        }
                        
                        string sql = BuildForeignKeySQL(tableName, fk);
                        await conn.ExecuteAsync(sql);
                        
                        Logger.LogInformation("已成功创建外键约束 {ConstraintName}", fk.Name);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, "创建外键 {ConstraintName} 时出错: {ErrorMessage}", fk.Name, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "为表 {TableName} 创建外键时出错: {ErrorMessage}", tableName, ex.Message);
                throw;
            }
        }
        
        private async Task<bool> ValidateReferencedTable(NhgdbConnection conn, string referencedTable)
        {
            var sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name = @tableName";
            var count = await conn.ExecuteScalarAsync<int>(sql, new { tableName = referencedTable });
            return count > 0;
        }
        
        /// <summary>
        /// 构建外键SQL
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="fk">外键信息</param>
        /// <returns>外键SQL语句</returns>
        protected override string BuildForeignKeySQL(string tableName, ForeignKeySchema fk)
        {
            if (string.IsNullOrEmpty(fk.Name))
            {
                // 生成外键名称
                fk.Name = $"FK_{tableName}_{fk.ReferencedTable}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            }
            
            var sql = new StringBuilder();
            
            // 列处理
            string columns;
            string refColumns;
            
            // 支持单列外键和多列外键
            if (fk.Columns != null && fk.Columns.Count > 0 && fk.ReferencedColumns != null && fk.ReferencedColumns.Count > 0)
            {
                // 多列外键
                columns = string.Join(", ", fk.Columns.Select(c => $"\"{c}\""));
                refColumns = string.Join(", ", fk.ReferencedColumns.Select(c => $"\"{c}\""));
            }
            else if (!string.IsNullOrEmpty(fk.ColumnName) && !string.IsNullOrEmpty(fk.ReferencedColumn))
            {
                // 单列外键
                columns = $"\"{fk.ColumnName}\"";
                refColumns = $"\"{fk.ReferencedColumn}\"";
            }
            else
            {
                throw new ArgumentException("外键定义必须包含列信息");
            }
            
            sql.AppendLine($"ALTER TABLE \"{tableName}\"");
            sql.AppendLine($"ADD CONSTRAINT \"{fk.Name}\"");
            sql.AppendLine($"FOREIGN KEY ({columns})");
            sql.AppendLine($"REFERENCES \"{fk.ReferencedTable}\" ({refColumns})");
            
            // 添加约束动作
            if (!string.IsNullOrEmpty(fk.OnUpdate))
            {
                sql.AppendLine($"ON UPDATE {GetValidConstraintAction(fk.OnUpdate)}");
            }
            
            if (!string.IsNullOrEmpty(fk.OnDelete))
            {
                sql.AppendLine($"ON DELETE {GetValidConstraintAction(fk.OnDelete)}");
            }
            
            return sql.ToString();
        }
        
        private string GetValidConstraintAction(string action)
        {
            if (string.IsNullOrEmpty(action))
                return "RESTRICT";
                
            // 标准化约束动作名称
            var normalizedAction = action.ToUpper();
            
            switch (normalizedAction)
            {
                case "CASCADE":
                case "SET NULL":
                case "SET DEFAULT":
                case "RESTRICT":
                case "NO ACTION":
                    return normalizedAction;
                default:
                    Logger.LogWarning("不支持的约束动作 {Action}，使用默认值 RESTRICT", action);
                    return "RESTRICT";
            }
        }
    }
}
