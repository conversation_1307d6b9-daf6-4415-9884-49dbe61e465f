using System;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.PostgreSQL.Writers
{
    /// <summary>
    /// PostgreSQL分区管理器
    /// </summary>
    public class PostgresPartitionManager
    {
        private readonly ILogger _logger;

        public PostgresPartitionManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 添加分区定义到SQL
        /// </summary>
        /// <param name="sql">SQL构建器</param>
        /// <param name="schema">表结构信息</param>
        public void AppendPartitionDefinition(StringBuilder sql, TableSchema schema)
        {
            if (!schema.IsPartitioned || string.IsNullOrEmpty(schema.PartitionKey))
            {
                _logger.LogWarning("表 {TableName} 的分区配置无效", schema.Name);
                return;
            }

            try
            {
                // PostgreSQL分区表语法：PARTITION BY RANGE/LIST/HASH (column)
                if (schema.PartitionType?.Equals("RANGE", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendRangePartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("LIST", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendListPartition(sql, schema);
                }
                else if (schema.PartitionType?.Equals("HASH", StringComparison.OrdinalIgnoreCase) == true)
                {
                    AppendHashPartition(sql, schema);
                }
                else
                {
                    _logger.LogWarning("不支持的PostgreSQL分区类型 {PartitionType}", schema.PartitionType);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成PostgreSQL分区定义时出错: {ErrorMessage}", ex.Message);
            }
        }

        /// <summary>
        /// 生成创建分区的SQL语句
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>创建分区的SQL语句列表</returns>
        public string[] GeneratePartitionCreationSql(TableSchema schema)
        {
            if (!schema.IsPartitioned || schema.Partitions.Count == 0)
            {
                return Array.Empty<string>();
            }

            var partitionSqls = new string[schema.Partitions.Count];
            
            for (int i = 0; i < schema.Partitions.Count; i++)
            {
                var partition = schema.Partitions[i];
                partitionSqls[i] = GeneratePartitionSql(schema, partition);
            }

            return partitionSqls;
        }

        private void AppendRangePartition(StringBuilder sql, TableSchema schema)
        {
            // PostgreSQL RANGE分区语法
            sql.AppendLine($"PARTITION BY RANGE (\"{schema.PartitionKey}\")");
            
            _logger.LogInformation("为表 {TableName} 创建了RANGE分区定义", schema.Name);
        }

        private void AppendListPartition(StringBuilder sql, TableSchema schema)
        {
            // PostgreSQL LIST分区语法
            sql.AppendLine($"PARTITION BY LIST (\"{schema.PartitionKey}\")");
            
            _logger.LogInformation("为表 {TableName} 创建了LIST分区定义", schema.Name);
        }

        private void AppendHashPartition(StringBuilder sql, TableSchema schema)
        {
            // PostgreSQL HASH分区语法
            sql.AppendLine($"PARTITION BY HASH (\"{schema.PartitionKey}\")");
            
            _logger.LogInformation("为表 {TableName} 创建了HASH分区定义", schema.Name);
        }

        private string GeneratePartitionSql(TableSchema schema, PartitionDefinition partition)
        {
            var sql = new StringBuilder();
            
            // PostgreSQL分区创建语法：CREATE TABLE partition_name PARTITION OF parent_table FOR VALUES ...
            sql.Append($"CREATE TABLE \"{partition.Name}\" PARTITION OF \"{schema.Name}\"");

            if (schema.PartitionType?.Equals("RANGE", StringComparison.OrdinalIgnoreCase) == true)
            {
                // 从PostgreSQL分区条件中提取范围值
                // 格式：FOR VALUES FROM (2023) TO (2024)
                sql.Append($" {partition.Condition}");
            }
            else if (schema.PartitionType?.Equals("LIST", StringComparison.OrdinalIgnoreCase) == true)
            {
                // 从PostgreSQL分区条件中提取列表值
                // 格式：FOR VALUES IN (1, 2, 3)
                sql.Append($" {partition.Condition}");
            }
            else if (schema.PartitionType?.Equals("HASH", StringComparison.OrdinalIgnoreCase) == true)
            {
                // PostgreSQL HASH分区语法：FOR VALUES WITH (modulus 4, remainder 0)
                // 这里需要从分区定义中解析模数和余数
                sql.Append($" {partition.Condition}");
            }

            sql.Append(";");
            
            return sql.ToString();
        }
    }
}
