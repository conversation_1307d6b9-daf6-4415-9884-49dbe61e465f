using System;
using System.Text.Json;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.TypeConverters;

namespace TryCode.DatabaseMigration.PostgreSQL.TypeConverters
{
    /// <summary>
    /// PostgreSQL特定的类型转换器
    /// </summary>
    public class PostgresTypeConverter : TypeConverterBase
    {
        public override string ConvertDataType(string sourceType, ColumnSchema sourceColumn)
        {
            if (string.IsNullOrEmpty(sourceType))
                throw new ArgumentNullException(nameof(sourceType));

            if (sourceColumn == null)
                throw new ArgumentNullException(nameof(sourceColumn));

            return sourceType.ToLower() switch
            {
                // 数值类型
                "smallint" => "smallint",
                "integer" => "int",
                "bigint" => "bigint",
                "decimal" or "numeric" => sourceColumn.NumericPrecision.HasValue && sourceColumn.Scale.HasValue ? 
                    $"decimal({sourceColumn.NumericPrecision}, {sourceColumn.Scale})" : "decimal(18,2)",
                "real" => "float",
                "double precision" => "float",
                "serial" => "int",
                "bigserial" => "bigint",

                // 字符串类型
                "character" or "char" => sourceColumn.MaxLength.HasValue ? 
                    $"char({sourceColumn.MaxLength})" : "char(1)",
                "character varying" or "varchar" => sourceColumn.MaxLength.HasValue ? 
                    $"nvarchar({sourceColumn.MaxLength})" : "nvarchar(max)",
                "text" => "nvarchar(max)",

                // 日期时间类型
                "date" => "date",
                "time" => "time",
                "timestamp" or "timestamp without time zone" => "datetime2",
                "timestamp with time zone" => "datetimeoffset",
                "interval" => "nvarchar(100)",

                // 布尔类型
                "boolean" => "bit",

                // 二进制类型
                "bytea" => "varbinary(max)",

                // 网络地址类型
                "inet" or "cidr" => "nvarchar(45)",
                "macaddr" => "nvarchar(17)",

                // JSON类型
                "json" or "jsonb" => "nvarchar(max)",

                // UUID类型
                "uuid" => "uniqueidentifier",

                // 几何类型
                "point" or "line" or "circle" or "box" => "nvarchar(max)",

                // 其他类型
                "xml" => "xml",
                "money" => "decimal(19,4)",
                _ => sourceType
            };
        }

        public override bool CanConvert(string sourceType, string targetType)
        {
            if (string.IsNullOrEmpty(sourceType))
                throw new ArgumentNullException(nameof(sourceType));

            if (string.IsNullOrEmpty(targetType))
                throw new ArgumentNullException(nameof(targetType));

            var source = sourceType.ToLower();
            var target = targetType.ToLower();

            return (source, target) switch
            {
                // 数值类型转换
                ("smallint", "smallint") => true,
                ("integer", "int") => true,
                ("bigint", "bigint") => true,
                ("decimal", "decimal") or ("numeric", "decimal") => true,
                ("real", "float") => true,
                ("double precision", "float") => true,
                ("serial", "int") => true,
                ("bigserial", "bigint") => true,

                // 字符串类型转换
                ("character", "char") or ("char", "char") => true,
                ("character varying", "nvarchar") or ("varchar", "nvarchar") => true,
                ("text", "nvarchar") => true,

                // 日期时间类型转换
                ("date", "date") => true,
                ("time", "time") => true,
                ("timestamp", "datetime2") => true,
                ("timestamp with time zone", "datetimeoffset") => true,
                ("interval", "nvarchar") => true,

                // 布尔类型转换
                ("boolean", "bit") => true,

                // 二进制类型转换
                ("bytea", "varbinary") => true,

                // 网络地址类型转换
                ("inet", "nvarchar") or ("cidr", "nvarchar") => true,
                ("macaddr", "nvarchar") => true,

                // JSON类型转换
                ("json", "nvarchar") or ("jsonb", "nvarchar") => true,

                // UUID类型转换
                ("uuid", "uniqueidentifier") => true,

                // 几何类型转换
                ("point", "nvarchar") or ("line", "nvarchar") or 
                ("circle", "nvarchar") or ("box", "nvarchar") => true,

                // 其他类型转换
                ("xml", "xml") => true,
                ("money", "decimal") => true,

                _ => false
            };
        }

        protected override Task<object?> HandleSpecialTypeAsync(object? value, string sourceType, string targetType)
        {
            if (value == null)
                return Task.FromResult<object?>(null);

            if (string.IsNullOrEmpty(sourceType))
                throw new ArgumentNullException(nameof(sourceType));

            if (string.IsNullOrEmpty(targetType))
                throw new ArgumentNullException(nameof(targetType));

            object? result = sourceType.ToLower() switch
            {
                "json" or "jsonb" => HandleJsonConversion(value),
                "point" or "line" or "circle" or "box" => HandleGeometryConversion(value),
                "inet" or "cidr" or "macaddr" => HandleNetworkAddressConversion(value),
                "interval" => HandleIntervalConversion(value),
                "boolean" => HandleBooleanConversion(value),
                "money" => HandleMoneyConversion(value),
                _ => null // 返回null表示使用基础转换
            };

            return Task.FromResult(result);
        }

        private string? HandleJsonConversion(object? value)
        {
            if (value == null)
                return null;

            try
            {
                // 如果已经是字符串，直接返回
                if (value is string strValue)
                    return strValue;

                // 如果是JsonDocument，转换为字符串
                if (value is JsonDocument jsonDoc)
                {
                    using (jsonDoc)
                    {
                        return jsonDoc.RootElement.GetRawText();
                    }
                }

                // 其他类型，尝试序列化为JSON字符串
                return JsonSerializer.Serialize(value);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON转换失败: {ex.Message}", ex);
            }
        }

        private string? HandleGeometryConversion(object? value)
        {
            if (value == null)
                return null;

            // 几何类型转换为字符串表示
            return value.ToString();
        }

        private string? HandleNetworkAddressConversion(object? value)
        {
            if (value == null)
                return null;

            // 网络地址转换为字符串
            return value.ToString();
        }

        private string? HandleIntervalConversion(object? value)
        {
            if (value == null)
                return null;

            // 时间间隔转换为字符串
            return value.ToString();
        }

        private bool HandleBooleanConversion(object? value)
        {
            if (value == null)
                return false;

            if (value is bool boolValue)
                return boolValue;

            // 尝试从字符串转换
            if (value is string strValue)
            {
                return strValue.ToLower() switch
                {
                    "true" or "t" or "yes" or "y" or "1" => true,
                    "false" or "f" or "no" or "n" or "0" => false,
                    _ => throw new InvalidCastException($"无法将值 '{strValue}' 转换为布尔类型")
                };
            }

            // 尝试从数字转换
            if (value is int intValue)
                return intValue != 0;

            throw new InvalidCastException($"无法将类型 '{value.GetType()}' 转换为布尔类型");
        }

        private decimal HandleMoneyConversion(object? value)
        {
            if (value == null)
                return 0m;

            if (value is decimal decimalValue)
                return decimalValue;

            if (value is string strValue && decimal.TryParse(strValue, out var result))
                return result;

            throw new InvalidCastException($"无法将类型 '{value.GetType()}' 转换为金额类型");
        }
    }
}
