using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.PostgreSQL.TypeConverters;

namespace TryCode.DatabaseMigration.PostgreSQL
{
    /// <summary>
    /// PostgreSQL数据库提供程序工厂
    /// </summary>
    public class PostgresProviderFactory : IDatabaseProviderFactory
    {
        private readonly string _connectionString;
        private readonly ILogger _logger;

        public PostgresProviderFactory(string connectionString, ILogger logger)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public IDataReader CreateReader()
        {
            return new PostgresDataReader(_connectionString);
        }

        public IDataWriter CreateWriter()
        {
            // 由于 PostgresDataWriter 需要 ILogger<PostgresDataWriter>，我们需要使用适配器模式
            var writerLogger = new PostgresDataWriterLogger(_logger);
            return new PostgresDataWriter(_connectionString, writerLogger);
        }

        public ITypeConverter CreateTypeConverter()
        {
            return new PostgresTypeConverter();
        }
        
        /// <summary>
        /// PostgreSQL数据写入器日志适配器
        /// </summary>
        private class PostgresDataWriterLogger : ILogger<PostgresDataWriter>
        {
            private readonly ILogger _innerLogger;
            
            public PostgresDataWriterLogger(ILogger innerLogger)
            {
                _innerLogger = innerLogger;
            }
            
            public IDisposable? BeginScope<TState>(TState state) where TState : notnull => _innerLogger.BeginScope(state);
            
            public bool IsEnabled(LogLevel logLevel) => _innerLogger.IsEnabled(logLevel);
            
            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                _innerLogger.Log(logLevel, eventId, state, exception, formatter);
            }
        }
    }
}
