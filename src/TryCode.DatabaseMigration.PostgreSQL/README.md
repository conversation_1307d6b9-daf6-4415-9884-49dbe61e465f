# PostgreSQL 数据库迁移工具

本模块提供了与 PostgreSQL 数据库交互的功能，用于读取表结构、数据和元数据。

## 主要功能

- 读取表结构信息
- 支持分页读取表数据
- 提取分区表信息
- 获取表统计信息
- 支持自动重试和错误处理

## 使用示例

### 基本用法

```csharp
// 创建 PostgreSQL 数据读取器
var connectionString = "Host=localhost;Database=mydb;Username=postgres;Password=******;";
var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<PostgresDataReader>();
var reader = new PostgresDataReader(connectionString, logger);

// 获取表结构
var tableSchema = await reader.GetTableSchemaAsync("my_table");

// 获取表行数
var rowCount = await reader.GetTableRowCountAsync("my_table");

// 分页读取数据
var pageSize = 1000;
long offset = 0;
var data = await reader.ReadTableDataAsync("my_table", pageSize, offset);
```

### 获取详细信息

```csharp
// 获取表统计信息
var tableStats = await reader.GetTableStatisticsAsync("my_table", logger);
Console.WriteLine($"表大小: {tableStats.TotalSize}, 行数: {tableStats.RowCount}");

// 获取数据库信息
var dbInfo = await reader.GetDatabaseInfoAsync(logger);
Console.WriteLine($"PostgreSQL版本: {dbInfo.ServerVersion}");
Console.WriteLine($"数据库: {dbInfo.DatabaseName}, 大小: {dbInfo.DatabaseSize}");
```

## 注意事项

1. 使用时请确保提供的连接字符串有足够的权限读取表结构和数据
2. 对于大表，建议使用合理的分页大小以避免内存压力
3. 对于分区表，会自动获取分区键和分区定义信息
4. 所有操作都支持取消令牌 (CancellationToken) 以便于长时间运行操作的取消

## 依赖项

- Npgsql 6.0.0+
- Dapper 2.0.0+
- Microsoft.Extensions.Logging

## 错误处理

默认配置下，所有数据库操作会在失败时自动重试3次，每次延迟递增。可以通过构造函数参数自定义重试次数和延迟时间。
