using System;
using System.Collections.Generic;
using TryCode.DatabaseMigration.Core.Interfaces;

namespace TryCode.DatabaseMigration.ProgressTracker
{
    /// <summary>
    /// 迁移进度跟踪器
    /// </summary>
    public class MigrationProgress : IMigrationProgress
    {
        private readonly Dictionary<string, bool> _completedTables = new();
        private readonly object _lock = new();

        public int TotalTables { get; private set; }
        public int CompletedTables => _completedTables.Count;
        public string CurrentTable { get; private set; }
        public long CurrentTableTotalRows { get; private set; }
        public long CurrentTableProcessedRows { get; private set; }
        public DateTime StartTime { get; }
        public double EstimatedRemainingSeconds { get; private set; }

        public event EventHandler<MigrationProgressEventArgs> ProgressChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="totalTables">总表数</param>
        public MigrationProgress(int totalTables)
        {
            TotalTables = totalTables;
            StartTime = DateTime.Now;
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        public void UpdateProgress(string tableName, long totalRows, long processedRows)
        {
            lock (_lock)
            {
                CurrentTable = tableName;
                CurrentTableTotalRows = totalRows;
                CurrentTableProcessedRows = processedRows;

                // 计算预计剩余时间
                var elapsedSeconds = (DateTime.Now - StartTime).TotalSeconds;
                var overallProgress = (CompletedTables + (processedRows / (double)totalRows)) / TotalTables;
                if (overallProgress > 0)
                {
                    EstimatedRemainingSeconds = (elapsedSeconds / overallProgress) * (1 - overallProgress);
                }

                OnProgressChanged();
            }
        }

        /// <summary>
        /// 完成表迁移
        /// </summary>
        public void CompleteTable(string tableName)
        {
            lock (_lock)
            {
                if (!_completedTables.ContainsKey(tableName))
                {
                    _completedTables[tableName] = true;
                    OnProgressChanged();
                }
            }
        }

        private void OnProgressChanged()
        {
            var handler = ProgressChanged;
            if (handler != null)
            {
                var args = new MigrationProgressEventArgs
                {
                    TotalTables = TotalTables,
                    CompletedTables = CompletedTables,
                    CurrentTable = CurrentTable,
                    CurrentTableTotalRows = CurrentTableTotalRows,
                    CurrentTableProcessedRows = CurrentTableProcessedRows,
                    EstimatedRemainingSeconds = EstimatedRemainingSeconds
                };
                handler(this, args);
            }
        }
    }
}
