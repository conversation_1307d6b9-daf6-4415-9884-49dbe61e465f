# TryCode.DatabaseMigration

数据库迁移工具，用于解决不同数据库之间的数据迁移问题，特别是处理特殊数据类型的转换。

## 功能特点

- 支持多种数据库之间的数据迁移
- 智能处理表依赖关系，自动确定迁移顺序
- 支持自定义数据类型转换
- 实时进度跟踪
- 断点续传支持
- 分批处理大数据量

## 项目结构

```
TryCode.DatabaseMigration/
├── TryCode.DatabaseMigration.CLI/          # 命令行工具
├── TryCode.DatabaseMigration.Core/         # 核心接口和服务
├── TryCode.DatabaseMigration.PostgreSQL/   # PostgreSQL实现
├── TryCode.DatabaseMigration.SqlServer/    # SQL Server实现
├── TryCode.DatabaseMigration.DependencyResolver/  # 依赖关系解析
├── TryCode.DatabaseMigration.ProgressTracker/     # 进度跟踪
└── TryCode.DatabaseMigration.Tests/        # 单元测试
```

## 使用方法

1. 配置连接字符串：

```json
{
  "ConnectionStrings": {
    "Source": "Host=localhost;Database=source_db;Username=postgres;Password=password",
    "Target": "Server=localhost;Database=target_db;User Id=sa;Password=password"
  }
}
```

2. 运行迁移：

```bash
dotnet run --project TryCode.DatabaseMigration.CLI
```

## 自定义类型转换

要添加自定义类型转换，继承 `TypeConverterBase` 类并实现必要的方法：

```csharp
public class CustomTypeConverter : TypeConverterBase
{
    protected override async Task<object> HandleSpecialTypeAsync(object value, string sourceType, string targetType)
    {
        if (sourceType == "custom_type")
        {
            // 实现自定义转换逻辑
            return ConvertCustomType(value);
        }
        return await base.HandleSpecialTypeAsync(value, sourceType, targetType);
    }
}
```

## 添加新的数据库支持

1. 创建新的项目（例如：`TryCode.DatabaseMigration.MySQL`）
2. 实现以下接口：
   - `IDatabaseProviderFactory`
   - `IDataReader`
   - `IDataWriter`
   - `ITypeConverter`

## 开发环境要求

- .NET 8.0 SDK
- Visual Studio 2022 或 VS Code
- 支持的数据库：
  - PostgreSQL 12+
  - SQL Server 2019+
  - MySQL 8.0+（计划中）

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m '添加一些特性'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License
