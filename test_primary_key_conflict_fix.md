# 主键冲突问题根本修复说明

## 问题根本原因

### 真正的问题：ORDER BY 不稳定导致重复数据读取

经过深入分析，发现主键冲突的根本原因不是断点恢复逻辑问题，而是**PostgreSQL数据读取时ORDER BY不稳定**导致的：

1. **不稳定的排序**: 原代码使用 `ORDER BY 1`（按第一列排序）
2. **重复读取**: 当第一列有重复值时，相同值的行在不同查询中可能返回不同顺序
3. **OFFSET问题**: PostgreSQL的OFFSET分页在没有稳定排序时可能跳过或重复行
4. **主键冲突**: 同一条记录被读取多次，导致尝试插入重复的主键值

### 具体场景
```sql
-- 第一次查询: OFFSET 0, LIMIT 1000
SELECT * FROM "Ledger_LedgerDepartments" ORDER BY 1 LIMIT 1000 OFFSET 0
-- 返回行 1-1000

-- 第二次查询: OFFSET 1000, LIMIT 1000
SELECT * FROM "Ledger_LedgerDepartments" ORDER BY 1 LIMIT 1000 OFFSET 1000
-- 由于ORDER BY 1不稳定，可能返回部分重复的行
```

## 修复方案

### 1. PostgreSQL数据读取器修复
- **文件**: `src/TryCode.DatabaseMigration.PostgreSQL/PostgresDataReader.cs`
- **修改**: 实现稳定的ORDER BY逻辑
- **原理**: 优先使用主键排序，其次使用唯一索引，确保分页结果稳定

### 2. 稳定排序策略
1. **主键排序**: 优先使用表的主键列进行排序
2. **唯一索引排序**: 如果没有主键，使用第一个唯一索引
3. **警告机制**: 如果既没有主键也没有唯一索引，记录警告并使用默认排序

### 3. 错误处理改进
- **文件**: `src/TryCode.DatabaseMigration.Parallel/ParallelMigrationExecutor.cs`
- **修改**: 将主键冲突视为严重错误，提供明确的错误信息
- **原理**: 主键冲突表示数据读取逻辑有问题，应该停止迁移并修复

## 技术实现

### 获取主键列的SQL
```sql
SELECT a.attname as column_name
FROM pg_index i
JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
WHERE i.indrelid = 'schema.tablename'::regclass
AND i.indisprimary = true
ORDER BY array_position(i.indkey, a.attnum)
```

### 获取唯一索引列的SQL
```sql
SELECT a.attname as column_name
FROM pg_index i
JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
WHERE i.indrelid = 'schema.tablename'::regclass
AND i.indisunique = true
AND i.indisprimary = false
ORDER BY i.indexrelid, array_position(i.indkey, a.attnum)
```

## 预期效果

### 修复前
```
ERROR: Duplicate entry '3a11ce00-2ec2-fcfe-fe70-9f07715b96c5-3a0ef8dc-68cc-6947-523b-b7f1e17f0e2f' for key 'PRIMARY'
-> 主键冲突导致迁移失败
-> 无法完成数据同步
```

### 修复后
```
DEBUG: 表 Ledger_LedgerDepartments 使用主键列进行排序: "LedgerId", "DepartmentId"
-> 稳定的排序确保数据不重复读取
-> 迁移正常完成
```

## 测试建议

### 1. 排序稳定性测试
- 验证有主键的表使用主键排序
- 验证有唯一索引的表使用唯一索引排序
- 验证无主键无唯一索引的表会记录警告

### 2. 分页一致性测试
- 多次执行相同的分页查询，验证结果一致
- 测试大表的分页查询稳定性
- 验证OFFSET分页不会跳过或重复数据

### 3. 断点恢复测试
- 中断迁移过程后重新启动
- 验证从断点处继续迁移不会出现主键冲突
- 验证数据完整性和一致性

### 4. 分区表测试
- 测试分区表的稳定排序
- 验证分区迁移的数据一致性
- 确保分区表也使用正确的ORDER BY

## 兼容性说明

### PostgreSQL版本兼容性
- 主键和唯一索引查询SQL兼容PostgreSQL 9.1+
- 不影响现有的数据读取功能
- 向后兼容所有现有配置

### 性能影响
- 主键排序通常比默认排序更高效
- 唯一索引排序性能良好
- 避免了重复数据读取，实际上提高了整体性能

## 监控建议

### 日志监控
- 关注排序列选择的调试日志
- 监控是否有表使用默认排序（警告日志）
- 确保主键冲突错误不再出现

### 数据验证
- 迁移完成后验证数据完整性
- 对比源表和目标表的行数
- 检查关键业务数据的一致性
- 验证复合主键数据的正确性
